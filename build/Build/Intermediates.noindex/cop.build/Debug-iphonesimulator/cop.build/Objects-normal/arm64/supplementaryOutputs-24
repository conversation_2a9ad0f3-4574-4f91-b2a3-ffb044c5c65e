"/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/MediaContentView.swift":
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaContentView.dia"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaContentView.o"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaContentView.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaContentView.swiftdeps"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaContentView.d"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/ContentBlockerManager.swift":
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerManager.o"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerManager.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerManager.swiftdeps"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerManager.dia"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerManager.d"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/ImportProgressView.swift":
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImportProgressView.swiftdeps"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImportProgressView.dia"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImportProgressView.o"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImportProgressView.swiftconstvalues"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImportProgressView.d"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/NewWebBrowserView.swift":
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserView.dia"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserView.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserView.swiftdeps"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserView.o"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserView.d"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/Utils/BrowserManager.swift":
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserManager.swiftdeps"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserManager.dia"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserManager.swiftconstvalues"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserManager.o"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserManager.d"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/ServiceIntegrationConfig.swift":
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceIntegrationConfig.swiftconstvalues"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceIntegrationConfig.d"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceIntegrationConfig.o"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceIntegrationConfig.swiftdeps"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceIntegrationConfig.dia"
