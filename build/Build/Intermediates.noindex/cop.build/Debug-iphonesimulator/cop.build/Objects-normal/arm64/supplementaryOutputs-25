"/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/NewWebBrowserViewModel.swift":
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserViewModel.dia"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserViewModel.d"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserViewModel.o"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserViewModel.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserViewModel.swiftdeps"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/AppComponents.swift":
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppComponents.d"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppComponents.o"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppComponents.dia"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppComponents.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppComponents.swiftdeps"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/MonitoringDashboardView.swift":
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MonitoringDashboardView.o"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MonitoringDashboardView.d"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MonitoringDashboardView.swiftconstvalues"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MonitoringDashboardView.dia"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MonitoringDashboardView.swiftdeps"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/Utils/LogManager.swift":
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/LogManager.swiftdeps"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/LogManager.d"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/LogManager.o"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/LogManager.swiftconstvalues"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/LogManager.dia"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/MediaLibraryViewModel.swift":
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLibraryViewModel.swiftconstvalues"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLibraryViewModel.o"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLibraryViewModel.swiftdeps"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLibraryViewModel.d"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLibraryViewModel.dia"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/DataManagementView.swift":
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DataManagementView.swiftdeps"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DataManagementView.o"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DataManagementView.d"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DataManagementView.swiftconstvalues"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DataManagementView.dia"
