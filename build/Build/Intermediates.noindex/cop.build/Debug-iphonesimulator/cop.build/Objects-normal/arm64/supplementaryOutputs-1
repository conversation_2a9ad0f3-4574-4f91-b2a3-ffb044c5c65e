"/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/OptimizedBrowserSettingsView.swift":
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/OptimizedBrowserSettingsView.dia"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/OptimizedBrowserSettingsView.d"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/OptimizedBrowserSettingsView.swiftconstvalues"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/OptimizedBrowserSettingsView.o"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/OptimizedBrowserSettingsView.swiftdeps"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/StatisticsView.swift":
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/StatisticsView.swiftdeps"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/StatisticsView.dia"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/StatisticsView.o"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/StatisticsView.swiftconstvalues"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/StatisticsView.d"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/ServiceManager.swift":
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceManager.swiftconstvalues"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceManager.o"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceManager.swiftdeps"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceManager.dia"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceManager.d"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/WebsiteDataDetailView.swift":
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/WebsiteDataDetailView.d"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/WebsiteDataDetailView.swiftconstvalues"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/WebsiteDataDetailView.dia"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/WebsiteDataDetailView.swiftdeps"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/WebsiteDataDetailView.o"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/ImageContentView.swift":
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImageContentView.swiftconstvalues"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImageContentView.o"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImageContentView.d"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImageContentView.swiftdeps"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImageContentView.dia"
"/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/MediaViewerState.swift":
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaViewerState.dia"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaViewerState.swiftconstvalues"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaViewerState.d"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaViewerState.o"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaViewerState.swiftdeps"
