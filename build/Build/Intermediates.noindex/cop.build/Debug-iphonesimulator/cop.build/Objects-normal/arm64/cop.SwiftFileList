/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/WebsiteDataDetailView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/ServiceManager.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/StatisticsView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/ImageContentView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/MediaViewerState.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/OptimizedBrowserSettingsView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/ContentView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/SettingsView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/MonitoringDashboardView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/ImportProgressView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/SearchEngineSelectionView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/SystemErrorHandler.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Utils/UnifiedMemoryManager.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/DataManagementView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/TabSwitcherView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/NewWebBrowserViewModel.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/FolderViewModel.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/NewBrowserHistoryView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/VideoPlayerService.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/DocumentPickerView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/SecurityStatisticsView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/MediaLoadingAndErrorViews.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/FullScreenMediaViewer.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/ModernFolderView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/BrowserUIComponents.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/MediaLibraryViewModel.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Models/MediaFile.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/ContentBlockerManager.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Utils/BrowserManager.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/AppComponents.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/ContentBlockerSettingsView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/ServiceIntegrationConfig.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/PermissionManagementView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/VideoPlayerView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/AdvancedStatsViewModel.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Models/SecurityModels.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/MainView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/FullScreenThumbnailBar.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/MediaDetailView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/UserAgentSelectionView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/FullScreenGestureOverlay.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/AdvancedStatsView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/NewWebBrowserView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/SecurityService.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/ServiceStatusView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Utils/MediaFormatDetector.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/copApp.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/DesignSystem/AppDesignSystem.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/MediaComponents.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/MediaContentView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/AdBlockTestView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/EasyListConverter.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/SecuritySettingsView.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/MediaImportService.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/FullScreenNavigationBar.swift
/Users/<USER>/Documents/Work/Xcode/cop/cop/Utils/LogManager.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/DerivedSources/GeneratedAssetSymbols.swift
