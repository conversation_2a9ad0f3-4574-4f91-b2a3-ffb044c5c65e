{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "COREML_CODEGEN_LANGUAGE": "Swift", "COREML_COMPILER_CONTAINER": "swift-package", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "PublicSuffixList", "GENERATE_EMBED_IN_CODE_ACCESSORS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "GENERATE_RESOURCE_ACCESSORS": "YES", "MARKETING_VERSION": "1.0", "MODULEMAP_FILE_CONTENTS": "module PublicSuffixList {\nheader \"PublicSuffixList-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/PublicSuffixList.modulemap", "PACKAGE_RESOURCE_BUNDLE_NAME": "swift-psl_PublicSuffixList", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-psl.PublicSuffixList", "PRODUCT_MODULE_NAME": "PublicSuffixList", "PRODUCT_NAME": "PublicSuffixList", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "PublicSuffixList-Swift.h", "SWIFT_VERSION": "5", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "PublicSuffixList"}, "guid": "PACKAGE-TARGET:PublicSuffixList-1ABD691032485E80-dynamic::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {"EMBED_PACKAGE_RESOURCE_BUNDLE_NAMES": ["$(inherited)", "swift-psl_PublicSuffixList"], "FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "LD_RUNPATH_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/PublicSuffixList.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "COREML_CODEGEN_LANGUAGE": "Swift", "COREML_COMPILER_CONTAINER": "swift-package", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "PublicSuffixList", "GENERATE_EMBED_IN_CODE_ACCESSORS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "GENERATE_RESOURCE_ACCESSORS": "YES", "MARKETING_VERSION": "1.0", "MODULEMAP_FILE_CONTENTS": "module PublicSuffixList {\nheader \"PublicSuffixList-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/PublicSuffixList.modulemap", "PACKAGE_RESOURCE_BUNDLE_NAME": "swift-psl_PublicSuffixList", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-psl.PublicSuffixList", "PRODUCT_MODULE_NAME": "PublicSuffixList", "PRODUCT_NAME": "PublicSuffixList", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "PublicSuffixList-Swift.h", "SWIFT_VERSION": "5", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "PublicSuffixList"}, "guid": "PACKAGE-TARGET:PublicSuffixList-1ABD691032485E80-dynamic::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {"EMBED_PACKAGE_RESOURCE_BUNDLE_NAMES": ["$(inherited)", "swift-psl_PublicSuffixList"], "FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/PublicSuffixList.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_10::REF_0", "guid": "PACKAGE-TARGET:PublicSuffixList-1ABD691032485E80-dynamic::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_10::REF_1", "guid": "PACKAGE-TARGET:PublicSuffixList-1ABD691032485E80-dynamic::BUILDPHASE_0::1", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_10::REF_2", "guid": "PACKAGE-TARGET:PublicSuffixList-1ABD691032485E80-dynamic::BUILDPHASE_0::2", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-TARGET:PublicSuffixList-1ABD691032485E80-dynamic::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}], "buildRules": [], "customTasks": [], "dependencies": [], "guid": "PACKAGE-TARGET:PublicSuffixList-1ABD691032485E80-dynamic", "name": "PublicSuffixList", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:PublicSuffixList-1ABD691032485E80-dynamic", "name": "PublicSuffixList.framework", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.framework", "type": "standard"}