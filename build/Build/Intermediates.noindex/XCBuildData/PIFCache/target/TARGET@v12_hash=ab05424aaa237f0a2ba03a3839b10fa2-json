{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTED_PLATFORMS": ["$(HOST_PLATFORM)"]}, "guid": "PACKAGE-TARGET:GenerateManual::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTED_PLATFORMS": ["$(HOST_PLATFORM)"]}, "guid": "PACKAGE-TARGET:GenerateManual::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "buildPhases": [], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-TARGET:ArgumentParserToolInfo", "platformFilters": []}, {"guid": "PACKAGE-TARGET:ArgumentParser", "platformFilters": []}, {"guid": "PACKAGE-PRODUCT:generate-manual", "platformFilters": []}], "guid": "PACKAGE-TARGET:GenerateManual", "name": "GenerateManual", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:GenerateManual", "name": "GenerateManual", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.tool", "type": "standard"}