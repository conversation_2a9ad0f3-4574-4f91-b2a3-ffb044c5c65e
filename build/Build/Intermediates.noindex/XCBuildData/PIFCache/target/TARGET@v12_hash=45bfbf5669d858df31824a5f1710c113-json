{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"SDKROOT": "auto", "SDK_VARIANT": "auto", "USES_SWIFTPM_UNSAFE_FLAGS": "NO"}, "guid": "PACKAGE-PRODUCT:ArgumentParser::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"SDKROOT": "auto", "SDK_VARIANT": "auto", "USES_SWIFTPM_UNSAFE_FLAGS": "NO"}, "guid": "PACKAGE-PRODUCT:ArgumentParser::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "customTasks": [], "dependencies": [{"guid": "PACKAGE-TARGET:ArgumentParser", "platformFilters": []}, {"guid": "PACKAGE-TARGET:ArgumentParserToolInfo", "platformFilters": []}], "dynamicTargetVariantGuid": "PACKAGE-PRODUCT:ArgumentParser-39B7DAEE4DD359BE-dynamic", "frameworksBuildPhase": {"buildFiles": [{"guid": "PACKAGE-PRODUCT:ArgumentParser::BUILDPHASE_0::0", "platformFilters": [], "targetReference": "PACKAGE-TARGET:ArgumentParser"}, {"guid": "PACKAGE-PRODUCT:ArgumentParser::BUILDPHASE_0::1", "platformFilters": [], "targetReference": "PACKAGE-TARGET:ArgumentParserToolInfo"}], "guid": "PACKAGE-PRODUCT:ArgumentParser::BUILDPHASE_0", "type": "com.apple.buildphase.frameworks"}, "guid": "PACKAGE-PRODUCT:ArgumentParser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "packageProduct"}