{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "ArgumentParserToolInfo", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "MODULEMAP_FILE_CONTENTS": "module ArgumentParserToolInfo {\nheader \"ArgumentParserToolInfo-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/ArgumentParserToolInfo.modulemap", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-argument-parser.ArgumentParserToolInfo", "PRODUCT_MODULE_NAME": "ArgumentParserToolInfo", "PRODUCT_NAME": "ArgumentParserToolInfo", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "ArgumentParserToolInfo-Swift.h", "SWIFT_VERSION": "5", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "ArgumentParserToolInfo"}, "guid": "PACKAGE-TARGET:ArgumentParserToolInfo-4859AC8F46C95AF0-dynamic::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "LD_RUNPATH_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/ArgumentParserToolInfo.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "ArgumentParserToolInfo", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "MODULEMAP_FILE_CONTENTS": "module ArgumentParserToolInfo {\nheader \"ArgumentParserToolInfo-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/ArgumentParserToolInfo.modulemap", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-argument-parser.ArgumentParserToolInfo", "PRODUCT_MODULE_NAME": "ArgumentParserToolInfo", "PRODUCT_NAME": "ArgumentParserToolInfo", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "ArgumentParserToolInfo-Swift.h", "SWIFT_VERSION": "5", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "ArgumentParserToolInfo"}, "guid": "PACKAGE-TARGET:ArgumentParserToolInfo-4859AC8F46C95AF0-dynamic::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/ArgumentParserToolInfo.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_6::REF_0", "guid": "PACKAGE-TARGET:ArgumentParserToolInfo-4859AC8F46C95AF0-dynamic::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-TARGET:ArgumentParserToolInfo-4859AC8F46C95AF0-dynamic::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}], "buildRules": [], "customTasks": [], "dependencies": [], "guid": "PACKAGE-TARGET:ArgumentParserToolInfo-4859AC8F46C95AF0-dynamic", "name": "ArgumentParserToolInfo", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:ArgumentParserToolInfo-4859AC8F46C95AF0-dynamic", "name": "ArgumentParserToolInfo.framework", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.framework", "type": "standard"}