{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "EXECUTABLE_NAME": "ContentBlockerConverter_-48525B91CD55894_PackageProduct", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "safariconverterlib.ContentBlockerConverter", "PRODUCT_MODULE_NAME": "ContentBlockerConverter", "PRODUCT_NAME": "ContentBlockerConverter_-48525B91CD55894_PackageProduct", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SWIFT_PACKAGE_NAME": "safariconverterlib", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "ContentBlockerConverter product", "USES_SWIFTPM_UNSAFE_FLAGS": "NO"}, "guid": "PACKAGE-PRODUCT:ContentBlockerConverter--48525B91CD55894-dynamic::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "EXECUTABLE_NAME": "ContentBlockerConverter_-48525B91CD55894_PackageProduct", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "safariconverterlib.ContentBlockerConverter", "PRODUCT_MODULE_NAME": "ContentBlockerConverter", "PRODUCT_NAME": "ContentBlockerConverter_-48525B91CD55894_PackageProduct", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SWIFT_PACKAGE_NAME": "safariconverterlib", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "ContentBlockerConverter product", "USES_SWIFTPM_UNSAFE_FLAGS": "NO"}, "guid": "PACKAGE-PRODUCT:ContentBlockerConverter--48525B91CD55894-dynamic::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"guid": "PACKAGE-PRODUCT:ContentBlockerConverter--48525B91CD55894-dynamic::BUILDPHASE_0::0", "platformFilters": [], "targetReference": "PACKAGE-TARGET:ContentBlockerConverter"}, {"guid": "PACKAGE-PRODUCT:ContentBlockerConverter--48525B91CD55894-dynamic::BUILDPHASE_0::1", "platformFilters": [], "targetReference": "PACKAGE-TARGET:FilterEngine"}, {"guid": "PACKAGE-PRODUCT:ContentBlockerConverter--48525B91CD55894-dynamic::BUILDPHASE_0::2", "platformFilters": [], "targetReference": "PACKAGE-PRODUCT:Punycode"}, {"guid": "PACKAGE-PRODUCT:ContentBlockerConverter--48525B91CD55894-dynamic::BUILDPHASE_0::3", "platformFilters": [], "targetReference": "PACKAGE-PRODUCT:PublicSuffixList"}], "guid": "PACKAGE-PRODUCT:ContentBlockerConverter--48525B91CD55894-dynamic::BUILDPHASE_0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "PACKAGE-PRODUCT:ContentBlockerConverter--48525B91CD55894-dynamic::BUILDPHASE_1", "type": "com.apple.buildphase.sources"}], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-TARGET:ContentBlockerConverter", "platformFilters": []}, {"guid": "PACKAGE-TARGET:FilterEngine", "platformFilters": []}, {"guid": "PACKAGE-PRODUCT:Punycode", "platformFilters": []}, {"guid": "PACKAGE-PRODUCT:PublicSuffixList", "platformFilters": []}], "guid": "PACKAGE-PRODUCT:ContentBlockerConverter--48525B91CD55894-dynamic", "name": "ContentBlockerConverter", "productReference": {"guid": "PRODUCTREF-PACKAGE-PRODUCT:ContentBlockerConverter--48525B91CD55894-dynamic", "name": "ContentBlockerConverter_-48525B91CD55894_PackageProduct.framework", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.framework", "type": "standard"}