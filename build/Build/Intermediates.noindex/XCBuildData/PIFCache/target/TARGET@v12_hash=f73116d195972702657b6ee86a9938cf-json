{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"COREML_CODEGEN_LANGUAGE": "None", "COREML_COMPILER_CONTAINER": "swift-package", "EXECUTABLE_NAME": "", "GENERATE_INFOPLIST_FILE": "YES", "PACKAGE_RESOURCE_TARGET_KIND": "resource", "PRODUCT_BUNDLE_IDENTIFIER": "swift-psl.PublicSuffixList.resources", "PRODUCT_MODULE_NAME": "swift-psl_PublicSuffixList", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "TARGET_NAME": "swift-psl_PublicSuffixList"}, "guid": "PACKAGE-RESOURCE:PublicSuffixList::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"COREML_CODEGEN_LANGUAGE": "None", "COREML_COMPILER_CONTAINER": "swift-package", "EXECUTABLE_NAME": "", "GENERATE_INFOPLIST_FILE": "YES", "PACKAGE_RESOURCE_TARGET_KIND": "resource", "PRODUCT_BUNDLE_IDENTIFIER": "swift-psl.PublicSuffixList.resources", "PRODUCT_MODULE_NAME": "swift-psl_PublicSuffixList", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "TARGET_NAME": "swift-psl_PublicSuffixList"}, "guid": "PACKAGE-RESOURCE:PublicSuffixList::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_5", "guid": "PACKAGE-RESOURCE:PublicSuffixList::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false", "resourceRule": "process"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_6", "guid": "PACKAGE-RESOURCE:PublicSuffixList::BUILDPHASE_0::1", "platformFilters": [], "removeHeadersOnCopy": "false", "resourceRule": "process"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_7", "guid": "PACKAGE-RESOURCE:PublicSuffixList::BUILDPHASE_0::2", "platformFilters": [], "removeHeadersOnCopy": "false", "resourceRule": "process"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_8", "guid": "PACKAGE-RESOURCE:PublicSuffixList::BUILDPHASE_0::3", "platformFilters": [], "removeHeadersOnCopy": "false", "resourceRule": "process"}], "guid": "PACKAGE-RESOURCE:PublicSuffixList::BUILDPHASE_0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "customTasks": [], "dependencies": [], "guid": "PACKAGE-RESOURCE:PublicSuffixList", "name": "swift-psl_PublicSuffixList", "productReference": {"guid": "PRODUCTREF-PACKAGE-RESOURCE:PublicSuffixList", "name": "swift-psl_PublicSuffixList", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.bundle", "type": "standard"}