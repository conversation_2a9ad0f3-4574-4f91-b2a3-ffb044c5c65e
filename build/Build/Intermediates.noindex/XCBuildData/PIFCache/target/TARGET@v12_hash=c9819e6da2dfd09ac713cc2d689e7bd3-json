{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "ResourceBuilder.o", "GENERATE_EMBED_IN_CODE_ACCESSORS": "YES", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "module ResourceBuilder {\nheader \"ResourceBuilder-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/ResourceBuilder.modulemap", "OTHER_SWIFT_FLAGS": ["$(inherited)", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "ResourceBuilder_main"], "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-psl.ResourceBuilder", "PRODUCT_MODULE_NAME": "ResourceBuilder", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_INSTALL_MODULE": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "ResourceBuilder-Swift.h", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "ResourceBuilder", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/ExecutableModules", "TARGET_NAME": "ResourceBuilder-352920573610061E-testable"}, "guid": "PACKAGE-TARGET:ResourceBuilder-352920573610061E-testable::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "LD_RUNPATH_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/ResourceBuilder.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Debug"}, {"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "ResourceBuilder.o", "GENERATE_EMBED_IN_CODE_ACCESSORS": "YES", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "module ResourceBuilder {\nheader \"ResourceBuilder-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/ResourceBuilder.modulemap", "OTHER_SWIFT_FLAGS": ["$(inherited)", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "ResourceBuilder_main"], "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-psl.ResourceBuilder", "PRODUCT_MODULE_NAME": "ResourceBuilder", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_INSTALL_MODULE": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "ResourceBuilder-Swift.h", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "ResourceBuilder", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/ExecutableModules", "TARGET_NAME": "ResourceBuilder-352920573610061E-testable"}, "guid": "PACKAGE-TARGET:ResourceBuilder-352920573610061E-testable::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/ResourceBuilder.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_11::REF_0", "guid": "PACKAGE-TARGET:ResourceBuilder-352920573610061E-testable::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-TARGET:ResourceBuilder-352920573610061E-testable::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-TARGET:PublicSuffixList", "platformFilters": []}, {"guid": "PACKAGE-PRODUCT:Punycode", "platformFilters": []}], "guid": "PACKAGE-TARGET:ResourceBuilder-352920573610061E-testable", "name": "ResourceBuilder", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:ResourceBuilder-352920573610061E-testable", "name": "ResourceBuilder.o", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.objfile", "type": "standard"}