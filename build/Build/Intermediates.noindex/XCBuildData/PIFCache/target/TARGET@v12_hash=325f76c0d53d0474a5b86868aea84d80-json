{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "FileLockTester.o", "GENERATE_EMBED_IN_CODE_ACCESSORS": "YES", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "module FileLockTester {\nheader \"FileLockTester-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/FileLockTester.modulemap", "OTHER_SWIFT_FLAGS": ["$(inherited)", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "FileLockTester_main"], "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "safariconverterlib.FileLockTester", "PRODUCT_MODULE_NAME": "FileLockTester", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_INSTALL_MODULE": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "FileLockTester-Swift.h", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "FileLockTester", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/ExecutableModules", "TARGET_NAME": "FileLockTester-117C868607C6928E-testable"}, "guid": "PACKAGE-TARGET:FileLockTester-117C868607C6928E-testable::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "LD_RUNPATH_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/FileLockTester.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Debug"}, {"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "FileLockTester.o", "GENERATE_EMBED_IN_CODE_ACCESSORS": "YES", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "module FileLockTester {\nheader \"FileLockTester-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/FileLockTester.modulemap", "OTHER_SWIFT_FLAGS": ["$(inherited)", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "FileLockTester_main"], "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "safariconverterlib.FileLockTester", "PRODUCT_MODULE_NAME": "FileLockTester", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_INSTALL_MODULE": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "FileLockTester-Swift.h", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "FileLockTester", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/ExecutableModules", "TARGET_NAME": "FileLockTester-117C868607C6928E-testable"}, "guid": "PACKAGE-TARGET:FileLockTester-117C868607C6928E-testable::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/FileLockTester.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_7::REF_0", "guid": "PACKAGE-TARGET:FileLockTester-117C868607C6928E-testable::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-TARGET:FileLockTester-117C868607C6928E-testable::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-PRODUCT:Punycode", "platformFilters": []}, {"guid": "PACKAGE-TARGET:ContentBlockerConverter", "platformFilters": []}, {"guid": "PACKAGE-PRODUCT:PublicSuffixList", "platformFilters": []}, {"guid": "PACKAGE-TARGET:FilterEngine", "platformFilters": []}], "guid": "PACKAGE-TARGET:FileLockTester-117C868607C6928E-testable", "name": "FileLockTester", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:FileLockTester-117C868607C6928E-testable", "name": "FileLockTester.o", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.objfile", "type": "standard"}