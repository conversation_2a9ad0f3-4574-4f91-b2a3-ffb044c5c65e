{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "generate-manual.o", "GENERATE_EMBED_IN_CODE_ACCESSORS": "YES", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "module generate_manual {\nheader \"generate-manual-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/generate-manual.modulemap", "OTHER_SWIFT_FLAGS": ["$(inherited)", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "generate_manual_main"], "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-argument-parser.generate-manual", "PRODUCT_MODULE_NAME": "generate_manual", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_INSTALL_MODULE": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "generate-manual-Swift.h", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "generate-manual", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/ExecutableModules", "TARGET_NAME": "generate-manual-55E729FAD4D73105-testable"}, "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "LD_RUNPATH_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/generate-manual.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Debug"}, {"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "generate-manual.o", "GENERATE_EMBED_IN_CODE_ACCESSORS": "YES", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "module generate_manual {\nheader \"generate-manual-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/generate-manual.modulemap", "OTHER_SWIFT_FLAGS": ["$(inherited)", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "generate_manual_main"], "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-argument-parser.generate-manual", "PRODUCT_MODULE_NAME": "generate_manual", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_INSTALL_MODULE": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "generate-manual-Swift.h", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "generate-manual", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/ExecutableModules", "TARGET_NAME": "generate-manual-55E729FAD4D73105-testable"}, "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/generate-manual.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_0", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_1", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::1", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_2", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::2", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_3", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::3", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_4", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::4", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_5", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::5", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_6", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::6", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_7", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::7", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_8", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::8", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_9", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::9", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_10", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::10", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_11", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::11", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_12", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::12", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_13", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::13", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_14", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::14", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_15", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::15", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_16", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::16", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_17", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::17", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_18", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::18", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_19", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::19", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_20", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::20", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_21", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::21", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_22", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::22", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_23", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::23", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_24", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::24", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_25", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::25", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_26", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::26", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_27", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::27", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_28", "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0::28", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-TARGET:ArgumentParserToolInfo", "platformFilters": []}, {"guid": "PACKAGE-TARGET:ArgumentParser", "platformFilters": []}], "guid": "PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable", "name": "generate-manual", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:generate-manual-55E729FAD4D73105-testable", "name": "generate-manual.o", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.objfile", "type": "standard"}