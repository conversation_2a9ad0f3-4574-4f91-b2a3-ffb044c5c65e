{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "ContentBlockerConverter.o", "GENERATE_MASTER_OBJECT_FILE": "NO", "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "module ContentBlockerConverter {\nheader \"ContentBlockerConverter-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/ContentBlockerConverter.modulemap", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "safariconverterlib.ContentBlockerConverter", "PRODUCT_MODULE_NAME": "ContentBlockerConverter", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "ContentBlockerConverter-Swift.h", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "ContentBlockerConverter", "TARGET_NAME": "ContentBlockerConverter"}, "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "LD_RUNPATH_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/ContentBlockerConverter.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Debug"}, {"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "ContentBlockerConverter.o", "GENERATE_MASTER_OBJECT_FILE": "NO", "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "module ContentBlockerConverter {\nheader \"ContentBlockerConverter-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/ContentBlockerConverter.modulemap", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "safariconverterlib.ContentBlockerConverter", "PRODUCT_MODULE_NAME": "ContentBlockerConverter", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "ContentBlockerConverter-Swift.h", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "ContentBlockerConverter", "TARGET_NAME": "ContentBlockerConverter"}, "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/ContentBlockerConverter.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_0", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_1", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::1", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_2", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::2", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_3", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::3", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_4", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::4", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_5", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::5", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_6", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::6", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_7", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::7", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_8", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::8", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_9", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::9", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_10", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::10", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_11", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::11", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_12", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::12", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_13", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::13", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_14", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::14", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_15", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::15", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_16", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::16", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_17", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::17", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_18", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::18", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_19", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::19", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_20", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::20", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_21", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::21", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_22", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::22", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_23", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::23", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_24", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::24", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_25", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::25", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_26", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::26", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_27", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::27", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_28", "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0::28", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-TARGET:ContentBlockerConverter::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-PRODUCT:Punycode", "platformFilters": []}], "dynamicTargetVariantGuid": "PACKAGE-TARGET:ContentBlockerConverter--48525B91CD55894-dynamic", "guid": "PACKAGE-TARGET:ContentBlockerConverter", "name": "ContentBlockerConverter", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:ContentBlockerConverter", "name": "ContentBlockerConverter.o", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.objfile", "type": "standard"}