{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "ArgumentParserToolInfo.o", "GENERATE_MASTER_OBJECT_FILE": "NO", "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "module ArgumentParserToolInfo {\nheader \"ArgumentParserToolInfo-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/ArgumentParserToolInfo.modulemap", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-argument-parser.ArgumentParserToolInfo", "PRODUCT_MODULE_NAME": "ArgumentParserToolInfo", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "ArgumentParserToolInfo-Swift.h", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "ArgumentParserToolInfo", "TARGET_NAME": "ArgumentParserToolInfo"}, "guid": "PACKAGE-TARGET:ArgumentParserToolInfo::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "LD_RUNPATH_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/ArgumentParserToolInfo.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Debug"}, {"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "ArgumentParserToolInfo.o", "GENERATE_MASTER_OBJECT_FILE": "NO", "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "module ArgumentParserToolInfo {\nheader \"ArgumentParserToolInfo-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/ArgumentParserToolInfo.modulemap", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-argument-parser.ArgumentParserToolInfo", "PRODUCT_MODULE_NAME": "ArgumentParserToolInfo", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "ArgumentParserToolInfo-Swift.h", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "ArgumentParserToolInfo", "TARGET_NAME": "ArgumentParserToolInfo"}, "guid": "PACKAGE-TARGET:ArgumentParserToolInfo::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/ArgumentParserToolInfo.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_5::REF_0", "guid": "PACKAGE-TARGET:ArgumentParserToolInfo::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-TARGET:ArgumentParserToolInfo::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}], "buildRules": [], "customTasks": [], "dependencies": [], "dynamicTargetVariantGuid": "PACKAGE-TARGET:ArgumentParserToolInfo-4859AC8F46C95AF0-dynamic", "guid": "PACKAGE-TARGET:ArgumentParserToolInfo", "name": "ArgumentParserToolInfo", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:ArgumentParserToolInfo", "name": "ArgumentParserToolInfo.o", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.objfile", "type": "standard"}