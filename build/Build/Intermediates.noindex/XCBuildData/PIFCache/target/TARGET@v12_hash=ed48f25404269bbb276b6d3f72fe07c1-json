{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "EXECUTABLE_NAME": "generate-manual", "GENERATE_EMBED_IN_CODE_ACCESSORS": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "MACOSX_DEPLOYMENT_TARGET": "10.13", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-argument-parser.generate-manual", "PRODUCT_MODULE_NAME": "generate_manual", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_VERSION": "5", "TARGET_NAME": "generate-manual", "TVOS_DEPLOYMENT_TARGET": "12.0", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE-PRODUCT:generate-manual::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "EXECUTABLE_NAME": "generate-manual", "GENERATE_EMBED_IN_CODE_ACCESSORS": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "MACOSX_DEPLOYMENT_TARGET": "10.13", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-argument-parser.generate-manual", "PRODUCT_MODULE_NAME": "generate_manual", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_VERSION": "5", "TARGET_NAME": "generate-manual", "TVOS_DEPLOYMENT_TARGET": "12.0", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE-PRODUCT:generate-manual::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_0", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_1", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::1", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_2", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::2", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_3", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::3", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_4", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::4", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_5", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::5", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_6", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::6", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_7", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::7", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_8", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::8", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_9", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::9", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_10", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::10", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_11", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::11", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_12", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::12", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_13", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::13", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_14", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::14", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_15", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::15", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_16", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::16", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_17", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::17", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_18", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::18", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_19", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::19", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_20", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::20", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_21", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::21", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_22", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::22", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_23", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::23", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_24", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::24", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_25", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::25", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_26", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::26", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_27", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::27", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_28", "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0::28", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_1::0", "platformFilters": [], "targetReference": "PACKAGE-TARGET:ArgumentParserToolInfo"}, {"guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_1::1", "platformFilters": [], "targetReference": "PACKAGE-TARGET:ArgumentParser"}], "guid": "PACKAGE-PRODUCT:generate-manual::BUILDPHASE_1", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-TARGET:ArgumentParserToolInfo", "platformFilters": []}, {"guid": "PACKAGE-TARGET:ArgumentParser", "platformFilters": []}], "guid": "PACKAGE-PRODUCT:generate-manual", "name": "generate-manual", "productReference": {"guid": "PRODUCTREF-PACKAGE-PRODUCT:generate-manual", "name": "generate-manual", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.tool", "type": "standard"}