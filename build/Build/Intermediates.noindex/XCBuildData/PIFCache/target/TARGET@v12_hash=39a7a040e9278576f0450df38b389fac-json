{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "COREML_CODEGEN_LANGUAGE": "Swift", "COREML_COMPILER_CONTAINER": "swift-package", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "PublicSuffixList.o", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "module PublicSuffixList {\nheader \"PublicSuffixList-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/PublicSuffixList.modulemap", "PACKAGE_RESOURCE_BUNDLE_NAME": "swift-psl_PublicSuffixList", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-psl.PublicSuffixList", "PRODUCT_MODULE_NAME": "PublicSuffixList", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "PublicSuffixList-Swift.h", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "PublicSuffixList", "TARGET_NAME": "PublicSuffixList"}, "guid": "PACKAGE-TARGET:PublicSuffixList::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {"EMBED_PACKAGE_RESOURCE_BUNDLE_NAMES": ["$(inherited)", "swift-psl_PublicSuffixList"], "FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "LD_RUNPATH_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/PublicSuffixList.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Debug"}, {"buildSettings": {"CLANG_COVERAGE_MAPPING_LINKER_ARGS": "NO", "CLANG_ENABLE_MODULES": "YES", "COREML_CODEGEN_LANGUAGE": "Swift", "COREML_COMPILER_CONTAINER": "swift-package", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "PublicSuffixList.o", "GENERATE_MASTER_OBJECT_FILE": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "MACH_O_TYPE": "mh_object", "MODULEMAP_FILE_CONTENTS": "module PublicSuffixList {\nheader \"PublicSuffixList-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/PublicSuffixList.modulemap", "PACKAGE_RESOURCE_BUNDLE_NAME": "swift-psl_PublicSuffixList", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-psl.PublicSuffixList", "PRODUCT_MODULE_NAME": "PublicSuffixList", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "STRIP_INSTALLED_PRODUCT": "NO", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "PublicSuffixList-Swift.h", "SWIFT_VERSION": "5", "TAPI_DYLIB_INSTALL_NAME": "PublicSuffixList", "TARGET_NAME": "PublicSuffixList"}, "guid": "PACKAGE-TARGET:PublicSuffixList::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {"EMBED_PACKAGE_RESOURCE_BUNDLE_NAMES": ["$(inherited)", "swift-psl_PublicSuffixList"], "FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/PublicSuffixList.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_9::REF_0", "guid": "PACKAGE-TARGET:PublicSuffixList::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_9::REF_1", "guid": "PACKAGE-TARGET:PublicSuffixList::BUILDPHASE_0::1", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_9::REF_2", "guid": "PACKAGE-TARGET:PublicSuffixList::BUILDPHASE_0::2", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-TARGET:PublicSuffixList::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-RESOURCE:PublicSuffixList", "platformFilters": []}], "dynamicTargetVariantGuid": "PACKAGE-TARGET:PublicSuffixList-1ABD691032485E80-dynamic", "guid": "PACKAGE-TARGET:PublicSuffixList", "name": "PublicSuffixList", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:PublicSuffixList", "name": "PublicSuffixList.o", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.objfile", "type": "standard"}