{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "EXECUTABLE_NAME": "ConverterTool", "GENERATE_EMBED_IN_CODE_ACCESSORS": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "MACOSX_DEPLOYMENT_TARGET": "10.13", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "safariconverterlib.ConverterTool", "PRODUCT_MODULE_NAME": "ConverterTool", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_VERSION": "5", "TARGET_NAME": "ConverterTool", "TVOS_DEPLOYMENT_TARGET": "12.0", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE-PRODUCT:ConverterTool::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "EXECUTABLE_NAME": "ConverterTool", "GENERATE_EMBED_IN_CODE_ACCESSORS": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "MACOSX_DEPLOYMENT_TARGET": "10.13", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "safariconverterlib.ConverterTool", "PRODUCT_MODULE_NAME": "ConverterTool", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_VERSION": "5", "TARGET_NAME": "ConverterTool", "TVOS_DEPLOYMENT_TARGET": "12.0", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE-PRODUCT:ConverterTool::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_2::REF_0", "guid": "PACKAGE-PRODUCT:ConverterTool::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_2::REF_1", "guid": "PACKAGE-PRODUCT:ConverterTool::BUILDPHASE_0::1", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_2::REF_2", "guid": "PACKAGE-PRODUCT:ConverterTool::BUILDPHASE_0::2", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_2::REF_3", "guid": "PACKAGE-PRODUCT:ConverterTool::BUILDPHASE_0::3", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-PRODUCT:ConverterTool::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"guid": "PACKAGE-PRODUCT:ConverterTool::BUILDPHASE_1::0", "platformFilters": [], "targetReference": "PACKAGE-PRODUCT:Punycode"}, {"guid": "PACKAGE-PRODUCT:ConverterTool::BUILDPHASE_1::1", "platformFilters": [], "targetReference": "PACKAGE-TARGET:ContentBlockerConverter"}, {"guid": "PACKAGE-PRODUCT:ConverterTool::BUILDPHASE_1::2", "platformFilters": [], "targetReference": "PACKAGE-PRODUCT:PublicSuffixList"}, {"guid": "PACKAGE-PRODUCT:ConverterTool::BUILDPHASE_1::3", "platformFilters": [], "targetReference": "PACKAGE-TARGET:FilterEngine"}, {"guid": "PACKAGE-PRODUCT:ConverterTool::BUILDPHASE_1::4", "platformFilters": [], "targetReference": "PACKAGE-PRODUCT:ArgumentParser"}], "guid": "PACKAGE-PRODUCT:ConverterTool::BUILDPHASE_1", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-PRODUCT:Punycode", "platformFilters": []}, {"guid": "PACKAGE-TARGET:ContentBlockerConverter", "platformFilters": []}, {"guid": "PACKAGE-PRODUCT:PublicSuffixList", "platformFilters": []}, {"guid": "PACKAGE-TARGET:FilterEngine", "platformFilters": []}, {"guid": "PACKAGE-PRODUCT:ArgumentParser", "platformFilters": []}], "guid": "PACKAGE-PRODUCT:ConverterTool", "name": "ConverterTool", "productReference": {"guid": "PRODUCTREF-PACKAGE-PRODUCT:ConverterTool", "name": "ConverterTool", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.tool", "type": "standard"}