{"buildConfigurations": [{"buildSettings": {"SDKROOT": "auto", "SDK_VARIANT": "auto"}, "guid": "PACKAGE-PRODUCT:GenerateManual::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"SDKROOT": "auto", "SDK_VARIANT": "auto"}, "guid": "PACKAGE-PRODUCT:GenerateManual::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "buildPhases": [], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-TARGET:GenerateManual", "platformFilters": []}], "guid": "PACKAGE-PRODUCT:GenerateManual", "name": "GenerateManual", "type": "aggregate"}