{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "FilterEngine", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "MODULEMAP_FILE_CONTENTS": "module FilterEngine {\nheader \"FilterEngine-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/FilterEngine.modulemap", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "safariconverterlib.FilterEngine", "PRODUCT_MODULE_NAME": "FilterEngine", "PRODUCT_NAME": "FilterEngine", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "FilterEngine-Swift.h", "SWIFT_VERSION": "5", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "FilterEngine"}, "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "LD_RUNPATH_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/FilterEngine.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "FilterEngine", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "MODULEMAP_FILE_CONTENTS": "module FilterEngine {\nheader \"FilterEngine-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/FilterEngine.modulemap", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "safariconverterlib.FilterEngine", "PRODUCT_MODULE_NAME": "FilterEngine", "PRODUCT_NAME": "FilterEngine", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "FilterEngine-Swift.h", "SWIFT_VERSION": "5", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "FilterEngine"}, "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/FilterEngine.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_0", "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_1", "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_0::1", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_2", "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_0::2", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_3", "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_0::3", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_4", "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_0::4", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_5", "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_0::5", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_6", "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_0::6", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_7", "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_0::7", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_8", "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_0::8", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_9", "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_0::9", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_10", "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_0::10", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_1::0", "platformFilters": [], "targetReference": "PACKAGE-PRODUCT:Punycode"}, {"guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_1::1", "platformFilters": [], "targetReference": "PACKAGE-TARGET:ContentBlockerConverter"}, {"guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_1::2", "platformFilters": [], "targetReference": "PACKAGE-PRODUCT:PublicSuffixList"}], "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic::BUILDPHASE_1", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-PRODUCT:Punycode", "platformFilters": []}, {"guid": "PACKAGE-TARGET:ContentBlockerConverter", "platformFilters": []}, {"guid": "PACKAGE-PRODUCT:PublicSuffixList", "platformFilters": []}], "guid": "PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic", "name": "FilterEngine", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:FilterEngine-C3C65F2158891C8-dynamic", "name": "FilterEngine.framework", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.framework", "type": "standard"}