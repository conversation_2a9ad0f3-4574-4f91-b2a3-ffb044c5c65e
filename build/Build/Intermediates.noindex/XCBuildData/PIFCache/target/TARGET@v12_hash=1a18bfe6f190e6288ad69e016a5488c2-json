{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "EXECUTABLE_NAME": "ArgumentParser_39B7DAEE4DD359BE_PackageProduct", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "swift-argument-parser.ArgumentParser", "PRODUCT_MODULE_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PRODUCT_NAME": "ArgumentParser_39B7DAEE4DD359BE_PackageProduct", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SWIFT_PACKAGE_NAME": "swift_argument_parser", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "ArgumentParser product", "USES_SWIFTPM_UNSAFE_FLAGS": "NO"}, "guid": "PACKAGE-PRODUCT:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "EXECUTABLE_NAME": "ArgumentParser_39B7DAEE4DD359BE_PackageProduct", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "swift-argument-parser.ArgumentParser", "PRODUCT_MODULE_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PRODUCT_NAME": "ArgumentParser_39B7DAEE4DD359BE_PackageProduct", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SWIFT_PACKAGE_NAME": "swift_argument_parser", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "ArgumentParser product", "USES_SWIFTPM_UNSAFE_FLAGS": "NO"}, "guid": "PACKAGE-PRODUCT:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"guid": "PACKAGE-PRODUCT:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::0", "platformFilters": [], "targetReference": "PACKAGE-TARGET:ArgumentParser"}, {"guid": "PACKAGE-PRODUCT:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::1", "platformFilters": [], "targetReference": "PACKAGE-TARGET:ArgumentParserToolInfo"}], "guid": "PACKAGE-PRODUCT:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "PACKAGE-PRODUCT:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_1", "type": "com.apple.buildphase.sources"}], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-TARGET:ArgumentParser", "platformFilters": []}, {"guid": "PACKAGE-TARGET:ArgumentParserToolInfo", "platformFilters": []}], "guid": "PACKAGE-PRODUCT:ArgumentParser-39B7DAEE4DD359BE-dynamic", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "productReference": {"guid": "PRODUCTREF-PACKAGE-PRODUCT:ArgumentParser-39B7DAEE4DD359BE-dynamic", "name": "ArgumentParser_39B7DAEE4DD359BE_PackageProduct.framework", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.framework", "type": "standard"}