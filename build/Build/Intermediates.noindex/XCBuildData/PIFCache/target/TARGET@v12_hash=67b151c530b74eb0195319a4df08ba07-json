{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "EXECUTABLE_NAME": "ResourceBuilder", "GENERATE_EMBED_IN_CODE_ACCESSORS": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "MACOSX_DEPLOYMENT_TARGET": "10.13", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-psl.ResourceBuilder", "PRODUCT_MODULE_NAME": "ResourceBuilder", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_VERSION": "5", "TARGET_NAME": "ResourceBuilder", "TVOS_DEPLOYMENT_TARGET": "12.0", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE-PRODUCT:ResourceBuilder::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "EXECUTABLE_NAME": "ResourceBuilder", "GENERATE_EMBED_IN_CODE_ACCESSORS": "NO", "GENERATE_RESOURCE_ACCESSORS": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "MACOSX_DEPLOYMENT_TARGET": "10.13", "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-psl.ResourceBuilder", "PRODUCT_MODULE_NAME": "ResourceBuilder", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_VERSION": "5", "TARGET_NAME": "ResourceBuilder", "TVOS_DEPLOYMENT_TARGET": "12.0", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE-PRODUCT:ResourceBuilder::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_4::REF_0", "guid": "PACKAGE-PRODUCT:ResourceBuilder::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-PRODUCT:ResourceBuilder::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"guid": "PACKAGE-PRODUCT:ResourceBuilder::BUILDPHASE_1::0", "platformFilters": [], "targetReference": "PACKAGE-TARGET:PublicSuffixList"}, {"guid": "PACKAGE-PRODUCT:ResourceBuilder::BUILDPHASE_1::1", "platformFilters": [], "targetReference": "PACKAGE-PRODUCT:Punycode"}], "guid": "PACKAGE-PRODUCT:ResourceBuilder::BUILDPHASE_1", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-TARGET:PublicSuffixList", "platformFilters": []}, {"guid": "PACKAGE-PRODUCT:Punycode", "platformFilters": []}], "guid": "PACKAGE-PRODUCT:ResourceBuilder", "name": "ResourceBuilder", "productReference": {"guid": "PRODUCTREF-PACKAGE-PRODUCT:ResourceBuilder", "name": "ResourceBuilder", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.tool", "type": "standard"}