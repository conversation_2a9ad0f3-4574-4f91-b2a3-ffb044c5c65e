{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"SDKROOT": "auto", "SDK_VARIANT": "auto", "USES_SWIFTPM_UNSAFE_FLAGS": "NO"}, "guid": "PACKAGE-PRODUCT:Punycode::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"SDKROOT": "auto", "SDK_VARIANT": "auto", "USES_SWIFTPM_UNSAFE_FLAGS": "NO"}, "guid": "PACKAGE-PRODUCT:Punycode::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "customTasks": [], "dependencies": [{"guid": "PACKAGE-TARGET:Punycode", "platformFilters": []}], "dynamicTargetVariantGuid": "PACKAGE-PRODUCT:Punycode--352DFC8771490AB1-dynamic", "frameworksBuildPhase": {"buildFiles": [{"guid": "PACKAGE-PRODUCT:Punycode::BUILDPHASE_0::0", "platformFilters": [], "targetReference": "PACKAGE-TARGET:Punycode"}], "guid": "PACKAGE-PRODUCT:Punycode::BUILDPHASE_0", "type": "com.apple.buildphase.frameworks"}, "guid": "PACKAGE-PRODUCT:Punycode", "name": "Punycode", "type": "packageProduct"}