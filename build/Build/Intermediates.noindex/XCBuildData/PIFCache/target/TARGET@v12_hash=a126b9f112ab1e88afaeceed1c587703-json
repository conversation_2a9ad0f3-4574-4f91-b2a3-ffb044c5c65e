{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"SDKROOT": "auto", "SDK_VARIANT": "auto", "USES_SWIFTPM_UNSAFE_FLAGS": "NO"}, "guid": "PACKAGE-PRODUCT:ContentBlockerConverter::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"SDKROOT": "auto", "SDK_VARIANT": "auto", "USES_SWIFTPM_UNSAFE_FLAGS": "NO"}, "guid": "PACKAGE-PRODUCT:ContentBlockerConverter::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "customTasks": [], "dependencies": [{"guid": "PACKAGE-TARGET:ContentBlockerConverter", "platformFilters": []}, {"guid": "PACKAGE-TARGET:FilterEngine", "platformFilters": []}, {"guid": "PACKAGE-PRODUCT:Punycode", "platformFilters": []}, {"guid": "PACKAGE-PRODUCT:PublicSuffixList", "platformFilters": []}], "dynamicTargetVariantGuid": "PACKAGE-PRODUCT:ContentBlockerConverter--48525B91CD55894-dynamic", "frameworksBuildPhase": {"buildFiles": [{"guid": "PACKAGE-PRODUCT:ContentBlockerConverter::BUILDPHASE_0::0", "platformFilters": [], "targetReference": "PACKAGE-TARGET:ContentBlockerConverter"}, {"guid": "PACKAGE-PRODUCT:ContentBlockerConverter::BUILDPHASE_0::1", "platformFilters": [], "targetReference": "PACKAGE-TARGET:FilterEngine"}, {"guid": "PACKAGE-PRODUCT:ContentBlockerConverter::BUILDPHASE_0::2", "platformFilters": [], "targetReference": "PACKAGE-PRODUCT:Punycode"}, {"guid": "PACKAGE-PRODUCT:ContentBlockerConverter::BUILDPHASE_0::3", "platformFilters": [], "targetReference": "PACKAGE-PRODUCT:PublicSuffixList"}], "guid": "PACKAGE-PRODUCT:ContentBlockerConverter::BUILDPHASE_0", "type": "com.apple.buildphase.frameworks"}, "guid": "PACKAGE-PRODUCT:ContentBlockerConverter", "name": "ContentBlockerConverter", "type": "packageProduct"}