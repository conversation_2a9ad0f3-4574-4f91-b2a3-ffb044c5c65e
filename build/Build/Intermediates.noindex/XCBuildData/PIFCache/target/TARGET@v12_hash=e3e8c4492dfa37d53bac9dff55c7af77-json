{"approvedByUser": "true", "buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "MODULEMAP_FILE_CONTENTS": "module ArgumentParser {\nheader \"ArgumentParser-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/ArgumentParser.modulemap", "OTHER_SWIFT_FLAGS": ["$(inherited)", "-enable-experimental-feature", "StrictConcurrency"], "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-argument-parser.ArgumentParser", "PRODUCT_MODULE_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PRODUCT_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "ArgumentParser-Swift.h", "SWIFT_VERSION": "5", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "LD_RUNPATH_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/ArgumentParser.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_MODULES": "YES", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "EXECUTABLE_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "MODULEMAP_FILE_CONTENTS": "module ArgumentParser {\nheader \"ArgumentParser-Swift.h\"\nexport *\n}", "MODULEMAP_PATH": "$(GENERATED_MODULEMAP_DIR)/ArgumentParser.modulemap", "OTHER_SWIFT_FLAGS": ["$(inherited)", "-enable-experimental-feature", "StrictConcurrency"], "PACKAGE_RESOURCE_TARGET_KIND": "regular", "PRODUCT_BUNDLE_IDENTIFIER": "swift-argument-parser.ArgumentParser", "PRODUCT_MODULE_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PRODUCT_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SDKROOT": "auto", "SDK_VARIANT": "auto", "SUPPORTS_TEXT_BASED_API": "NO", "SWIFT_ENABLE_BARE_SLASH_REGEX": "NO", "SWIFT_OBJC_INTERFACE_HEADER_DIR": "$(GENERATED_MODULEMAP_DIR)", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "ArgumentParser-Swift.h", "SWIFT_VERSION": "5", "TARGET_BUILD_DIR": "$(TARGET_BUILD_DIR)/PackageFrameworks", "TARGET_NAME": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {"FRAMEWORK_SEARCH_PATHS": ["$(BUILT_PRODUCTS_DIR)/PackageFrameworks", "$(inherited)"], "OTHER_CFLAGS": ["-fmodule-map-file=$(GENERATED_MODULEMAP_DIR)/ArgumentParser.modulemap", "$(inherited)"], "OTHER_LDFLAGS": ["-Wl,-no_warn_duplicate_libraries", "$(inherited)"], "OTHER_LDRFLAGS": []}}, "name": "Release"}], "buildPhases": [{"buildFiles": [{"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_0", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::0", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_1", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::1", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_2", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::2", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_3", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::3", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_4", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::4", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_5", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::5", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_6", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::6", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_7", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::7", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_8", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::8", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_9", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::9", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_10", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::10", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_11", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::11", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_12", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::12", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_13", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::13", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_14", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::14", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_15", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::15", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_16", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::16", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_17", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::17", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_18", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::18", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_19", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::19", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_20", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::20", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_21", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::21", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_22", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::22", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_23", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::23", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_24", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::24", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_25", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::25", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_26", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::26", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_27", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::27", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_28", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::28", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_29", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::29", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_30", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::30", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_31", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::31", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_32", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::32", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_33", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::33", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_34", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::34", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_35", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::35", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_36", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::36", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_37", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::37", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_38", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::38", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_39", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::39", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_40", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::40", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_41", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::41", "platformFilters": [], "removeHeadersOnCopy": "false"}, {"codeSignOnCopy": "false", "fileReference": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_42", "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0::42", "platformFilters": [], "removeHeadersOnCopy": "false"}], "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_1::0", "platformFilters": [], "targetReference": "PACKAGE-TARGET:ArgumentParserToolInfo"}], "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic::BUILDPHASE_1", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "customTasks": [], "dependencies": [{"guid": "PACKAGE-TARGET:ArgumentParserToolInfo", "platformFilters": []}], "guid": "PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "productReference": {"guid": "PRODUCTREF-PACKAGE-TARGET:ArgumentParser-39B7DAEE4DD359BE-dynamic", "name": "ArgumentParser.framework", "type": "file"}, "productTypeIdentifier": "com.apple.product-type.framework", "type": "standard"}