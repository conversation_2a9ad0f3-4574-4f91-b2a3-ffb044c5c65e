{"buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_OBJC_ARC": "YES", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "", "COMPILER_WORKING_DIRECTORY": "$(WORKSPACE_DIR)", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_NS_ASSERTIONS": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_TESTING_SEARCH_PATHS": "YES", "ENTITLEMENTS_REQUIRED": "NO", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "SWIFT_PACKAGE", "DEBUG=1"], "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "KEEP_PRIVATE_EXTERNS": "NO", "MACOSX_DEPLOYMENT_TARGET": "10.13", "ONLY_ACTIVE_ARCH": "YES", "OTHER_CFLAGS": ["$(inherited)", "-DXcode"], "OTHER_LDRFLAGS": [], "OTHER_SWIFT_FLAGS": ["$(inherited)", "-DXcode"], "PRODUCT_NAME": "$(TARGET_NAME)", "SKIP_CLANG_STATIC_ANALYZER": "YES", "SKIP_INSTALL": "YES", "SUPPORTED_PLATFORMS": ["$(AVAILABLE_PLATFORMS)"], "SUPPRESS_WARNINGS": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": ["$(inherited)", "SWIFT_PACKAGE", "DEBUG"], "SWIFT_INSTALL_OBJC_HEADER": "NO", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2,3,4,6,7", "TVOS_DEPLOYMENT_TARGET": "12.0", "USE_HEADERMAP": "NO", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_OBJC_ARC": "YES", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "", "COMPILER_WORKING_DIRECTORY": "$(WORKSPACE_DIR)", "COPY_PHASE_STRIP": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_TESTING_SEARCH_PATHS": "YES", "ENTITLEMENTS_REQUIRED": "NO", "GCC_OPTIMIZATION_LEVEL": "s", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "SWIFT_PACKAGE"], "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "KEEP_PRIVATE_EXTERNS": "NO", "MACOSX_DEPLOYMENT_TARGET": "10.13", "OTHER_CFLAGS": ["$(inherited)", "-DXcode"], "OTHER_LDRFLAGS": [], "OTHER_SWIFT_FLAGS": ["$(inherited)", "-DXcode"], "PRODUCT_NAME": "$(TARGET_NAME)", "SKIP_CLANG_STATIC_ANALYZER": "YES", "SKIP_INSTALL": "YES", "SUPPORTED_PLATFORMS": ["$(AVAILABLE_PLATFORMS)"], "SUPPRESS_WARNINGS": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": ["$(inherited)", "SWIFT_PACKAGE"], "SWIFT_INSTALL_OBJC_HEADER": "NO", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2,3,4,6,7", "TVOS_DEPLOYMENT_TARGET": "12.0", "USE_HEADERMAP": "NO", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "defaultConfigurationName": "Release", "groupTree": {"children": [{"children": [], "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git::MAINGROUP::REF_0", "name": "AdditionalFiles", "path": "/", "sourceTree": "<absolute>", "type": "group"}, {"children": [], "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git::MAINGROUP::REF_1", "name": "Binaries", "path": "/", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git::MAINGROUP::REF_2::REF_0", "path": "Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git::MAINGROUP::REF_2::REF_1", "path": "Helpers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git::MAINGROUP::REF_2::REF_2", "path": "Punycode.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git::MAINGROUP::REF_2", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/PunycodeSwift/Sources", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/PunycodeSwift/Sources", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git::MAINGROUP::REF_3::REF_0", "path": "Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git::MAINGROUP::REF_3::REF_1", "path": "Helpers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git::MAINGROUP::REF_3::REF_2", "path": "Punycode.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git::MAINGROUP::REF_3", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/PunycodeSwift/Sources", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/PunycodeSwift/Sources", "sourceTree": "<absolute>", "type": "group"}], "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git::MAINGROUP", "name": "", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "PACKAGE:https://github.com/gumob/PunycodeSwift.git", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/PunycodeSwift/Package.swift", "projectDirectory": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/PunycodeSwift", "projectIsPackage": "true", "projectName": "Punycode", "targets": ["TARGET@v12_hash=ba0aae75a6eb1c68b8ece33c999dcd71", "TARGET@v12_hash=ec6be63dd768138654f32202070ca47f", "TARGET@v12_hash=e58dd753e4d05725f034b428ab39da0c", "TARGET@v12_hash=a0b062d9a4f8ff7d04751ea313feac13"]}