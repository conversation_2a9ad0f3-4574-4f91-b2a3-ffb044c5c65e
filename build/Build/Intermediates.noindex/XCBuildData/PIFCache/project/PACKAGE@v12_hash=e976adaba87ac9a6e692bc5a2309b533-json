{"buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_OBJC_ARC": "YES", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "", "COMPILER_WORKING_DIRECTORY": "$(WORKSPACE_DIR)", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_NS_ASSERTIONS": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_TESTING_SEARCH_PATHS": "YES", "ENTITLEMENTS_REQUIRED": "NO", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "SWIFT_PACKAGE", "DEBUG=1"], "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "KEEP_PRIVATE_EXTERNS": "NO", "MACOSX_DEPLOYMENT_TARGET": "10.13", "ONLY_ACTIVE_ARCH": "YES", "OTHER_CFLAGS": ["$(inherited)", "-DXcode"], "OTHER_LDRFLAGS": [], "OTHER_SWIFT_FLAGS": ["$(inherited)", "-DXcode"], "PRODUCT_NAME": "$(TARGET_NAME)", "SKIP_CLANG_STATIC_ANALYZER": "YES", "SKIP_INSTALL": "YES", "SUPPORTED_PLATFORMS": ["$(AVAILABLE_PLATFORMS)"], "SUPPRESS_WARNINGS": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": ["$(inherited)", "SWIFT_PACKAGE", "DEBUG"], "SWIFT_INSTALL_OBJC_HEADER": "NO", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2,3,4,6,7", "TVOS_DEPLOYMENT_TARGET": "12.0", "USE_HEADERMAP": "NO", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_OBJC_ARC": "YES", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "", "COMPILER_WORKING_DIRECTORY": "$(WORKSPACE_DIR)", "COPY_PHASE_STRIP": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_TESTING_SEARCH_PATHS": "YES", "ENTITLEMENTS_REQUIRED": "NO", "GCC_OPTIMIZATION_LEVEL": "s", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "SWIFT_PACKAGE"], "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "KEEP_PRIVATE_EXTERNS": "NO", "MACOSX_DEPLOYMENT_TARGET": "10.13", "OTHER_CFLAGS": ["$(inherited)", "-DXcode"], "OTHER_LDRFLAGS": [], "OTHER_SWIFT_FLAGS": ["$(inherited)", "-DXcode"], "PRODUCT_NAME": "$(TARGET_NAME)", "SKIP_CLANG_STATIC_ANALYZER": "YES", "SKIP_INSTALL": "YES", "SUPPORTED_PLATFORMS": ["$(AVAILABLE_PLATFORMS)"], "SUPPRESS_WARNINGS": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": ["$(inherited)", "SWIFT_PACKAGE"], "SWIFT_INSTALL_OBJC_HEADER": "NO", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2,3,4,6,7", "TVOS_DEPLOYMENT_TARGET": "12.0", "USE_HEADERMAP": "NO", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "defaultConfigurationName": "Release", "groupTree": {"children": [{"children": [], "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_0", "name": "AdditionalFiles", "path": "/", "sourceTree": "<absolute>", "type": "group"}, {"children": [], "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_1", "name": "Binaries", "path": "/", "sourceTree": "<absolute>", "type": "group"}, {"fileType": "file", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_2", "path": "$(CONFIGURATION_BUILD_DIR)/swift-psl_PublicSuffixList.bundle", "sourceTree": "<group>", "type": "file"}, {"fileType": "file", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_3", "path": "$(CONFIGURATION_BUILD_DIR)/swift-psl_PublicSuffixList.bundle", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_4::REF_0", "path": "main.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_4", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl/Sources/ResourceBuilder", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl/Sources/ResourceBuilder", "sourceTree": "<absolute>", "type": "group"}, {"fileType": "file", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_5", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl/Sources/PublicSuffixList/Resources/asterisk.bin", "sourceTree": "<absolute>", "type": "file"}, {"fileType": "file", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_6", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl/Sources/PublicSuffixList/Resources/common.bin", "sourceTree": "<absolute>", "type": "file"}, {"fileType": "file", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_7", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl/Sources/PublicSuffixList/Resources/negated.bin", "sourceTree": "<absolute>", "type": "file"}, {"fileType": "file", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_8", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl/Sources/PublicSuffixList/Resources/version.txt", "sourceTree": "<absolute>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_9::REF_0", "path": "ByteArraySuffixTrie.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_9::REF_1", "path": "PublicSuffixList.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_9::REF_2", "path": "SuffixTrie.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_9", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl/Sources/PublicSuffixList", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl/Sources/PublicSuffixList", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_10::REF_0", "path": "ByteArraySuffixTrie.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_10::REF_1", "path": "PublicSuffixList.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_10::REF_2", "path": "SuffixTrie.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_10", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl/Sources/PublicSuffixList", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl/Sources/PublicSuffixList", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_11::REF_0", "path": "main.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP::REF_11", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl/Sources/ResourceBuilder", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl/Sources/ResourceBuilder", "sourceTree": "<absolute>", "type": "group"}], "guid": "PACKAGE:https://github.com/ameshkov/swift-psl::MAINGROUP", "name": "", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "PACKAGE:https://github.com/ameshkov/swift-psl", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl/Package.swift", "projectDirectory": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-psl", "projectIsPackage": "true", "projectName": "swift-psl", "targets": ["TARGET@v12_hash=b70029bd608282546c6e230a58bd26cd", "TARGET@v12_hash=e957694021e0da1937356e2dd94b5ecd", "TARGET@v12_hash=67b151c530b74eb0195319a4df08ba07", "TARGET@v12_hash=39a7a040e9278576f0450df38b389fac", "TARGET@v12_hash=f73116d195972702657b6ee86a9938cf", "TARGET@v12_hash=7f69ce13febec0ce3a797faa344efac6", "TARGET@v12_hash=c9819e6da2dfd09ac713cc2d689e7bd3"]}