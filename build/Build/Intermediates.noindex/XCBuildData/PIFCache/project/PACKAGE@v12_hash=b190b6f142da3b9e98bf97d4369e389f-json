{"buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_OBJC_ARC": "YES", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "", "COMPILER_WORKING_DIRECTORY": "$(WORKSPACE_DIR)", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_NS_ASSERTIONS": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_TESTING_SEARCH_PATHS": "YES", "ENTITLEMENTS_REQUIRED": "NO", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "SWIFT_PACKAGE", "DEBUG=1"], "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "KEEP_PRIVATE_EXTERNS": "NO", "MACOSX_DEPLOYMENT_TARGET": "10.13", "ONLY_ACTIVE_ARCH": "YES", "OTHER_CFLAGS": ["$(inherited)", "-DXcode"], "OTHER_LDRFLAGS": [], "OTHER_SWIFT_FLAGS": ["$(inherited)", "-DXcode"], "PRODUCT_NAME": "$(TARGET_NAME)", "SKIP_CLANG_STATIC_ANALYZER": "YES", "SKIP_INSTALL": "YES", "SUPPORTED_PLATFORMS": ["$(AVAILABLE_PLATFORMS)"], "SUPPRESS_WARNINGS": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": ["$(inherited)", "SWIFT_PACKAGE", "DEBUG"], "SWIFT_INSTALL_OBJC_HEADER": "NO", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2,3,4,6,7", "TVOS_DEPLOYMENT_TARGET": "12.0", "USE_HEADERMAP": "NO", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_OBJC_ARC": "YES", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "", "COMPILER_WORKING_DIRECTORY": "$(WORKSPACE_DIR)", "COPY_PHASE_STRIP": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_TESTING_SEARCH_PATHS": "YES", "ENTITLEMENTS_REQUIRED": "NO", "GCC_OPTIMIZATION_LEVEL": "s", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "SWIFT_PACKAGE"], "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "KEEP_PRIVATE_EXTERNS": "NO", "MACOSX_DEPLOYMENT_TARGET": "10.13", "OTHER_CFLAGS": ["$(inherited)", "-DXcode"], "OTHER_LDRFLAGS": [], "OTHER_SWIFT_FLAGS": ["$(inherited)", "-DXcode"], "PRODUCT_NAME": "$(TARGET_NAME)", "SKIP_CLANG_STATIC_ANALYZER": "YES", "SKIP_INSTALL": "YES", "SUPPORTED_PLATFORMS": ["$(AVAILABLE_PLATFORMS)"], "SUPPRESS_WARNINGS": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": ["$(inherited)", "SWIFT_PACKAGE"], "SWIFT_INSTALL_OBJC_HEADER": "NO", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2,3,4,6,7", "TVOS_DEPLOYMENT_TARGET": "12.0", "USE_HEADERMAP": "NO", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "defaultConfigurationName": "Release", "groupTree": {"children": [{"children": [], "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_0", "name": "AdditionalFiles", "path": "/", "sourceTree": "<absolute>", "type": "group"}, {"children": [], "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_1", "name": "Binaries", "path": "/", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_0", "path": "AuthorArgument.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_1", "path": "DSL/ArgumentSynopsis.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_2", "path": "DSL/Author.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_3", "path": "DSL/Authors.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_4", "path": "DSL/Core/Container.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_5", "path": "DSL/Core/Empty.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_6", "path": "DSL/Core/ForEach.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_7", "path": "DSL/Core/MDocASTNodeWrapper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_8", "path": "DSL/Core/MDocBuilder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_9", "path": "DSL/Core/MDocComponent.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_10", "path": "DSL/Document.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_11", "path": "DSL/DocumentDate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_12", "path": "DSL/Exit.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_13", "path": "DSL/List.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_14", "path": "DSL/MultiPageDescription.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_15", "path": "DSL/Name.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_16", "path": "DSL/Preamble.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_17", "path": "DSL/Section.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_18", "path": "DSL/SeeAlso.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_19", "path": "DSL/SinglePageDescription.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_20", "path": "DSL/Synopsis.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_21", "path": "Extensions/ArgumentParser+MDoc.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_22", "path": "Extensions/Date+ExpressibleByArgument.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_23", "path": "Extensions/Process+SimpleAPI.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_24", "path": "GenerateManual.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_25", "path": "MDoc/MDocASTNode.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_26", "path": "MDoc/MDocMacro.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_27", "path": "MDoc/MDocSerializationContext.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2::REF_28", "path": "MDoc/String+Escaping.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_2", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser/Tools/generate-manual", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser/Tools/generate-manual", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_0", "path": "Completions/BashCompletionsGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_1", "path": "Completions/CompletionsGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_2", "path": "Completions/FishCompletionsGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_3", "path": "Completions/ZshCompletionsGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.documentationcatalog", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_4", "path": "Documentation.docc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_5", "path": "Parsable Properties/Argument.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_6", "path": "Parsable Properties/ArgumentHelp.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_7", "path": "Parsable Properties/ArgumentVisibility.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_8", "path": "Parsable Properties/CompletionKind.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_9", "path": "Parsable Properties/Errors.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_10", "path": "Parsable Properties/Flag.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_11", "path": "Parsable Properties/NameSpecification.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_12", "path": "Parsable Properties/Option.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_13", "path": "Parsable Properties/OptionGroup.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_14", "path": "Parsable Types/AsyncParsableCommand.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_15", "path": "Parsable Types/CommandConfiguration.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_16", "path": "Parsable Types/CommandGroup.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_17", "path": "Parsable Types/EnumerableFlag.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_18", "path": "Parsable Types/ExpressibleByArgument.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_19", "path": "Parsable Types/ParsableArguments.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_20", "path": "Parsable Types/ParsableArgumentsValidation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_21", "path": "Parsable Types/ParsableCommand.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_22", "path": "Parsing/ArgumentDecoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_23", "path": "Parsing/ArgumentDefinition.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_24", "path": "Parsing/ArgumentSet.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_25", "path": "Parsing/CommandParser.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_26", "path": "Parsing/InputKey.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_27", "path": "Parsing/InputOrigin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_28", "path": "Parsing/Name.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_29", "path": "Parsing/Parsed.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_30", "path": "Parsing/ParsedValues.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_31", "path": "Parsing/ParserError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_32", "path": "Parsing/SplitArguments.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_33", "path": "Usage/DumpHelpGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_34", "path": "Usage/HelpCommand.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_35", "path": "Usage/HelpGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_36", "path": "Usage/MessageInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_37", "path": "Usage/UsageGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_38", "path": "Utilities/CollectionExtensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_39", "path": "Utilities/Platform.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_40", "path": "Utilities/SequenceExtensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_41", "path": "Utilities/StringExtensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3::REF_42", "path": "Utilities/Tree.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_3", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser/Sources/ArgumentParser", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser/Sources/ArgumentParser", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_0", "path": "Completions/BashCompletionsGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_1", "path": "Completions/CompletionsGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_2", "path": "Completions/FishCompletionsGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_3", "path": "Completions/ZshCompletionsGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.documentationcatalog", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_4", "path": "Documentation.docc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_5", "path": "Parsable Properties/Argument.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_6", "path": "Parsable Properties/ArgumentHelp.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_7", "path": "Parsable Properties/ArgumentVisibility.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_8", "path": "Parsable Properties/CompletionKind.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_9", "path": "Parsable Properties/Errors.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_10", "path": "Parsable Properties/Flag.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_11", "path": "Parsable Properties/NameSpecification.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_12", "path": "Parsable Properties/Option.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_13", "path": "Parsable Properties/OptionGroup.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_14", "path": "Parsable Types/AsyncParsableCommand.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_15", "path": "Parsable Types/CommandConfiguration.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_16", "path": "Parsable Types/CommandGroup.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_17", "path": "Parsable Types/EnumerableFlag.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_18", "path": "Parsable Types/ExpressibleByArgument.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_19", "path": "Parsable Types/ParsableArguments.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_20", "path": "Parsable Types/ParsableArgumentsValidation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_21", "path": "Parsable Types/ParsableCommand.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_22", "path": "Parsing/ArgumentDecoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_23", "path": "Parsing/ArgumentDefinition.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_24", "path": "Parsing/ArgumentSet.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_25", "path": "Parsing/CommandParser.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_26", "path": "Parsing/InputKey.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_27", "path": "Parsing/InputOrigin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_28", "path": "Parsing/Name.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_29", "path": "Parsing/Parsed.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_30", "path": "Parsing/ParsedValues.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_31", "path": "Parsing/ParserError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_32", "path": "Parsing/SplitArguments.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_33", "path": "Usage/DumpHelpGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_34", "path": "Usage/HelpCommand.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_35", "path": "Usage/HelpGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_36", "path": "Usage/MessageInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_37", "path": "Usage/UsageGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_38", "path": "Utilities/CollectionExtensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_39", "path": "Utilities/Platform.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_40", "path": "Utilities/SequenceExtensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_41", "path": "Utilities/StringExtensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4::REF_42", "path": "Utilities/Tree.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_4", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser/Sources/ArgumentParser", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser/Sources/ArgumentParser", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_5::REF_0", "path": "ToolInfo.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_5", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_6::REF_0", "path": "ToolInfo.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_6", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_0", "path": "AuthorArgument.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_1", "path": "DSL/ArgumentSynopsis.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_2", "path": "DSL/Author.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_3", "path": "DSL/Authors.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_4", "path": "DSL/Core/Container.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_5", "path": "DSL/Core/Empty.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_6", "path": "DSL/Core/ForEach.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_7", "path": "DSL/Core/MDocASTNodeWrapper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_8", "path": "DSL/Core/MDocBuilder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_9", "path": "DSL/Core/MDocComponent.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_10", "path": "DSL/Document.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_11", "path": "DSL/DocumentDate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_12", "path": "DSL/Exit.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_13", "path": "DSL/List.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_14", "path": "DSL/MultiPageDescription.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_15", "path": "DSL/Name.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_16", "path": "DSL/Preamble.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_17", "path": "DSL/Section.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_18", "path": "DSL/SeeAlso.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_19", "path": "DSL/SinglePageDescription.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_20", "path": "DSL/Synopsis.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_21", "path": "Extensions/ArgumentParser+MDoc.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_22", "path": "Extensions/Date+ExpressibleByArgument.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_23", "path": "Extensions/Process+SimpleAPI.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_24", "path": "GenerateManual.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_25", "path": "MDoc/MDocASTNode.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_26", "path": "MDoc/MDocMacro.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_27", "path": "MDoc/MDocSerializationContext.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7::REF_28", "path": "MDoc/String+Escaping.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP::REF_7", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser/Tools/generate-manual", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser/Tools/generate-manual", "sourceTree": "<absolute>", "type": "group"}], "guid": "PACKAGE:https://github.com/apple/swift-argument-parser::MAINGROUP", "name": "", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "PACKAGE:https://github.com/apple/swift-argument-parser", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser/<EMAIL>", "projectDirectory": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/swift-argument-parser", "projectIsPackage": "true", "projectName": "swift-argument-parser", "targets": ["TARGET@v12_hash=45bfbf5669d858df31824a5f1710c113", "TARGET@v12_hash=1a18bfe6f190e6288ad69e016a5488c2", "TARGET@v12_hash=dd54d6202a71fa2bc79bdcf8f126daf0", "TARGET@v12_hash=ed48f25404269bbb276b6d3f72fe07c1", "TARGET@v12_hash=98dff1dc66d100f5a75e108dc70c3670", "TARGET@v12_hash=e3e8c4492dfa37d53bac9dff55c7af77", "TARGET@v12_hash=82b4d4abc1122236609d91cc28a2a1e0", "TARGET@v12_hash=bf4e9d2e9d74e03e9900ad6575147aa8", "TARGET@v12_hash=ab05424aaa237f0a2ba03a3839b10fa2", "TARGET@v12_hash=9e8ea5e6edbe9bdd87e4c35aa2217686"]}