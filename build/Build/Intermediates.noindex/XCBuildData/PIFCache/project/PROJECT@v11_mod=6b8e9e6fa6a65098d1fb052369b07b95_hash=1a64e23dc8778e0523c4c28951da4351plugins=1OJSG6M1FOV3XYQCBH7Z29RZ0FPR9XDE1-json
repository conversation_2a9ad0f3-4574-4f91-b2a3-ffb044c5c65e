{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.4", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "1a64e23dc8778e0523c4c28951da4351f40d9edb8acf498868777466b43b978c", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.4", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "1a64e23dc8778e0523c4c28951da4351cb7cf318880b6b12b04b6ee6afe2265d", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351f658b121906c8b9e411b528dfd70e155", "path": "MediaFile.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435110e12d8a87a44aba266a2f0e0566d130", "path": "SecurityModels.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da43511f6dda02ec08f06c083cd04f6f8dee46", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435102de1c6a6b9afceb863c5f0e1c48317c", "path": "ContentBlockerManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435134a637ed68eaadcf997d985d1fb10f34", "path": "EasyListConverter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351c1e1ac1894de3f6b5622d4dee5091c24", "path": "MediaImportService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351657f1fe4193a5be649212657bc85b154", "path": "SecurityService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43514fd0569377c0102f2e52f1a5df7a0ebd", "path": "ServiceIntegrationConfig.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351840a5bfbebe17ea50f84ac926aad02d7", "path": "ServiceManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435141244b09481121c6609363b7b186d7bb", "path": "SystemErrorHandler.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da43511a35892d422213ab709646b0bc7f2202", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351552570229990150827a09a6eedbc770e", "path": "BrowserManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43517bb9da943d84dd428491751bf75a6411", "path": "LogManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43513ff207a8fc26c6bb6faedbf71105a32f", "path": "MediaFormatDetector.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351c7f12630386d703a21fb535e7e38b07e", "path": "UnifiedMemoryManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da43510e26b6fdc4400a1a6b2d1bfe01a861a4", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435162d0c13d6dd7cd5a61e66fb014a9f76c", "path": "AdvancedStatsViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435190e4039267bf32a3822cf2e453ef325d", "path": "FolderViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351990b620c92e6500511f15db4c9d512de", "path": "MediaLibraryViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351e56c2981e13a3d5ae7ff650e00e62317", "path": "MediaViewerState.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43519db756299bc3c87b2c5ed1efd530f23f", "path": "NewWebBrowserViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435100180f9a969574b66f930874e0a28700", "path": "VideoPlayerService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da4351d1c077e81ee14ab51789927303d0536a", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43512441a542db9b95968b96c580f9063c78", "path": "AppComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351392bf4fba3e6e0dbc8deb8d4a56661fa", "path": "BrowserUIComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43516bb702ee7bd53ed92d26a692ba5c65b7", "path": "FullScreenGestureOverlay.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351c7d173f42d2c8fb9f7526376cd19f4d9", "path": "FullScreenNavigationBar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43519b4db456fdf2ed04b6bb4a654c9738d4", "path": "FullScreenThumbnailBar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43513b82c1051aaa151e134d28a5367cce24", "path": "ImageContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351f418a970ff2306f79cd74f8b04bc2c3b", "path": "MediaComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43516181e9f99ccb8d332b22ba0e629431f7", "path": "MediaContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351102cec7c50f730b4a955c93cdded6c58", "path": "MediaLoadingAndErrorViews.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43514cce3252d431224e75e32e50718721db", "path": "VideoPlayerView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da43514559aeabcdfd2938a362af15bcc78930", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351ec6e3387ab0b176179f9f202337e69bb", "path": "AppDesignSystem.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da43512622e3ed824b90ec46b36eb2f8be4187", "name": "DesignSystem", "path": "DesignSystem", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351fa505269b1cbf40259cc9350b523b8a0", "path": "AdBlockTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435152f94abc381437b41698e54d1c98aaf8", "path": "AdvancedStatsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351248434bc16c464779dcd4f63f4c45cc0", "path": "ContentBlockerSettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351fb8f84df2b2a0a845693acde643c0641", "path": "DataManagementView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435102f5be681df6ae4ccd5c4be3e1106479", "path": "DocumentPickerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43516ac34eb07d83d74eafd618ae84bbaea2", "path": "FullScreenMediaViewer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43510747c2265a486af8b3e9ae3c45cef5cf", "path": "ImportProgressView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43513388ad2b521b5a424c51cc5778d0683e", "path": "MainView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351c0009550eb09d4c43f51a0d5456ebda2", "path": "MediaDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43518b809adef9a60aeeeface018f538d1b9", "path": "ModernFolderView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43510457616bf3080d76327f1f89c7b77d02", "path": "MonitoringDashboardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351f0a11f59dd75c30d028eac4ff9d38092", "path": "NewBrowserHistoryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435134146e205816d68f3b0c665fad9ad63d", "path": "NewWebBrowserView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351a2b96dacb9ea49cac1ecd8a388ea2d60", "path": "OptimizedBrowserSettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351cc654ea380e24994a47f4e292456835c", "path": "PermissionManagementView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43518418db0ee602fc2f90176cf7ed6ea920", "path": "SearchEngineSelectionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351fb212b3aceb1e5d07b457c9d91993494", "path": "SecuritySettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351939ae09ea979cf55055480c3ab4d7a40", "path": "SecurityStatisticsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351b5dd73a49c43ab7241c231f5cbbf296e", "path": "ServiceStatusView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351aa9b4269c521208b3c75f064e1eccb23", "path": "SettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351ab5dae528a50f8e5c9f813ff010d3c85", "path": "StatisticsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351ee39a0d03d0d0ec11dc7cd1afd245a23", "path": "TabSwitcherView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351b6061c37d99eaca5dcdb9f390d5d8971", "path": "UserAgentSelectionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351bcb2e1a081e2515004613558dd768070", "path": "WebsiteDataDetailView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da4351ee854c52ff74f718590077ed1ab5bc4c", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "1a64e23dc8778e0523c4c28951da4351b349574612ec3cb43311392ac4bd2d09", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351616c8f893ab007c680e78153e5190677", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351d2acd7e00227c60d6b5467b630d8189c", "path": "copApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da4351a91ed9e21a5699d5a152d25e8c777f3f", "name": "cop", "path": "cop", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351ce537a7bb77ff566bbfe8f9d00f6ca61", "path": "copTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da4351c1d17f859af2e51b5c92647a911dcd1a", "name": "copTests", "path": "copTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351bb812ce4fc727b29d80cd276e29e5ed0", "path": "copUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351df738b2d46cfad4035b4f8f23e9f2e57", "path": "copUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da4351f220fd28ff7d83bac9d5e1de7b3e3069", "name": "copUITests", "path": "copUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "1a64e23dc8778e0523c4c28951da4351c4ed80bba9a4b142149cd23873d181ff", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "1a64e23dc8778e0523c4c28951da4351d163398ea6c5ad2f46f55788b928f347", "name": "cop", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "1a64e23dc8778e0523c4c28951da4351", "path": "/Users/<USER>/Documents/Work/Xcode/cop/cop.xcodeproj", "projectDirectory": "/Users/<USER>/Documents/Work/Xcode/cop", "targets": ["TARGET@v11_hash=28d05c5b22eb6433cafbf649e69afa8e", "TARGET@v11_hash=a2ec0411485c6c271c706c6fa2742760", "TARGET@v11_hash=a70802a901fc98269eb66385b4586a38"]}