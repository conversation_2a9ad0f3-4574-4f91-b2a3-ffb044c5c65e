{"buildConfigurations": [{"buildSettings": {"CLANG_ENABLE_OBJC_ARC": "YES", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "", "COMPILER_WORKING_DIRECTORY": "$(WORKSPACE_DIR)", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_NS_ASSERTIONS": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_TESTING_SEARCH_PATHS": "YES", "ENTITLEMENTS_REQUIRED": "NO", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "SWIFT_PACKAGE", "DEBUG=1"], "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "KEEP_PRIVATE_EXTERNS": "NO", "MACOSX_DEPLOYMENT_TARGET": "10.13", "ONLY_ACTIVE_ARCH": "YES", "OTHER_CFLAGS": ["$(inherited)", "-DXcode"], "OTHER_LDRFLAGS": [], "OTHER_SWIFT_FLAGS": ["$(inherited)", "-DXcode"], "PRODUCT_NAME": "$(TARGET_NAME)", "SKIP_CLANG_STATIC_ANALYZER": "YES", "SKIP_INSTALL": "YES", "SUPPORTED_PLATFORMS": ["$(AVAILABLE_PLATFORMS)"], "SUPPRESS_WARNINGS": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": ["$(inherited)", "SWIFT_PACKAGE", "DEBUG"], "SWIFT_INSTALL_OBJC_HEADER": "NO", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2,3,4,6,7", "TVOS_DEPLOYMENT_TARGET": "12.0", "USE_HEADERMAP": "NO", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::BUILDCONFIG_0", "impartedBuildProperties": {"buildSettings": {}}, "name": "Debug"}, {"buildSettings": {"CLANG_ENABLE_OBJC_ARC": "YES", "CODE_SIGNING_REQUIRED": "NO", "CODE_SIGN_IDENTITY": "", "COMPILER_WORKING_DIRECTORY": "$(WORKSPACE_DIR)", "COPY_PHASE_STRIP": "YES", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DRIVERKIT_DEPLOYMENT_TARGET": "19.0", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_TESTING_SEARCH_PATHS": "YES", "ENTITLEMENTS_REQUIRED": "NO", "GCC_OPTIMIZATION_LEVEL": "s", "GCC_PREPROCESSOR_DEFINITIONS": ["$(inherited)", "SWIFT_PACKAGE"], "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "IPHONEOS_DEPLOYMENT_TARGET[__platform_filter=ios-maccatalyst]": ["13.0"], "KEEP_PRIVATE_EXTERNS": "NO", "MACOSX_DEPLOYMENT_TARGET": "10.13", "OTHER_CFLAGS": ["$(inherited)", "-DXcode"], "OTHER_LDRFLAGS": [], "OTHER_SWIFT_FLAGS": ["$(inherited)", "-DXcode"], "PRODUCT_NAME": "$(TARGET_NAME)", "SKIP_CLANG_STATIC_ANALYZER": "YES", "SKIP_INSTALL": "YES", "SUPPORTED_PLATFORMS": ["$(AVAILABLE_PLATFORMS)"], "SUPPRESS_WARNINGS": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": ["$(inherited)", "SWIFT_PACKAGE"], "SWIFT_INSTALL_OBJC_HEADER": "NO", "SWIFT_OBJC_INTERFACE_HEADER_NAME": "", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2,3,4,6,7", "TVOS_DEPLOYMENT_TARGET": "12.0", "USE_HEADERMAP": "NO", "WATCHOS_DEPLOYMENT_TARGET": "5.0", "XROS_DEPLOYMENT_TARGET": "1.0"}, "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::BUILDCONFIG_1", "impartedBuildProperties": {"buildSettings": {}}, "name": "Release"}], "defaultConfigurationName": "Release", "groupTree": {"children": [{"children": [], "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_0", "name": "AdditionalFiles", "path": "/", "sourceTree": "<absolute>", "type": "group"}, {"children": [], "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_1", "name": "Binaries", "path": "/", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_2::REF_0", "path": "BuildEngineCommand.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_2::REF_1", "path": "ConvertCommand.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_2::REF_2", "path": "Utils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_2::REF_3", "path": "main.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_2", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/CommandLineWrapper", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/CommandLineWrapper", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_3::REF_0", "path": "main.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_3", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FileLockTester", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FileLockTester", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_4::REF_0", "path": "BuildEngineCommand.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_4::REF_1", "path": "ConvertCommand.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_4::REF_2", "path": "Utils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_4::REF_3", "path": "main.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_4", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/CommandLineWrapper", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/CommandLineWrapper", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_0", "path": "Compiler/BlockerEntry.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_1", "path": "Compiler/BlockerEntryEncoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_2", "path": "Compiler/BlockerEntryFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_3", "path": "Compiler/CompilationResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_4", "path": "Compiler/Compiler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_5", "path": "Compiler/SafariCbBuilder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_6", "path": "Compiler/SafariRegex.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_7", "path": "ContentBlockerConverter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_8", "path": "ContentBlockerConverterVersion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_9", "path": "ConversionResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_10", "path": "Rules/Chars.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_11", "path": "Rules/CosmeticRule.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_12", "path": "Rules/CosmeticRuleMarker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_13", "path": "Rules/NetworkRule.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_14", "path": "Rules/NetworkRuleParser.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_15", "path": "Rules/Rule.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_16", "path": "Rules/RuleConverter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_17", "path": "Rules/RuleFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_18", "path": "Rules/SafariVersion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_19", "path": "Rules/ScriptletParser.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_20", "path": "Rules/SimpleRegex.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_21", "path": "Rules/SyntaxError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_22", "path": "Utils/ArrayExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_23", "path": "Utils/DomainUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_24", "path": "Utils/ErrorsCounter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_25", "path": "Utils/Logger.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_26", "path": "Utils/PrefixMatcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_27", "path": "Utils/StringExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5::REF_28", "path": "WebExtensionHelpers.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_5", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_0", "path": "Compiler/BlockerEntry.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_1", "path": "Compiler/BlockerEntryEncoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_2", "path": "Compiler/BlockerEntryFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_3", "path": "Compiler/CompilationResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_4", "path": "Compiler/Compiler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_5", "path": "Compiler/SafariCbBuilder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_6", "path": "Compiler/SafariRegex.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_7", "path": "ContentBlockerConverter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_8", "path": "ContentBlockerConverterVersion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_9", "path": "ConversionResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_10", "path": "Rules/Chars.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_11", "path": "Rules/CosmeticRule.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_12", "path": "Rules/CosmeticRuleMarker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_13", "path": "Rules/NetworkRule.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_14", "path": "Rules/NetworkRuleParser.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_15", "path": "Rules/Rule.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_16", "path": "Rules/RuleConverter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_17", "path": "Rules/RuleFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_18", "path": "Rules/SafariVersion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_19", "path": "Rules/ScriptletParser.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_20", "path": "Rules/SimpleRegex.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_21", "path": "Rules/SyntaxError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_22", "path": "Utils/ArrayExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_23", "path": "Utils/DomainUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_24", "path": "Utils/ErrorsCounter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_25", "path": "Utils/Logger.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_26", "path": "Utils/PrefixMatcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_27", "path": "Utils/StringExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6::REF_28", "path": "WebExtensionHelpers.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_6", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_7::REF_0", "path": "main.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_7", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FileLockTester", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FileLockTester", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_8::REF_0", "path": "FilterEngine.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_8::REF_1", "path": "FilterEngineSerialization.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_8::REF_2", "path": "FilterRule.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_8::REF_3", "path": "FilterRuleSerialization.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_8::REF_4", "path": "FilterRuleStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_8::REF_5", "path": "Request.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_8::REF_6", "path": "Schema.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_8::REF_7", "path": "Utils/ByteArrayTrie.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_8::REF_8", "path": "Utils/FileLock.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_8::REF_9", "path": "Utils/TrieNode.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_8::REF_10", "path": "WebExtension.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_8", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine", "sourceTree": "<absolute>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_0", "path": "FilterEngine.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_1", "path": "FilterEngineSerialization.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_2", "path": "FilterRule.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_3", "path": "FilterRuleSerialization.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_4", "path": "FilterRuleStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_5", "path": "Request.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_6", "path": "Schema.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_7", "path": "Utils/ByteArrayTrie.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_8", "path": "Utils/FileLock.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_9", "path": "Utils/TrieNode.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9::REF_10", "path": "WebExtension.swift", "sourceTree": "<group>", "type": "file"}], "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP::REF_9", "name": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine", "sourceTree": "<absolute>", "type": "group"}], "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git::MAINGROUP", "name": "", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "PACKAGE:https://github.com/AdguardTeam/SafariConverterLib.git", "path": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Package.swift", "projectDirectory": "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib", "projectIsPackage": "true", "projectName": "ContentBlockerConverter", "targets": ["TARGET@v12_hash=a126b9f112ab1e88afaeceed1c587703", "TARGET@v12_hash=3ccad6a10fa8df0a2b49f761cbec17a4", "TARGET@v12_hash=dbe27fe73502ec77228bd0be92ede778", "TARGET@v12_hash=d42d16cbaf80f5bbc569f93458349c22", "TARGET@v12_hash=9e783a7f91e8829f1910b923639583d4", "TARGET@v12_hash=9d318c37cfc6648dfceaf8638effa5b4", "TARGET@v12_hash=6e0b4fd0e377f9440b48b3e6e49870a7", "TARGET@v12_hash=325f76c0d53d0474a5b86868aea84d80", "TARGET@v12_hash=8232cda1145d4444474b98c9ab3c1a11", "TARGET@v12_hash=16ee1e6f6901fe2e459cff49f68dcd1b"]}