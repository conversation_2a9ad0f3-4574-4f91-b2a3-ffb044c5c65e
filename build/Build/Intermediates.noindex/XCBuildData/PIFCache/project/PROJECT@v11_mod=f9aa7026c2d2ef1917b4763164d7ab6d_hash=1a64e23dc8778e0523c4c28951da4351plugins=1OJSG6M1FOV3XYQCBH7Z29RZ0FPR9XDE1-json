{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.4", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "1a64e23dc8778e0523c4c28951da4351f40d9edb8acf498868777466b43b978c", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.4", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "1a64e23dc8778e0523c4c28951da4351cb7cf318880b6b12b04b6ee6afe2265d", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351428bba8216759bdc0ddf11598bdf85b8", "path": "MediaFile.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351e11057713c4b7c5b13d0c6526bf1bb1f", "path": "SecurityModels.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da4351b15edfa09c5b196c5c17ed71ca7df827", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43512851df22ca8a3f3ee7f69fba6790f7d6", "path": "ContentBlockerManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43516d1e28c9c931687fc3accd8a1a6bcbe7", "path": "EasyListConverter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43510c8cbcaedcca74b59a1d97ed1a8a7392", "path": "MediaImportService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351e620f2ae9fb0fc1a93bb1e4ccea36045", "path": "SecurityService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435170eddcfb537a4ef3662824656fd55afc", "path": "ServiceIntegrationConfig.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43511d3d28f9d82de18feb3b5fe3f32359b1", "path": "ServiceManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43510c1084e55a28846f68392a4c6c702b83", "path": "SystemErrorHandler.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da43511113dc7aee8ff5c45bf9b80bc41f270a", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43511b5053fd5cf314a6c1a5030454c80acf", "path": "BrowserManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43511cf26dfc927838fc59b2fd42402fc170", "path": "LogManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351fea3d9b4eacc097f17b24762821c3431", "path": "MediaFormatDetector.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43511b01107c9429d403a51deca126b82e5d", "path": "UnifiedMemoryManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da435146e5bf78f6e0e10e5ab8f6e02129f49e", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435135488d13aaf62a384f126e063aef4361", "path": "AdvancedStatsViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351631d28316ecd69d4ea71dd178db4ada6", "path": "FolderViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43515c70e1c64a16d894f731aeee7ec02c22", "path": "MediaLibraryViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351046cfd4deb000e0499b49b7ddeb607c1", "path": "MediaViewerState.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43516c8165cd2ff4c541749af98ed98e14ee", "path": "NewWebBrowserViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351afee0ab00df966446fa00013b3f27a5b", "path": "VideoPlayerService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da4351e108af484c5635187cc908e52f387103", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351e91da6f4c8342eb52b6625ee277f62da", "path": "AppComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435121e613aeea0736a4206388187f5985a1", "path": "BrowserUIComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351e86a2f54a10ad67d6585967f27e508b1", "path": "FullScreenGestureOverlay.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351f69d26724e9b2f4ef6bc62649141d20d", "path": "FullScreenNavigationBar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43517e696c1bfa51ebf80144e4d48eaa125e", "path": "FullScreenThumbnailBar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43513e7b3c04611014e9dd5bff805d3298ca", "path": "ImageContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351e4175f8d37683598d09d6dbaab1b65f4", "path": "MediaComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435144372456fc4ccc32ac4c55d32eee9474", "path": "MediaContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43513ce640a08aa6099ada160d606274b4d3", "path": "MediaLoadingAndErrorViews.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351140136f235a5ae8989f1bc7c82ceaab4", "path": "VideoPlayerView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da4351d8855f65b5351a54f078c60691fdfcda", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435134f9411786038fc99d59208c5debdd78", "path": "AppDesignSystem.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da4351ca8bde5f18887cc89fdbedf7614bca98", "name": "DesignSystem", "path": "DesignSystem", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351ec1e5103b0b3e1d9da6a89904963d650", "path": "AdBlockTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351ba7ee326fd2d7f3dc428b62bec6a8654", "path": "AdvancedStatsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351117a1eae1c5735cdfba494eb9019b940", "path": "ContentBlockerSettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43519279aada639933b1d35254217d03c63a", "path": "DataManagementView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351f48a155b26228417aec0554ee4ff5bbc", "path": "DocumentPickerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351c06954cca78581d98a52d6364dd7ace7", "path": "FullScreenMediaViewer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43516d6048fe5f16c6cdb166dce29593c1d7", "path": "ImportProgressView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43517ddf9e6bc9d184b8de2f356e12ff6a6b", "path": "MainView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351da8ebda8205447795d6e7653bb50346e", "path": "MediaDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43518cdba2ad81415544f29ff294fa473a38", "path": "ModernFolderView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435195ca61b654b1599380ba413aef6dfd72", "path": "MonitoringDashboardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43517e048d9ed4266a3902b2c5144bf5f59f", "path": "NewBrowserHistoryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435189cc187de9bb9e83e3eee4e67d4fbed5", "path": "NewWebBrowserView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351bdeaeb0cd776ec50223e50f21dbf16e3", "path": "OptimizedBrowserSettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435191faee94ffcf4b641837f04000f913b2", "path": "PermissionManagementView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351e01112fc92ea12f9365a42b841473947", "path": "SearchEngineSelectionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43514d30c4169699668faf4a9cd6aff62dd9", "path": "SecuritySettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351b4860d120a8fba2b1f2367ffedaff6ed", "path": "SecurityStatisticsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351b55f3f0b2aefa7de15f3d9f20f39c0e2", "path": "ServiceStatusView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351aa469735077634a93d8ba453a30b6243", "path": "SettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435178c3fdf66d200e64ed03aa51cf505a7e", "path": "StatisticsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43510f1ce62ad14d741b45cfcc0fd91a10a3", "path": "TabSwitcherView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da4351db8986d37513ddc8c48c90fe44f20e6e", "path": "UserAgentSelectionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43514b9aca774dbd637b5c3967df92e72fd5", "path": "WebsiteDataDetailView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da4351b1df688c12a34a111342c123eb935680", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "1a64e23dc8778e0523c4c28951da435133079eb0b70846c7c9df3309d06476c6", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435149e6da2c7170cac7e0dd20ae3617543f", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43510f831291d9501fb918f8d1d6742a8395", "path": "copApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da4351a91ed9e21a5699d5a152d25e8c777f3f", "name": "cop", "path": "cop", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43510ddddf3d2e179a66b24ffca5a23a3ec5", "path": "copTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da4351c1d17f859af2e51b5c92647a911dcd1a", "name": "copTests", "path": "copTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da435104149432a60ec36324575c6df69b3c50", "path": "copUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "1a64e23dc8778e0523c4c28951da43514a9ab597dab48cffbacab3fcdf419b22", "path": "copUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "1a64e23dc8778e0523c4c28951da4351f220fd28ff7d83bac9d5e1de7b3e3069", "name": "copUITests", "path": "copUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "1a64e23dc8778e0523c4c28951da4351c4ed80bba9a4b142149cd23873d181ff", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "1a64e23dc8778e0523c4c28951da4351d163398ea6c5ad2f46f55788b928f347", "name": "cop", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "1a64e23dc8778e0523c4c28951da4351", "path": "/Users/<USER>/Documents/Work/Xcode/cop/cop.xcodeproj", "projectDirectory": "/Users/<USER>/Documents/Work/Xcode/cop", "targets": ["TARGET@v11_hash=f435be15d6c07dac6c402c4e70d1413b", "TARGET@v11_hash=819910e251c53c4f0d62a98effd2147c", "TARGET@v11_hash=8556de509e442c363d06fecb216b6145"]}