{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "1a64e23dc8778e0523c4c28951da4351f473738b070a3e18fb167ccfff689ae8"}], "containerPath": "/Users/<USER>/Documents/Work/Xcode/cop/cop.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.4", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Products", "derivedDataPath": "/Users/<USER>/Documents/Work/Xcode/cop/build", "indexDataStoreFolderPath": "/Users/<USER>/Documents/Work/Xcode/cop/build/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Documents/Work/Xcode/cop/build/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPad16,2", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "18.4", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPad16,2", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "CLANG_COVERAGE_MAPPING": "YES", "CLANG_PROFILE_DATA_DIRECTORY": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/ProfileData", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "108", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES", "TARGET_DEVICE_IDENTIFIER": "05FAFAB3-CF34-462C-9348-EE9E49E3782A", "TARGET_DEVICE_MODEL": "iPad16,2", "TARGET_DEVICE_OS_VERSION": "18.4", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}