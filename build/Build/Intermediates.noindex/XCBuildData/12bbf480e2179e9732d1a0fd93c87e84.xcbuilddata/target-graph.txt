Target dependency graph (9 targets)
Target 'cop' in project 'cop'
➜ Explicit dependency on target 'ContentBlockerConverter' in project 'ContentBlockerConverter'
Target 'ContentBlockerConverter' in project 'ContentBlockerConverter'
➜ Explicit dependency on target 'ContentBlockerConverter' in project 'ContentBlockerConverter'
➜ Explicit dependency on target 'FilterEngine' in project 'ContentBlockerConverter'
➜ Explicit dependency on target 'Punycode' in project 'Punycode'
➜ Explicit dependency on target 'PublicSuffixList' in project 'swift-psl'
Target 'FilterEngine' in project 'ContentBlockerConverter'
➜ Explicit dependency on target 'ContentBlockerConverter' in project 'ContentBlockerConverter'
➜ Explicit dependency on target 'Punycode' in project 'Punycode'
➜ Explicit dependency on target 'PublicSuffixList' in project 'swift-psl'
Target 'PublicSuffixList' in project 'swift-psl'
➜ Explicit dependency on target 'PublicSuffixList' in project 'swift-psl'
➜ Explicit dependency on target 'swift-psl_PublicSuffixList' in project 'swift-psl'
Target 'PublicSuffixList' in project 'swift-psl'
➜ Explicit dependency on target 'swift-psl_PublicSuffixList' in project 'swift-psl'
Target 'swift-psl_PublicSuffixList' in project 'swift-psl' (no dependencies)
Target 'ContentBlockerConverter' in project 'ContentBlockerConverter'
➜ Explicit dependency on target 'Punycode' in project 'Punycode'
Target 'Punycode' in project 'Punycode'
➜ Explicit dependency on target 'Punycode' in project 'Punycode'
Target 'Punycode' in project 'Punycode' (no dependencies)