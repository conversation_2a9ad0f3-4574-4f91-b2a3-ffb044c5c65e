{"": {"diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter-master.dia", "emit-module-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter-master.swiftdeps"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/BlockerEntry.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntry.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntry.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntry.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntry.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntry.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntry.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntry.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntry~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/BlockerEntryEncoder.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryEncoder.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryEncoder.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryEncoder.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryEncoder.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryEncoder.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryEncoder.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryEncoder.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryEncoder~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/BlockerEntryFactory.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryFactory.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryFactory.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryFactory.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryFactory.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryFactory.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryFactory.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryFactory.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/BlockerEntryFactory~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/CompilationResult.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CompilationResult.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CompilationResult.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CompilationResult.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CompilationResult.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CompilationResult.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CompilationResult.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CompilationResult.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CompilationResult~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/Compiler.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Compiler.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Compiler.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Compiler.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Compiler.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Compiler.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Compiler.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Compiler.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Compiler~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/SafariCbBuilder.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariCbBuilder.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariCbBuilder.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariCbBuilder.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariCbBuilder.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariCbBuilder.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariCbBuilder.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariCbBuilder.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariCbBuilder~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/SafariRegex.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariRegex.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariRegex.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariRegex.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariRegex.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariRegex.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariRegex.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariRegex.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariRegex~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/ContentBlockerConverter.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/ContentBlockerConverterVersion.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverterVersion.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverterVersion.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverterVersion.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverterVersion.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverterVersion.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverterVersion.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverterVersion.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverterVersion~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/ConversionResult.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ConversionResult.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ConversionResult.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ConversionResult.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ConversionResult.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ConversionResult.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ConversionResult.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ConversionResult.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ConversionResult~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/Chars.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Chars.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Chars.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Chars.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Chars.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Chars.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Chars.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Chars.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Chars~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/CosmeticRule.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRule.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRule.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRule.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRule.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRule.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRule.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRule.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRule~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/CosmeticRuleMarker.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRuleMarker.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRuleMarker.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRuleMarker.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRuleMarker.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRuleMarker.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRuleMarker.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRuleMarker.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/CosmeticRuleMarker~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/NetworkRule.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRule.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRule.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRule.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRule.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRule.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRule.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRule.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRule~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/NetworkRuleParser.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRuleParser.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRuleParser.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRuleParser.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRuleParser.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRuleParser.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRuleParser.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRuleParser.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/NetworkRuleParser~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/Rule.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Rule.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Rule.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Rule.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Rule.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Rule.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Rule.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Rule.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Rule~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/RuleConverter.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleConverter.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleConverter.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleConverter.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleConverter.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleConverter.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleConverter.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleConverter.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleConverter~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/RuleFactory.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleFactory.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleFactory.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleFactory.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleFactory.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleFactory.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleFactory.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleFactory.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/RuleFactory~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/SafariVersion.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariVersion.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariVersion.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariVersion.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariVersion.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariVersion.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariVersion.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariVersion.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariVersion~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/ScriptletParser.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ScriptletParser.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ScriptletParser.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ScriptletParser.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ScriptletParser.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ScriptletParser.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ScriptletParser.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ScriptletParser.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ScriptletParser~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/SimpleRegex.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SimpleRegex.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SimpleRegex.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SimpleRegex.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SimpleRegex.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SimpleRegex.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SimpleRegex.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SimpleRegex.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SimpleRegex~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/SyntaxError.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SyntaxError.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SyntaxError.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SyntaxError.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SyntaxError.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SyntaxError.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SyntaxError.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SyntaxError.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SyntaxError~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/ArrayExtension.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ArrayExtension.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ArrayExtension.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ArrayExtension.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ArrayExtension.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ArrayExtension.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ArrayExtension.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ArrayExtension.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ArrayExtension~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/DomainUtils.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/DomainUtils.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/DomainUtils.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/DomainUtils.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/DomainUtils.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/DomainUtils.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/DomainUtils.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/DomainUtils.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/DomainUtils~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/ErrorsCounter.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ErrorsCounter.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ErrorsCounter.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ErrorsCounter.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ErrorsCounter.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ErrorsCounter.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ErrorsCounter.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ErrorsCounter.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ErrorsCounter~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/Logger.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Logger.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Logger.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Logger.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Logger.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Logger.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Logger.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Logger.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Logger~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/PrefixMatcher.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/PrefixMatcher.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/PrefixMatcher.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/PrefixMatcher.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/PrefixMatcher.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/PrefixMatcher.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/PrefixMatcher.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/PrefixMatcher.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/PrefixMatcher~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/StringExtension.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/StringExtension.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/StringExtension.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/StringExtension.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/StringExtension.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/StringExtension.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/StringExtension.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/StringExtension.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/StringExtension~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/WebExtensionHelpers.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/WebExtensionHelpers.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/WebExtensionHelpers.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/WebExtensionHelpers.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/WebExtensionHelpers.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/WebExtensionHelpers.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/WebExtensionHelpers.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/WebExtensionHelpers.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/WebExtensionHelpers~partial.swiftmodule"}}