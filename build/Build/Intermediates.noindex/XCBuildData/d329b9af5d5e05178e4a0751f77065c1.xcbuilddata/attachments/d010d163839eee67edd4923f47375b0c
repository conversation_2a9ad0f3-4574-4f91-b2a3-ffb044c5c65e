{"": {"diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/cop-master.dia", "emit-module-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/cop-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/cop-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/cop-master.swiftdeps"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/ContentView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Models/MediaFile.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFile.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFile.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFile.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFile.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFile.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFile.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFile.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFile~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Models/SecurityModels.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityModels.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityModels.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityModels.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityModels.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityModels.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityModels.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityModels.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityModels~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/ContentBlockerManager.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerManager.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerManager.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerManager.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerManager.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerManager.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerManager~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/EasyListConverter.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/EasyListConverter.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/EasyListConverter.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/EasyListConverter.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/EasyListConverter.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/EasyListConverter.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/EasyListConverter.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/EasyListConverter.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/EasyListConverter~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/MediaImportService.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaImportService.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaImportService.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaImportService.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaImportService.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaImportService.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaImportService.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaImportService.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaImportService~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/SecurityService.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityService.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityService.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityService.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityService.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityService.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityService.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityService.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityService~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/ServiceIntegrationConfig.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceIntegrationConfig.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceIntegrationConfig.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceIntegrationConfig.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceIntegrationConfig.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceIntegrationConfig.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceIntegrationConfig.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceIntegrationConfig.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceIntegrationConfig~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/ServiceManager.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceManager.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceManager.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceManager.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceManager.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceManager.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceManager~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Services/SystemErrorHandler.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SystemErrorHandler.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SystemErrorHandler.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SystemErrorHandler.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SystemErrorHandler.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SystemErrorHandler.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SystemErrorHandler.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SystemErrorHandler.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SystemErrorHandler~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Utils/BrowserManager.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserManager.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserManager.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserManager.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserManager.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserManager.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserManager~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Utils/LogManager.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/LogManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/LogManager.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/LogManager.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/LogManager.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/LogManager.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/LogManager.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/LogManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/LogManager~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Utils/MediaFormatDetector.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFormatDetector.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFormatDetector.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFormatDetector.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFormatDetector.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFormatDetector.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFormatDetector.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFormatDetector.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaFormatDetector~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Utils/UnifiedMemoryManager.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UnifiedMemoryManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UnifiedMemoryManager.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UnifiedMemoryManager.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UnifiedMemoryManager.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UnifiedMemoryManager.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UnifiedMemoryManager.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UnifiedMemoryManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UnifiedMemoryManager~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/AdvancedStatsViewModel.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsViewModel.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsViewModel.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsViewModel.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsViewModel.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsViewModel.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsViewModel~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/FolderViewModel.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FolderViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FolderViewModel.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FolderViewModel.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FolderViewModel.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FolderViewModel.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FolderViewModel.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FolderViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FolderViewModel~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/MediaLibraryViewModel.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLibraryViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLibraryViewModel.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLibraryViewModel.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLibraryViewModel.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLibraryViewModel.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLibraryViewModel.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLibraryViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLibraryViewModel~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/MediaViewerState.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaViewerState.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaViewerState.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaViewerState.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaViewerState.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaViewerState.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaViewerState.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaViewerState.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaViewerState~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/NewWebBrowserViewModel.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserViewModel.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserViewModel.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserViewModel.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserViewModel.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserViewModel.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserViewModel~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/ViewModels/VideoPlayerService.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerService.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerService.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerService.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerService.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerService.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerService.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerService.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerService~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/AdBlockTestView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdBlockTestView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdBlockTestView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdBlockTestView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdBlockTestView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdBlockTestView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdBlockTestView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdBlockTestView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdBlockTestView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/AdvancedStatsView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AdvancedStatsView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/AppComponents.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppComponents.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppComponents.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppComponents.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppComponents.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppComponents.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppComponents~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/BrowserUIComponents.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserUIComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserUIComponents.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserUIComponents.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserUIComponents.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserUIComponents.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserUIComponents.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserUIComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/BrowserUIComponents~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/FullScreenGestureOverlay.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenGestureOverlay.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenGestureOverlay.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenGestureOverlay.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenGestureOverlay.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenGestureOverlay.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenGestureOverlay.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenGestureOverlay.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenGestureOverlay~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/FullScreenNavigationBar.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenNavigationBar.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenNavigationBar.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenNavigationBar.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenNavigationBar.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenNavigationBar.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenNavigationBar.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenNavigationBar.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenNavigationBar~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/FullScreenThumbnailBar.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenThumbnailBar.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenThumbnailBar.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenThumbnailBar.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenThumbnailBar.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenThumbnailBar.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenThumbnailBar.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenThumbnailBar.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenThumbnailBar~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/ImageContentView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImageContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImageContentView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImageContentView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImageContentView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImageContentView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImageContentView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImageContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImageContentView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/MediaComponents.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaComponents.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaComponents.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaComponents.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaComponents.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaComponents.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaComponents~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/MediaContentView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaContentView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaContentView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaContentView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaContentView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaContentView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaContentView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/MediaLoadingAndErrorViews.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLoadingAndErrorViews.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLoadingAndErrorViews.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLoadingAndErrorViews.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLoadingAndErrorViews.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLoadingAndErrorViews.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLoadingAndErrorViews.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLoadingAndErrorViews.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaLoadingAndErrorViews~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/Components/VideoPlayerView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/VideoPlayerView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/ContentBlockerSettingsView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerSettingsView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerSettingsView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerSettingsView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerSettingsView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerSettingsView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerSettingsView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerSettingsView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ContentBlockerSettingsView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/DataManagementView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DataManagementView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DataManagementView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DataManagementView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DataManagementView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DataManagementView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DataManagementView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DataManagementView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DataManagementView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/DesignSystem/AppDesignSystem.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppDesignSystem.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppDesignSystem.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppDesignSystem.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppDesignSystem.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppDesignSystem.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppDesignSystem.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppDesignSystem.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/AppDesignSystem~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/DocumentPickerView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DocumentPickerView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DocumentPickerView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DocumentPickerView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DocumentPickerView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DocumentPickerView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DocumentPickerView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DocumentPickerView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/DocumentPickerView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/FullScreenMediaViewer.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenMediaViewer.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenMediaViewer.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenMediaViewer.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenMediaViewer.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenMediaViewer.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenMediaViewer.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenMediaViewer.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/FullScreenMediaViewer~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/ImportProgressView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImportProgressView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImportProgressView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImportProgressView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImportProgressView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImportProgressView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImportProgressView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImportProgressView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ImportProgressView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/MainView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MainView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MainView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MainView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MainView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MainView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MainView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MainView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MainView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/MediaDetailView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaDetailView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaDetailView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaDetailView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaDetailView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaDetailView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaDetailView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaDetailView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MediaDetailView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/ModernFolderView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ModernFolderView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ModernFolderView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ModernFolderView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ModernFolderView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ModernFolderView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ModernFolderView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ModernFolderView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ModernFolderView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/MonitoringDashboardView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MonitoringDashboardView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MonitoringDashboardView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MonitoringDashboardView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MonitoringDashboardView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MonitoringDashboardView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MonitoringDashboardView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MonitoringDashboardView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/MonitoringDashboardView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/NewBrowserHistoryView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewBrowserHistoryView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewBrowserHistoryView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewBrowserHistoryView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewBrowserHistoryView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewBrowserHistoryView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewBrowserHistoryView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewBrowserHistoryView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewBrowserHistoryView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/NewWebBrowserView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/NewWebBrowserView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/OptimizedBrowserSettingsView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/OptimizedBrowserSettingsView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/OptimizedBrowserSettingsView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/OptimizedBrowserSettingsView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/OptimizedBrowserSettingsView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/OptimizedBrowserSettingsView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/OptimizedBrowserSettingsView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/OptimizedBrowserSettingsView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/OptimizedBrowserSettingsView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/PermissionManagementView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/PermissionManagementView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/PermissionManagementView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/PermissionManagementView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/PermissionManagementView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/PermissionManagementView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/PermissionManagementView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/PermissionManagementView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/PermissionManagementView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/SearchEngineSelectionView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SearchEngineSelectionView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SearchEngineSelectionView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SearchEngineSelectionView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SearchEngineSelectionView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SearchEngineSelectionView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SearchEngineSelectionView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SearchEngineSelectionView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SearchEngineSelectionView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/SecuritySettingsView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecuritySettingsView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecuritySettingsView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecuritySettingsView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecuritySettingsView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecuritySettingsView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecuritySettingsView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecuritySettingsView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecuritySettingsView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/SecurityStatisticsView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityStatisticsView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityStatisticsView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityStatisticsView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityStatisticsView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityStatisticsView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityStatisticsView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityStatisticsView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SecurityStatisticsView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/ServiceStatusView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceStatusView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceStatusView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceStatusView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceStatusView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceStatusView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceStatusView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceStatusView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/ServiceStatusView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/SettingsView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SettingsView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SettingsView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SettingsView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SettingsView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SettingsView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SettingsView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SettingsView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/SettingsView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/StatisticsView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/StatisticsView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/StatisticsView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/StatisticsView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/StatisticsView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/StatisticsView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/StatisticsView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/StatisticsView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/StatisticsView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/TabSwitcherView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/TabSwitcherView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/TabSwitcherView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/TabSwitcherView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/TabSwitcherView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/TabSwitcherView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/TabSwitcherView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/TabSwitcherView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/TabSwitcherView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/UserAgentSelectionView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UserAgentSelectionView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UserAgentSelectionView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UserAgentSelectionView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UserAgentSelectionView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UserAgentSelectionView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UserAgentSelectionView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UserAgentSelectionView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/UserAgentSelectionView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/Views/WebsiteDataDetailView.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/WebsiteDataDetailView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/WebsiteDataDetailView.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/WebsiteDataDetailView.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/WebsiteDataDetailView.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/WebsiteDataDetailView.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/WebsiteDataDetailView.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/WebsiteDataDetailView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/WebsiteDataDetailView~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/cop/copApp.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/copApp.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/copApp.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/copApp.dia", "index-unit-output-path": "/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/copApp.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/copApp.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/copApp.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/copApp.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/cop.build/Debug-iphonesimulator/cop.build/Objects-normal/arm64/copApp~partial.swiftmodule"}}