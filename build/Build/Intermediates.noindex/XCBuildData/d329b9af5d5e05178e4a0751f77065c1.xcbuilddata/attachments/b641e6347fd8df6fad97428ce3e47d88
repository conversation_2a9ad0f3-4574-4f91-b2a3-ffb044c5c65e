{"": {"diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Punycode-master.dia", "emit-module-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Punycode-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Punycode-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Punycode-master.swiftdeps"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/PunycodeSwift/Sources/Extensions.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Extensions.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Extensions.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Extensions.dia", "index-unit-output-path": "/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Extensions.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Extensions.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Extensions.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Extensions.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Extensions~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/PunycodeSwift/Sources/Helpers.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Helpers.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Helpers.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Helpers.dia", "index-unit-output-path": "/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Helpers.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Helpers.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Helpers.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Helpers.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Helpers~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/PunycodeSwift/Sources/Punycode.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Punycode.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Punycode.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Punycode.dia", "index-unit-output-path": "/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Punycode.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Punycode.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Punycode.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Punycode.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/Punycode.build/Debug-iphonesimulator/Punycode.build/Objects-normal/arm64/Punycode~partial.swiftmodule"}}