{"": {"diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngine-master.dia", "emit-module-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngine-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngine-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngine-master.swiftdeps"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine/FilterEngine.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngine.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngine.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngine.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngine.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngine.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngine.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngine.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngine~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine/FilterEngineSerialization.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngineSerialization.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngineSerialization.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngineSerialization.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngineSerialization.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngineSerialization.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngineSerialization.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngineSerialization.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterEngineSerialization~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine/FilterRule.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRule.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRule.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRule.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRule.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRule.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRule.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRule.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRule~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine/FilterRuleSerialization.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleSerialization.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleSerialization.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleSerialization.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleSerialization.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleSerialization.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleSerialization.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleSerialization.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleSerialization~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine/FilterRuleStorage.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleStorage.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleStorage.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleStorage.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleStorage.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleStorage.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleStorage.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleStorage.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FilterRuleStorage~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine/Request.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Request.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Request.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Request.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Request.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Request.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Request.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Request.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Request~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine/Schema.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Schema.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Schema.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Schema.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Schema.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Schema.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Schema.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Schema.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/Schema~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine/Utils/ByteArrayTrie.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/ByteArrayTrie.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/ByteArrayTrie.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/ByteArrayTrie.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/ByteArrayTrie.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/ByteArrayTrie.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/ByteArrayTrie.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/ByteArrayTrie.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/ByteArrayTrie~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine/Utils/FileLock.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FileLock.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FileLock.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FileLock.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FileLock.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FileLock.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FileLock.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FileLock.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/FileLock~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine/Utils/TrieNode.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/TrieNode.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/TrieNode.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/TrieNode.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/TrieNode.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/TrieNode.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/TrieNode.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/TrieNode.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/TrieNode~partial.swiftmodule"}, "/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/FilterEngine/WebExtension.swift": {"const-values": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/WebExtension.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/WebExtension.d", "diagnostics": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/WebExtension.dia", "index-unit-output-path": "/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/WebExtension.o", "llvm-bc": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/WebExtension.bc", "object": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/WebExtension.o", "swift-dependencies": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/WebExtension.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/FilterEngine.build/Objects-normal/arm64/WebExtension~partial.swiftmodule"}}