"/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/SafariRegex.swift":
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariRegex.swiftdeps"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariRegex.d"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariRegex.swiftconstvalues"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariRegex.o"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/SafariRegex.dia"
"/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/ContentBlockerConverter.swift":
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter.swiftconstvalues"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter.o"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter.swiftdeps"
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter.d"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverter.dia"
"/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/ContentBlockerConverterVersion.swift":
  dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverterVersion.d"
  object: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverterVersion.o"
  swift-dependencies: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverterVersion.swiftdeps"
  diagnostics: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverterVersion.dia"
  const-values: "/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/ContentBlockerConverterVersion.swiftconstvalues"
