/Users/<USER>/Documents/Work/Xcode/cop/build/Build/Intermediates.noindex/ContentBlockerConverter.build/Debug-iphonesimulator/ContentBlockerConverter.build/Objects-normal/arm64/Logger.o : /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/Rule.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/CosmeticRule.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/NetworkRule.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/StringExtension.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/ArrayExtension.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/SafariVersion.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/ContentBlockerConverterVersion.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/SafariCbBuilder.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/BlockerEntryEncoder.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/Logger.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/PrefixMatcher.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/CosmeticRuleMarker.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/Compiler.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/NetworkRuleParser.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/ScriptletParser.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/ErrorsCounter.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/RuleConverter.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/ContentBlockerConverter.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/SyntaxError.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/DomainUtils.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/Chars.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/WebExtensionHelpers.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/ConversionResult.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/CompilationResult.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/SimpleRegex.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/SafariRegex.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/RuleFactory.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/BlockerEntryFactory.swift /Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/BlockerEntry.swift /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/XPC.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/ObjectiveC.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/unistd.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/sys_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/Dispatch.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_math.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_signal.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/System.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/CoreFoundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/Observation.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_stdio.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_errno.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_Builtin_float.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/Swift.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/SwiftOnoneSupport.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/XPC.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/ObjectiveC.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/unistd.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_time.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/sys_time.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Combine.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Dispatch.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_math.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_signal.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/System.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Foundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/CoreFoundation.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Observation.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_stdio.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_errno.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_Builtin_float.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Swift.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/SwiftOnoneSupport.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/include/XPC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/include/ObjectiveC.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/include/_time.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/usr/include/Dispatch.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk/System/Library/Frameworks/Security.framework/Headers/Security.apinotes /Users/<USER>/Documents/Work/Xcode/cop/build/Build/Products/Debug-iphonesimulator/Punycode.swiftmodule/arm64-apple-ios-simulator.swiftmodule
