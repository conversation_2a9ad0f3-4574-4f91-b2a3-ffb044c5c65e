/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/BlockerEntry.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/BlockerEntryEncoder.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/BlockerEntryFactory.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/CompilationResult.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/Compiler.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/SafariCbBuilder.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Compiler/SafariRegex.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/ContentBlockerConverter.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/ContentBlockerConverterVersion.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/ConversionResult.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/Chars.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/CosmeticRule.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/CosmeticRuleMarker.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/NetworkRule.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/NetworkRuleParser.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/Rule.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/RuleConverter.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/RuleFactory.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/SafariVersion.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/ScriptletParser.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/SimpleRegex.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Rules/SyntaxError.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/ArrayExtension.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/DomainUtils.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/ErrorsCounter.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/Logger.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/PrefixMatcher.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/Utils/StringExtension.swift
/Users/<USER>/Documents/Work/Xcode/cop/build/SourcePackages/checkouts/SafariConverterLib/Sources/ContentBlockerConverter/WebExtensionHelpers.swift
