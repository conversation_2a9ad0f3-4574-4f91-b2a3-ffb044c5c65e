<!DOCTYPE html>
<html lang="en">
  <head>
    <title>String Extension Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Extension/String" class="dashAnchor"></a>

    <a title="String Extension Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          Punycode 3.0.0 Docs
        </a>
        
      </p>
    
      <div class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </div>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="https://github.com/gumob/PunycodeSwift">
            <img class="header-icon" src="../img/gh.png" alt="GitHub"/>
            View on GitHub
          </a>
        </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="dash-feed://https%3A%2F%2Fgithub.com%2Fgumob%2FPunycodeSwift%2Ftree%2F3.0.0%2Fdocs%2Fdocsets%2FPunycode.xml">
            <img class="header-icon" src="../img/dash.png" alt="Dash"/>
            Install in Dash
          </a>
        </p>
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">Punycode</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      <a class="breadcrumb" href="../Extensions.html">Extensions</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      Swift.String Extension Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Puny.html">Puny</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/String.html">String</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Substring.html">Substring</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>String</h1>
              <div class="declaration">
                <div class="language">
                  
                  <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">extension</span> <span class="kt">String</span></code></pre>

                </div>
              </div>
            <p>This extension provides methods for encoding and decoding strings using Punycode (RFC 3492) 
and IDNA encoding. It allows for the conversion of String instances to their Punycode 
and IDNA representations, facilitating the handling of internationalized domain names.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:SS8PunycodeE15punycodeEncodedSSSgvp"></a>
                    <a name="//apple_ref/swift/Property/punycodeEncoded" class="dashAnchor"></a>
                    <a class="token" href="#/s:SS8PunycodeE15punycodeEncodedSSSgvp">punycodeEncoded</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Returns new string in punycode encoding (RFC 3492)</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">punycodeEncoded</span><span class="p">:</span> <span class="kt">String</span><span class="p">?</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>Punycode encoded string or nil if the string can&rsquo;t be encoded</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:SS8PunycodeE15punycodeDecodedSSSgvp"></a>
                    <a name="//apple_ref/swift/Property/punycodeDecoded" class="dashAnchor"></a>
                    <a class="token" href="#/s:SS8PunycodeE15punycodeDecodedSSSgvp">punycodeDecoded</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Returns new string decoded from punycode representation (RFC 3492)</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">punycodeDecoded</span><span class="p">:</span> <span class="kt">String</span><span class="p">?</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>Original string or nil if the string doesn&rsquo;t contain correct encoding</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:SS8PunycodeE11idnaEncodedSSSgvp"></a>
                    <a name="//apple_ref/swift/Property/idnaEncoded" class="dashAnchor"></a>
                    <a class="token" href="#/s:SS8PunycodeE11idnaEncodedSSSgvp">idnaEncoded</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Returns new string containing IDNA-encoded hostname</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">idnaEncoded</span><span class="p">:</span> <span class="kt">String</span><span class="p">?</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>IDNA encoded hostname or nil if the string can&rsquo;t be encoded</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:SS8PunycodeE11idnaDecodedSSSgvp"></a>
                    <a name="//apple_ref/swift/Property/idnaDecoded" class="dashAnchor"></a>
                    <a class="token" href="#/s:SS8PunycodeE11idnaDecodedSSSgvp">idnaDecoded</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Returns new string containing hostname decoded from IDNA representation</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="k">var</span> <span class="nv">idnaDecoded</span><span class="p">:</span> <span class="kt">String</span><span class="p">?</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>Original hostname or nil if the string doesn&rsquo;t contain correct encoding</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2024 <a class="link" href="https://github.com/gumob" target="_blank" rel="external noopener">Kojiro Futamura</a>. All rights reserved. (Last updated: 2024-08-28)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external noopener">jazzy ♪♫ v0.15.1</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external noopener">Realm</a> project.</p>
    </section>
  </body>
</html>
