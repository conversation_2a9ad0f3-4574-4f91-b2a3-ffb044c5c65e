{"Extensions/String.html#/s:SS8PunycodeE15punycodeEncodedSSSgvp": {"name": "punycodeEncoded", "abstract": "<p>Returns new string in punycode encoding (RFC 3492)</p>", "parent_name": "String"}, "Extensions/String.html#/s:SS8PunycodeE15punycodeDecodedSSSgvp": {"name": "punycodeDecoded", "abstract": "<p>Returns new string decoded from punycode representation (RFC 3492)</p>", "parent_name": "String"}, "Extensions/String.html#/s:SS8PunycodeE11idnaEncodedSSSgvp": {"name": "idnaEncoded", "abstract": "<p>Returns new string containing IDNA-encoded hostname</p>", "parent_name": "String"}, "Extensions/String.html#/s:SS8PunycodeE11idnaDecodedSSSgvp": {"name": "idnaDecoded", "abstract": "<p>Returns new string containing hostname decoded from IDNA representation</p>", "parent_name": "String"}, "Extensions/Substring.html#/s:Ss8PunycodeE15punycodeEncodedSSSgvp": {"name": "punycodeEncoded", "abstract": "<p>Returns new string in punycode encoding (RFC 3492)</p>", "parent_name": "Substring"}, "Extensions/Substring.html#/s:Ss8PunycodeE15punycodeDecodedSSSgvp": {"name": "punycodeDecoded", "abstract": "<p>Returns new string decoded from punycode representation (RFC 3492)</p>", "parent_name": "Substring"}, "Extensions/Substring.html#/s:Ss8PunycodeE11idnaEncodedSSSgvp": {"name": "idnaEncoded", "abstract": "<p>Returns new string containing IDNA-encoded hostname</p>", "parent_name": "Substring"}, "Extensions/Substring.html#/s:Ss8PunycodeE11idnaDecodedSSSgvp": {"name": "idnaDecoded", "abstract": "<p>Returns new string containing hostname decoded from IDNA representation</p>", "parent_name": "Substring"}, "Extensions/Substring.html": {"name": "Substring", "abstract": "<p>This extension provides methods for encoding and decoding strings using Punycode (RFC 3492)"}, "Extensions/String.html": {"name": "String", "abstract": "<p>This extension provides methods for encoding and decoding strings using Punycode (RFC 3492)"}, "Classes/Puny.html#/s:8Punycode4PunyC06decodeA0ySSSgSsF": {"name": "decodePunycode(_:)", "abstract": "<p>Decodes a punycode encoded string to its original representation.</p>", "parent_name": "Puny"}, "Classes/Puny.html#/s:8Punycode4PunyC06encodeA0ySSSgSsF": {"name": "encodePunycode(_:)", "abstract": "<p>Encodes a substring to punycode (RFC 3492).</p>", "parent_name": "Puny"}, "Classes/Puny.html#/s:8Punycode4PunyC10encodeIDNAySSSgSsF": {"name": "encodeIDNA(_:)", "abstract": "<p>Returns new string containing IDNA-encoded hostname.</p>", "parent_name": "Puny"}, "Classes/Puny.html#/s:8Punycode4PunyC11decodedIDNAySSSgSsF": {"name": "decodedIDNA(_:)", "abstract": "<p>Returns a new string containing the hostname decoded from IDNA representation.</p>", "parent_name": "Puny"}, "Classes/Puny.html": {"name": "Puny", "abstract": "<p>Puny class provides methods to encode and decode strings using Punycode (RFC 3492)."}, "Classes.html": {"name": "Classes", "abstract": "<p>The following classes are available globally.</p>"}, "Extensions.html": {"name": "Extensions", "abstract": "<p>The following extensions are available globally.</p>"}}