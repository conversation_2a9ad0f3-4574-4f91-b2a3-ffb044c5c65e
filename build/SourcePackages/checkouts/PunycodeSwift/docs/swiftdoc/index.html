<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Punycode  Reference</title>
    <link rel="stylesheet" type="text/css" href="css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="css/highlight.css" />
    <meta charset="utf-8">
    <script src="js/jquery.min.js" defer></script>
    <script src="js/jazzy.js" defer></script>
    
    <script src="js/lunr.min.js" defer></script>
    <script src="js/typeahead.jquery.js" defer></script>
    <script src="js/jazzy.search.js" defer></script>
  </head>
  <body>


    <a title="Punycode  Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="index.html">
          Punycode 3.0.0 Docs
        </a>
        
      </p>
    
      <div class="header-col--secondary">
        <form role="search" action="search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </div>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="https://github.com/gumob/PunycodeSwift">
            <img class="header-icon" src="img/gh.png" alt="GitHub"/>
            View on GitHub
          </a>
        </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="dash-feed://https%3A%2F%2Fgithub.com%2Fgumob%2FPunycodeSwift%2Ftree%2F3.0.0%2Fdocs%2Fdocsets%2FPunycode.xml">
            <img class="header-icon" src="img/dash.png" alt="Dash"/>
            Install in Dash
          </a>
        </p>
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="index.html">Punycode</a>
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Puny.html">Puny</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/String.html">String</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/Substring.html">Substring</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            
            <p><a href="https://github.com/gumob/PunycodeSwift"><img src="https://img.shields.io/badge/Swift_Package_Manager-compatible-orange" alt="Swift Package Manager compatible"></a>
<a href="https://github.com/gumob/PunycodeSwift"><img src="https://img.shields.io/badge/Carthage-compatible-4BC51D.svg" alt="Carthage compatible"></a>
<a href="https://cocoapods.org/pods/Punycode"><img src="https://img.shields.io/cocoapods/v/Punycode.svg" alt="Cocoapods Version"></a>
<a href="https://cocoadocs.org/docsets/Punycode"><img src="https://img.shields.io/cocoapods/p/Punycode.svg" alt="Cocoapods Platform"></a>
<a href="https://github.com/gumob/PunycodeSwift/actions/workflows/main.yml"><img src="https://github.com/gumob/PunycodeSwift/actions/workflows/main.yml/badge.svg" alt="Build"></a>
<a href="https://codecov.io/gh/gumob/PunycodeSwift"><img src="https://codecov.io/gh/gumob/PunycodeSwift/branch/master/graph/badge.svg" alt="codecov"></a>
<img src="https://img.shields.io/badge/Language-Swift%205.0-orange.svg" alt="Language">
<img src="https://img.shields.io/packagist/l/doctrine/orm.svg" alt="Packagist"></p>
<h1 id='punycodeswift' class='heading'>PunycodeSwift</h1>

<p><code>PunycodeSwift</code> is a pure Swift library to allows you to encode and decode <code>punycoded</code> strings by using String extension.</p>
<h2 id='what-is-punycode' class='heading'>What is Punycode?</h2>

<p>Punycode is a representation of Unicode with the limited ASCII character subset used for Internet host names. Using Punycode, host names containing Unicode characters are transcoded to a subset of ASCII consisting of letters, digits, and hyphen, which is called the Letter-Digit-Hyphen (LDH) subset. For example, München (German name for Munich) is encoded as Mnchen-3ya. <a href="https://en.wikipedia.org/wiki/Punycode">(Wikipedia)</a></p>
<h2 id='requirements' class='heading'>Requirements</h2>

<ul>
<li>macOS 10.13 or later</li>
<li>iOS 12.0 or later</li>
<li>tvOS 12.0 or later</li>
<li>watchOS 4.0 or later</li>
<li>visionOS 1.0 or later</li>
<li>Swift 5.0 or later</li>
</ul>
<h2 id='installation' class='heading'>Installation</h2>
<h3 id='swift-package-manager' class='heading'>Swift Package Manager</h3>

<p>Add the following to your <code>Package.swift</code> file.</p>

<ul>
<li><p>macOS, iOS, tvOS, watchOS, visionOS, and Swift 5</p>
<pre class="highlight swift"><code><span class="nv">dependencies</span><span class="p">:</span> <span class="p">[</span>
    <span class="o">.</span><span class="nf">package</span><span class="p">(</span><span class="nv">url</span><span class="p">:</span> <span class="s">"https://github.com/gumob/PunycodeSwift.git"</span><span class="p">,</span> <span class="o">.</span><span class="nf">upToNextMajor</span><span class="p">(</span><span class="nv">from</span><span class="p">:</span> <span class="s">"3.0.0"</span><span class="p">))</span>
<span class="p">]</span>
</code></pre></li>
<li><p>macOS, iOS, tvOS, and Swift 5</p>
<pre class="highlight swift"><code><span class="nv">dependencies</span><span class="p">:</span> <span class="p">[</span>
    <span class="o">.</span><span class="nf">package</span><span class="p">(</span><span class="nv">url</span><span class="p">:</span> <span class="s">"https://github.com/gumob/PunycodeSwift.git"</span><span class="p">,</span> <span class="o">.</span><span class="nf">upToNextMajor</span><span class="p">(</span><span class="nv">from</span><span class="p">:</span> <span class="s">"2.1.1"</span><span class="p">))</span>
<span class="p">]</span>
</code></pre></li>
</ul>
<h3 id='carthage' class='heading'>Carthage</h3>

<p>Add the following to your <code>Cartfile</code> and follow <a href="https://github.com/Carthage/Carthage#adding-frameworks-to-an-application">these instructions</a>.</p>

<ul>
<li><p>macOS, iOS, tvOS, watchOS, visionOS, and Swift 5</p>
<pre class="highlight plaintext"><code>github "gumob/PunycodeSwift" ~&gt; 3.0
</code></pre></li>
<li><p>macOS, iOS, tvOS, and Swift 5</p>
<pre class="highlight plaintext"><code>github "gumob/PunycodeSwift" ~&gt; 2.0
</code></pre></li>
<li><p>macOS, iOS, tvOS, and Swift 4</p>
<pre class="highlight plaintext"><code>github "gumob/PunycodeSwift" ~&gt; 1.0
</code></pre></li>
</ul>
<h3 id='cocoapods' class='heading'>CocoaPods</h3>

<p>To integrate TLDExtract into your project, add the following to your <code>Podfile</code>.</p>

<ul>
<li><p>macOS, iOS, tvOS, watchOS, visionOS, and Swift 5.0</p>
<pre class="highlight ruby"><code><span class="n">pod</span> <span class="s1">'Punycode'</span><span class="p">,</span> <span class="s1">'~&gt; 3.0'</span>
</code></pre></li>
<li><p>macOS, iOS, tvOS, and Swift 5.0</p>
<pre class="highlight ruby"><code><span class="n">pod</span> <span class="s1">'Punycode'</span><span class="p">,</span> <span class="s1">'~&gt; 2.0'</span>
</code></pre></li>
<li><p>macOS, iOS, tvOS, and Swift 4.2</p>
<pre class="highlight ruby"><code><span class="n">pod</span> <span class="s1">'Punycode'</span><span class="p">,</span> <span class="s1">'~&gt; 1.0'</span>
</code></pre></li>
</ul>
<h2 id='usage' class='heading'>Usage</h2>

<p>Full documentation is available at <a href="https://gumob.github.io/PunycodeSwift/swiftdoc/">https://gumob.github.io/PunycodeSwift/swiftdoc/</a>.</p>
<h3 id='encode-and-decode-idna' class='heading'>Encode and decode IDNA:</h3>
<pre class="highlight swift"><code><span class="kd">import</span> <span class="kt">Punycode</span>

<span class="k">var</span> <span class="nv">sushi</span><span class="p">:</span> <span class="kt">String</span> <span class="o">=</span> <span class="s">"寿司"</span>

<span class="n">sushi</span> <span class="o">=</span> <span class="n">sushi</span><span class="o">.</span><span class="n">idnaEncoded</span><span class="o">!</span>
<span class="nf">print</span><span class="p">(</span><span class="n">sushi</span><span class="p">)</span>  <span class="c1">// xn--sprr0q</span>

<span class="n">sushi</span> <span class="o">=</span> <span class="n">sushi</span><span class="o">.</span><span class="n">idnaDecoded</span><span class="o">!</span>
<span class="nf">print</span><span class="p">(</span><span class="n">sushi</span><span class="p">)</span>  <span class="c1">// "寿司"</span>
</code></pre>
<h3 id='encode-and-decode-punycode-directly' class='heading'>Encode and decode Punycode directly:</h3>
<pre class="highlight swift"><code><span class="kd">import</span> <span class="kt">Punycode</span>

<span class="k">var</span> <span class="nv">sushi</span><span class="p">:</span> <span class="kt">String</span> <span class="o">=</span> <span class="s">"寿司"</span>

<span class="n">sushi</span> <span class="o">=</span> <span class="n">sushi</span><span class="o">.</span><span class="n">punycodeEncoded</span><span class="o">!</span>
<span class="nf">print</span><span class="p">(</span><span class="n">sushi</span><span class="p">)</span>  <span class="c1">// sprr0q</span>

<span class="n">sushi</span> <span class="o">=</span> <span class="n">sushi</span><span class="o">.</span><span class="n">punycodeDecoded</span><span class="o">!</span>
<span class="nf">print</span><span class="p">(</span><span class="n">sushi</span><span class="p">)</span>  <span class="c1">// "寿司"</span>
</code></pre>
<h2 id='copyright' class='heading'>Copyright</h2>

<p>Punycode is released under MIT license, which means you can modify it, redistribute it or use it however you like.</p>

          </div>
        </section>


      </article>
    </div>
    <section class="footer">
      <p>&copy; 2024 <a class="link" href="https://github.com/gumob" target="_blank" rel="external noopener">Kojiro Futamura</a>. All rights reserved. (Last updated: 2024-08-28)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external noopener">jazzy ♪♫ v0.15.1</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external noopener">Realm</a> project.</p>
    </section>
  </body>
</html>
