{"fileScopedDeclarationPrivacy": {"accessLevel": "private"}, "indentation": {"spaces": 4}, "indentConditionalCompilationBlocks": false, "indentSwitchCaseLabels": false, "lineBreakAroundMultilineExpressionChainComponents": false, "lineBreakBeforeControlFlowKeywords": false, "lineBreakBeforeEachArgument": true, "lineBreakBeforeEachGenericRequirement": false, "lineLength": 200, "maximumBlankLines": 1, "multiElementCollectionTrailingCommas": true, "noAssignmentInExpressions": {"allowedFunctions": ["XCTAssertNoThrow"]}, "prioritizeKeepingFunctionOutputTogether": false, "respectsExistingLineBreaks": true, "rules": {"AllPublicDeclarationsHaveDocumentation": false, "AlwaysUseLiteralForEmptyCollectionInit": false, "AlwaysUseLowerCamelCase": true, "AmbiguousTrailingClosureOverload": true, "BeginDocumentationCommentWithOneLineSummary": false, "DoNotUseSemicolons": true, "DontRepeatTypeInStaticProperties": true, "FileScopedDeclarationPrivacy": true, "FullyIndirectEnum": true, "GroupNumericLiterals": true, "IdentifiersMustBeASCII": true, "NeverForceUnwrap": false, "NeverUseForceTry": false, "NeverUseImplicitlyUnwrappedOptionals": false, "NoAccessLevelOnExtensionDeclaration": false, "NoAssignmentInExpressions": true, "NoBlockComments": false, "NoCasesWithOnlyFallthrough": true, "NoEmptyTrailingClosureParentheses": true, "NoLabelsInCasePatterns": true, "NoLeadingUnderscores": false, "NoParensAroundConditions": true, "NoPlaygroundLiterals": true, "NoVoidReturnOnFunctionSignature": true, "OmitExplicitReturns": false, "OneCasePerLine": true, "OneVariableDeclarationPerLine": true, "OnlyOneTrailingClosureArgument": true, "OrderedImports": true, "ReplaceForEachWithForLoop": true, "ReturnVoidInsteadOfEmptyTuple": true, "TypeNamesShouldBeCapitalized": true, "UseEarlyExits": false, "UseLetInEveryBoundCaseVariable": false, "UseShorthandTypeNames": true, "UseSingleLinePropertyGetter": true, "UseSynthesizedInitializer": false, "UseTripleSlashForDocumentationComments": true, "UseWhereClausesInForLoops": false, "ValidateDocumentationComments": false}, "spacesAroundRangeFormationOperators": false, "tabWidth": 4, "version": 1}