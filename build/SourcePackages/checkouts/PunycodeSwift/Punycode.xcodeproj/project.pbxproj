// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A745A0A12C7ABB7C005625EF /* Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A745A09E2C7ABB7C005625EF /* Extensions.swift */; };
		A745A0A22C7ABB7C005625EF /* Punycode.swift in Sources */ = {isa = PBXBuildFile; fileRef = A745A09F2C7ABB7C005625EF /* Punycode.swift */; };
		A745A0A32C7ABB7C005625EF /* Helpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = A745A0A02C7ABB7C005625EF /* Helpers.swift */; };
		A75359B82C7ABA7D00564274 /* Punycode.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A75359AF2C7ABA7C00564274 /* Punycode.framework */; };
		A75359BD2C7ABA7D00564274 /* PunycodeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A75359BC2C7ABA7D00564274 /* PunycodeTests.swift */; };
		A75359BE2C7ABA7D00564274 /* Punycode.h in Headers */ = {isa = PBXBuildFile; fileRef = A75359B22C7ABA7C00564274 /* Punycode.h */; settings = {ATTRIBUTES = (Public, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		A75359B92C7ABA7D00564274 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A75359A62C7ABA7C00564274 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A75359AE2C7ABA7C00564274;
			remoteInfo = Punycode;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		A745A09E2C7ABB7C005625EF /* Extensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Extensions.swift; sourceTree = "<group>"; };
		A745A09F2C7ABB7C005625EF /* Punycode.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Punycode.swift; sourceTree = "<group>"; };
		A745A0A02C7ABB7C005625EF /* Helpers.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Helpers.swift; sourceTree = "<group>"; };
		A75359AF2C7ABA7C00564274 /* Punycode.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Punycode.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A75359B22C7ABA7C00564274 /* Punycode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Punycode.h; sourceTree = "<group>"; };
		A75359B72C7ABA7D00564274 /* PunycodeTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PunycodeTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		A75359BC2C7ABA7D00564274 /* PunycodeTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PunycodeTests.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A75359AC2C7ABA7C00564274 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A75359B42C7ABA7D00564274 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A75359B82C7ABA7D00564274 /* Punycode.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A75359A52C7ABA7C00564274 = {
			isa = PBXGroup;
			children = (
				A75359B12C7ABA7C00564274 /* Sources */,
				A75359BB2C7ABA7D00564274 /* Tests */,
				A75359B02C7ABA7C00564274 /* Products */,
			);
			sourceTree = "<group>";
		};
		A75359B02C7ABA7C00564274 /* Products */ = {
			isa = PBXGroup;
			children = (
				A75359AF2C7ABA7C00564274 /* Punycode.framework */,
				A75359B72C7ABA7D00564274 /* PunycodeTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A75359B12C7ABA7C00564274 /* Sources */ = {
			isa = PBXGroup;
			children = (
				A75359B22C7ABA7C00564274 /* Punycode.h */,
				A745A09F2C7ABB7C005625EF /* Punycode.swift */,
				A745A09E2C7ABB7C005625EF /* Extensions.swift */,
				A745A0A02C7ABB7C005625EF /* Helpers.swift */,
			);
			path = Sources;
			sourceTree = "<group>";
		};
		A75359BB2C7ABA7D00564274 /* Tests */ = {
			isa = PBXGroup;
			children = (
				A75359BC2C7ABA7D00564274 /* PunycodeTests.swift */,
			);
			path = Tests;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		A75359AA2C7ABA7C00564274 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A75359BE2C7ABA7D00564274 /* Punycode.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		A75359AE2C7ABA7C00564274 /* Punycode */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A75359C12C7ABA7D00564274 /* Build configuration list for PBXNativeTarget "Punycode" */;
			buildPhases = (
				A719B8B92C7C7F8E008E89EF /* Lint codes with swift-format */,
				A75359AA2C7ABA7C00564274 /* Headers */,
				A75359AB2C7ABA7C00564274 /* Sources */,
				A75359AC2C7ABA7C00564274 /* Frameworks */,
				A75359AD2C7ABA7C00564274 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Punycode;
			productName = Punycode;
			productReference = A75359AF2C7ABA7C00564274 /* Punycode.framework */;
			productType = "com.apple.product-type.framework";
		};
		A75359B62C7ABA7D00564274 /* PunycodeTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A75359C42C7ABA7D00564274 /* Build configuration list for PBXNativeTarget "PunycodeTests" */;
			buildPhases = (
				A75359B32C7ABA7D00564274 /* Sources */,
				A75359B42C7ABA7D00564274 /* Frameworks */,
				A75359B52C7ABA7D00564274 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				A75359BA2C7ABA7D00564274 /* PBXTargetDependency */,
			);
			name = PunycodeTests;
			productName = PunycodeTests;
			productReference = A75359B72C7ABA7D00564274 /* PunycodeTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A75359A62C7ABA7C00564274 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					A75359AE2C7ABA7C00564274 = {
						CreatedOnToolsVersion = 15.4;
						LastSwiftMigration = 1540;
					};
					A75359B62C7ABA7D00564274 = {
						CreatedOnToolsVersion = 15.4;
					};
				};
			};
			buildConfigurationList = A75359A92C7ABA7C00564274 /* Build configuration list for PBXProject "Punycode" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A75359A52C7ABA7C00564274;
			productRefGroup = A75359B02C7ABA7C00564274 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A75359AE2C7ABA7C00564274 /* Punycode */,
				A75359B62C7ABA7D00564274 /* PunycodeTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A75359AD2C7ABA7C00564274 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A75359B52C7ABA7D00564274 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		A719B8B92C7C7F8E008E89EF /* Lint codes with swift-format */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Lint codes with swift-format";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/zsh;
			shellScript = "export PATH=\"$PATH:/opt/homebrew/bin\"\n\nif which swift-format >/dev/null; then\n    swift-format format --in-place --ignore-unparsable-files --configuration .swift-format --recursive Sources Tests || true\n    swift-format lint --ignore-unparsable-files --configuration .swift-format --recursive Sources Tests || true\nelse\n    echo \"warning: swift-format not installed\"\nfi\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A75359AB2C7ABA7C00564274 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A745A0A22C7ABB7C005625EF /* Punycode.swift in Sources */,
				A745A0A32C7ABB7C005625EF /* Helpers.swift in Sources */,
				A745A0A12C7ABB7C005625EF /* Extensions.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A75359B32C7ABA7D00564274 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A75359BD2C7ABA7D00564274 /* PunycodeTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		A75359BA2C7ABA7D00564274 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A75359AE2C7ABA7C00564274 /* Punycode */;
			targetProxy = A75359B92C7ABA7D00564274 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		A75359BF2C7ABA7D00564274 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				TVOS_DEPLOYMENT_TARGET = 12.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
				XROS_DEPLOYMENT_TARGET = 1.0;
			};
			name = Debug;
		};
		A75359C02C7ABA7D00564274 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				TVOS_DEPLOYMENT_TARGET = 12.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
				XROS_DEPLOYMENT_TARGET = 1.0;
			};
			name = Release;
		};
		A75359C22C7ABA7D00564274 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = (
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MARKETING_VERSION = 3.0.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.gumob.Punycode;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = auto;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator xros xrsimulator";
				SUPPORTS_MACCATALYST = YES;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,3,4,7";
			};
			name = Debug;
		};
		A75359C32C7ABA7D00564274 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = (
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MARKETING_VERSION = 3.0.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.gumob.Punycode;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = auto;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator xros xrsimulator";
				SUPPORTS_MACCATALYST = YES;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,3,4,7";
			};
			name = Release;
		};
		A75359C52C7ABA7D00564274 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MARKETING_VERSION = 3.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.gumob.PunycodeTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator xros xrsimulator";
				SUPPORTS_MACCATALYST = YES;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,3,4,7";
			};
			name = Debug;
		};
		A75359C62C7ABA7D00564274 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MARKETING_VERSION = 3.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.gumob.PunycodeTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator xros xrsimulator";
				SUPPORTS_MACCATALYST = YES;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,3,4,7";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A75359A92C7ABA7C00564274 /* Build configuration list for PBXProject "Punycode" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A75359BF2C7ABA7D00564274 /* Debug */,
				A75359C02C7ABA7D00564274 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A75359C12C7ABA7D00564274 /* Build configuration list for PBXNativeTarget "Punycode" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A75359C22C7ABA7D00564274 /* Debug */,
				A75359C32C7ABA7D00564274 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A75359C42C7ABA7D00564274 /* Build configuration list for PBXNativeTarget "PunycodeTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A75359C52C7ABA7D00564274 /* Debug */,
				A75359C62C7ABA7D00564274 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A75359A62C7ABA7C00564274 /* Project object */;
}
