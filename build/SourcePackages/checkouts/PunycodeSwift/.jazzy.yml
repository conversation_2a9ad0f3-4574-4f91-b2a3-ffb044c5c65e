module: Punycode
version: 3.0.0

author: <PERSON><PERSON><PERSON>
author_url: https://github.com/gumob
github_url: https://github.com/gumob/PunycodeSwift
# copyright: 'Copyright © 2024 Ko<PERSON>ro Fu<PERSON>. All rights reserved.'

source_host_url: https://github.com/gumob/PunycodeSwift
root_url: https://github.com/gumob/PunycodeSwift/tree/3.0.0/docs/

readme: "README.md"

theme: fullwidth

xcodebuild_arguments:
  - -project
  - ../Punycode.xcodeproj
  - -scheme
  - Punycode

output: "docs/swiftdoc"
min_acl: public
clean: true
skip_undocumented: false
hide_documentation_coverage: true
source_directory: Sources
umbrella_header: "Sources/Punycode.h"

exclude:
  - Tests/*
