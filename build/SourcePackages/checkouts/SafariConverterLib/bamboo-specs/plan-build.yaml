---
version: 2
plan:
  project-key: IOS
  key: CONVERTERBUILD
  name: Converter - build
stages:
  - Lint:
      manual: false
      final: false
      jobs:
        - Lint
  - Test:
      manual: false
      final: false
      jobs:
        - Test
  - Build:
      manual: false
      final: false
      jobs:
        - Build
Lint:
  key: LINT
  tasks:
    - checkout:
        force-clean-build: "true"
    - script:
        interpreter: SHELL
        scripts:
          - |-
            set -e
            set -x

            # Install markdownlint-cli.
            npm install -g markdownlint-cli

            # Check the tools.
            make tools

            # Run linters.
            make lint
  requirements:
    - image: registry.int.agrd.dev/macos/sequoia-build-agent-xcode16.3:latest
    - ephemeral

Test:
  key: TEST
  tasks:
    - checkout:
        force-clean-build: "true"
    - script:
        interpreter: SHELL
        scripts:
          - |-
            set -e
            set -x

            # Run all tests.
            make test
  requirements:
    - image: registry.int.agrd.dev/macos/sequoia-build-agent-xcode16.3:latest
    - ephemeral

Build:
  key: BUILD
  tasks:
    - checkout:
        force-clean-build: "true"
    - script:
        interpreter: SHELL
        scripts:
          - |-
            set -e
            set -x

            # Run build.
            make build
  requirements:
    - image: registry.int.agrd.dev/macos/sequoia-build-agent-xcode16.3:latest
    - ephemeral

branches:
  create: for-pull-request
  delete:
    after-deleted-days: 1
    after-inactive-days: 30
  integration:
    push-on-success: false
    merge-from: Converter - build
  link-to-jira: true

notifications: []
labels: []
other:
  concurrent-build-plugin: system-default
