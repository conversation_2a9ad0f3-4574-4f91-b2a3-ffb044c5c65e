{"pins": [{"identity": "punycodeswift", "kind": "remoteSourceControl", "location": "https://github.com/gumob/PunycodeSwift.git", "state": {"revision": "30a462bdb4398ea835a3585472229e0d74b36ba5", "version": "3.0.0"}}, {"identity": "swift-argument-parser", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-argument-parser", "state": {"revision": "41982a3656a71c768319979febd796c6fd111d5c", "version": "1.5.0"}}, {"identity": "swift-psl", "kind": "remoteSourceControl", "location": "https://github.com/ameshkov/swift-psl", "state": {"revision": "58eb614f348d30bc78db6376947560a7a7338f74", "version": "1.1.0"}}], "version": 2}