{"version": 1, "lineLength": 100, "indentation": {"spaces": 4}, "lineBreakBeforeEachArgument": true, "indentConditionalCompilationBlocks": false, "prioritizeKeepingFunctionOutputTogether": true, "rules": {"AlwaysUseLowerCamelCase": false, "AmbiguousTrailingClosureOverload": false, "NoBlockComments": false, "OrderedImports": true, "UseLetInEveryBoundCaseVariable": false, "UseSynthesizedInitializer": false}}