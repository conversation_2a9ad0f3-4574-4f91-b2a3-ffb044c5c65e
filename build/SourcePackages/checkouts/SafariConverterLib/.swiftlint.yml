excluded:
  - ${PWD}/Carthage
  - ${PWD}/Pods
  - ${PWD}/DerivedData
  - ${PWD}/.build
  - ${PWD}/.swiftpm
  - ${PWD}/bamboo-specs

  # Excluding tests as the linter fails on long rule texts. Splitting test rules
  # into smaller chunks would be actually counter-productive and harder to
  # maintain.
  - ${PWD}/Tests

disabled_rules:
  - discarded_notification_center_observer
  - notification_center_detachment
  - orphaned_doc_comment
  - todo
  - cyclomatic_complexity
  - inclusive_language
  - file_length
  - function_body_length
  - type_body_length
  - trailing_comma
  - opening_brace # handled by swift-format

opt_in_rules:
  - array_init
  - attributes
  - closure_end_indentation
  - closure_spacing
  - collection_alignment
  - colon # promote to error
  - convenience_type
  - discouraged_object_literal
  - empty_collection_literal
  - empty_count
  - empty_string
  - enum_case_associated_values_count
  - fatal_error_message
  - first_where
  - force_unwrapping
  - implicitly_unwrapped_optional
  - indentation_width
  - last_where
  - legacy_random
  - literal_expression_end_indentation
  - multiline_arguments
  - multiline_function_chains
  - multiline_literal_brackets
  - multiline_parameters
  - multiline_parameters_brackets
  - operator_usage_whitespace
  - overridden_super_call
  - pattern_matching_keywords
  - prefer_self_type_over_type_of_self
  - redundant_nil_coalescing
  - redundant_type_annotation
  - strict_fileprivate
  - toggle_bool
  - trailing_closure
  - unneeded_parentheses_in_closure_argument
  - vertical_whitespace_closing_braces
  - vertical_whitespace_opening_braces
  - yoda_condition

analyzer_rules:
  - unused_import
  - unused_declaration
  - capture_variable
  - typesafe_array_init

custom_rules:
  array_constructor:
    name: "Array/Dictionary initializer"
    regex: '[let,var] .+ = (\[.+\]\(\))'
    capture_group: 1
    message: "Use explicit type annotation when initializing empty arrays and dictionaries"
    severity: warning

attributes:
  always_on_same_line:
    - "@IBSegueAction"
    - "@IBAction"
    - "@NSManaged"
    - "@objc"

legacy_hashing: error

identifier_name:
  excluded:
    - i
    - id
    - x
    - y
    - z
    - js
  max_length: 60
  allowed_symbols: ["_"]

indentation_width:
  indentation_width: 4
  include_comments: false

line_length:
  ignores_urls: true
  ignores_multiline_strings: true

multiline_arguments:
  first_argument_location: next_line
  only_enforce_after_first_closure_on_first_line: true

private_over_fileprivate:
  validate_extensions: true

trailing_whitespace:
  ignores_empty_lines: false
  ignores_comments: false

vertical_whitespace:
  max_empty_lines: 0
