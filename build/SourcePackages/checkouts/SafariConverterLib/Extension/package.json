{"name": "@adguard/safari-extension", "version": "3.0.0", "description": "Safari Web Extension API for SafariConverterLib", "author": "Adguard Software Ltd.", "license": "GPL-3.0", "repository": {"type": "git", "url": "git+https://github.com/AdguardTeam/SafariConverterLib.git", "directory": "Extension"}, "bugs": {"url": "https://github.com/AdguardTeam/SafariConverterLib/issues"}, "homepage": "https://github.com/AdguardTeam/SafariConverterLib/tree/master/Extension", "type": "module", "main": "dist/safari-extension.cjs", "module": "dist/safari-extension.esm.js", "browser": "dist/safari-extension.umd.min.js", "types": "dist/safari-extension.d.ts", "exports": {".": {"types": "./dist/safari-extension.d.ts", "import": "./dist/safari-extension.esm.js", "require": "./dist/safari-extension.cjs"}, "./es": "./dist/safari-extension.esm.js", "./iife": "./dist/safari-extension.iife.min.js", "./umd": "./dist/safari-extension.umd.min.js"}, "files": ["dist"], "scripts": {"build": "pnpm clean && pnpm build-types && pnpm rollup --config rollup.config.ts --configPlugin @rollup/plugin-json --configPlugin @rollup/plugin-typescript && pnpm clean-types", "build-types": "tsc --declaration --emitDeclarationOnly --outdir dist/types", "package": "pnpm build && pnpm pack --out adguard-safari-extension.tgz", "check-types": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "clean-types": "rimraf dist/types", "lint": "eslint . --cache --ext .ts", "test": "vitest"}, "dependencies": {"@adguard/extended-css": "^2.1.1", "@adguard/scriptlets": "^2.1.4"}, "devDependencies": {"@babel/core": "^7.22.8", "@babel/preset-env": "^7.22.7", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^25.0.4", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.2.1", "@rollup/plugin-typescript": "^11.1.4", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.57.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-import-newlines": "^1.3.4", "eslint-plugin-jsdoc": "^46.8.2", "markdown-table": "^3.0.3", "markdownlint": "^0.31.1", "markdownlint-cli": "^0.37.0", "rimraf": "^5.0.5", "rollup": "^3.29.4", "rollup-plugin-dts": "^6.0.2", "typescript": "^5.1.6", "vitest": "^3.0.4"}}