#!/bin/sh

set -e -f -u

# Only show interactive prompts if there a terminal is attached to stdout.
# While this technically doesn't guarantee that reading from /dev/tty works,
# this should work reasonably well on all of our supported development systems
# and in most terminal emulators.
is_tty='0'
if [ -t '1' ]; then
    is_tty='1'
fi
readonly is_tty

# prompt is a helper that prompts the user for interactive input if that can be
# done.  If there is no terminal attached, it sleeps for two seconds, giving the
# programmer some time to react, and returns with a zero exit code.
prompt() {
    if [ "$is_tty" -eq '0' ]; then
        sleep 2

        return 0
    fi

    while true; do
        printf 'commit anyway? y/[n]: '
        read -r ans </dev/tty

        case "$ans" in
        'y' | 'Y')
            break
            ;;
        '' | 'n' | 'N')
            exit 1
            ;;
        *)
            continue
            ;;
        esac
    done
}

# Warn the programmer about unstaged changes and untracked files, but do not
# fail the commit, because those changes might be temporary or for a different
# branch.
#
# shellcheck disable=SC2016
awk_prog='substr($2, 2, 1) != "." { print $9; } $1 == "?" { print $2; }'
readonly awk_prog

unstaged="$(git status --porcelain=2 | awk "$awk_prog")"
readonly unstaged

if [ "$unstaged" != '' ]; then
    printf 'WARNING: you have unstaged changes:\n\n%s\n\n' "$unstaged"
    prompt
fi

# Warn the programmer about temporary todos and skel FIXMEs, but do not fail the
# commit, because the commit could be in a temporary branch.
temp_todos="$(
    git grep -e 'FIXME' -e 'TODO.*!!' -- ':!scripts/hooks/pre-commit' || :
)"
readonly temp_todos

if [ "$temp_todos" != '' ]; then
    printf 'WARNING: you have temporary todos:\n\n%s\n\n' "$temp_todos"
    prompt
fi

if [ "$(git diff --cached --name-only -- '*.md' || :)" != '' ]; then
    make md-lint
fi

if [ "$(git diff --cached --name-only -- '*.js' '*.ts' || :)" != '' ]; then
    make js-lint
    make js-test
fi

if [ "$(git diff --cached --name-only -- '*.swift' || :)" != '' ]; then
    make swift-lint
    make swift-test
fi

if [ "$(git diff --cached --name-only -- '*FileLock*' || :)" != '' ]; then
    make filelock-test
fi

if [ "$(git diff --cached --name-only -- '*CommandLineWrapper*' || :)" != '' ]; then
    make command-line-wrapper-test
fi
