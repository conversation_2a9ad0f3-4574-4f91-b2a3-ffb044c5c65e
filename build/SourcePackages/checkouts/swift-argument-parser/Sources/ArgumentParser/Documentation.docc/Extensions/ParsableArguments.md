# ``ArgumentParser/ParsableArguments``

## Topics

### Handling Validation

- ``validate()-5r0ge``

### Parsing a Type

- ``parse(_:)``
- ``parseOrExit(_:)``

### Exiting a Program

- ``exit(withError:)``

### Generating Help Text

- ``helpMessage(includeHidden:columns:)``

### Handling Errors

- ``message(for:)``
- ``fullMessage(for:columns:)``
- ``exitCode(for:)``

### Generating Completion Scripts

- ``completionScript(for:)``
- ``CompletionShell``

### Infrequently Used APIs

- ``init()``
