# ``ArgumentParser/Option``

## Topics

### Single-Value Options

- ``init(name:parsing:help:completion:)-4yske``
- ``init(name:parsing:help:completion:)-7slrf``
- ``init(wrappedValue:name:parsing:help:completion:)-7ilku``
- ``init(name:parsing:help:completion:transform:)-2wf44``
- ``init(name:parsing:help:completion:transform:)-25g7b``
- ``init(wrappedValue:name:parsing:help:completion:transform:)-2llve``
- ``SingleValueParsingStrategy``

### Array Options

- ``init(name:parsing:help:completion:)-238hg``
- ``init(name:parsing:help:completion:transform:)-74hnp``
- ``init(wrappedValue:name:parsing:help:completion:)-1dtbf``
- ``init(wrappedValue:name:parsing:help:completion:transform:)-1kpto``
- ``ArrayParsingStrategy``

### Infrequently Used APIs

- ``wrappedValue``
