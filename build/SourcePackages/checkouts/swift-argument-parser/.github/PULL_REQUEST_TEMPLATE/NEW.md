<!--
    Thanks for contributing to the Swift Argument Parser!

    Before you submit your request, please replace each paragraph
    below with the relevant details, and complete the steps in the
    checklist by placing an 'x' in each box:
    
    - [x] I've completed this task
    - [ ] This task isn't completed
-->

### Description
Replace this paragraph with a description of your changes and rationale. Provide links to an existing issue or external references/discussions, if appropriate.

### Detailed Design
Include any additional information about the design here. At minimum, show any new API:

```swift
/// The new feature implemented by this pull request.
struct NewFeature {}
```

### Documentation Plan
How has the new feature been documented? Have the relevant portions of the guide been updated in addition to symbol-level documentation?

### Test Plan
How is the new feature tested?

### Source Impact
What is the impact of this change on existing users? Does it deprecate or remove any existing API?

### Checklist
- [ ] I've added at least one test that validates that my change is working, if appropriate
- [ ] I've followed the code style of the rest of the project
- [ ] I've read the [Contribution Guidelines](https://github.com/apple/swift-argument-parser/blob/main/CONTRIBUTING.md)
- [ ] I've updated the documentation if necessary
