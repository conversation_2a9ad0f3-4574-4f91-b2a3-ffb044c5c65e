---
name: 🐛 Bug Report
about: Something isn't working as expected
---

<!--
    Thanks for contributing to the Swift Argument Parser!

    Before you submit your issue, please replace each paragraph
    below with the relevant details for your bug, and complete
    the steps in the checklist by placing an 'x' in each box:
    
    - [x] I've completed this task
    - [ ] This task isn't completed
-->

Replace this paragraph with a short description of the incorrect incorrect behavior. If this is a regression, please note the last version that the behavior was correct in addition to your current version.

**ArgumentParser version:** `0.1.0` or the `main` branch, for example.
**Swift version:** Paste the output of `swift --version` here.

### Checklist
- [ ] If possible, I've reproduced the issue using the `main` branch of this package
- [ ] I've searched for [existing GitHub issues](https://github.com/apple/swift-argument-parser/issues)

### Steps to Reproduce
Replace this paragraph with an explanation of how to reproduce the incorrect behavior. This could include a code listing for a reduced version of your command, or a link to the code that is exhibiting the issue.

### Expected behavior
Describe what you expect to happen.

### Actual behavior
Describe or copy/paste the behavior you observe.
