---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Charts.swiftmodule/arm64-apple-ios-simulator.swiftmodule'
dependencies:
  - mtime:           1742264668000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.4/Charts.swiftmodule/arm64-apple-ios-simulator.swiftmodule'
    size:            524844
  - mtime:           1741411723000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1929099
    sdk_relative:    true
  - mtime:           1741411882000000000
    path:            'usr/lib/swift/SwiftOnoneSupport.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1015
    sdk_relative:    true
  - mtime:           1741412425000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1741412518000000000
    path:            'usr/lib/swift/_errno.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            3851
    sdk_relative:    true
  - mtime:           1741412530000000000
    path:            'usr/lib/swift/_signal.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1062
    sdk_relative:    true
  - mtime:           1741412535000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1063
    sdk_relative:    true
  - mtime:           1741412529000000000
    path:            'usr/lib/swift/_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1026
    sdk_relative:    true
  - mtime:           1741412529000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1474
    sdk_relative:    true
  - mtime:           1741412538000000000
    path:            'usr/lib/swift/unistd.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            814
    sdk_relative:    true
  - mtime:           1741412517000000000
    path:            'usr/lib/swift/_math.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            15245
    sdk_relative:    true
  - mtime:           1741411886000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            4224
    sdk_relative:    true
  - mtime:           1741412547000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            18216
    sdk_relative:    true
  - mtime:           1741412890000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            230593
    sdk_relative:    true
  - mtime:           1741413044000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            22870
    sdk_relative:    true
  - mtime:           1741413566000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            167795
    sdk_relative:    true
  - mtime:           1741411513000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1741408955000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1741413528000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            6558
    sdk_relative:    true
  - mtime:           1741413691000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            57131
    sdk_relative:    true
  - mtime:           1741413840000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            22820
    sdk_relative:    true
  - mtime:           1741411435000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1741409320000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1741748427000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1741413871000000000
    path:            'usr/lib/swift/XPC.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            33270
    sdk_relative:    true
  - mtime:           1741412918000000000
    path:            'usr/lib/swift/Observation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            3451
    sdk_relative:    true
  - mtime:           1741413558000000000
    path:            'usr/lib/swift/System.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            95465
    sdk_relative:    true
  - mtime:           1741753489000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            991696
    sdk_relative:    true
  - mtime:           1741410155000000000
    path:            'System/Library/Frameworks/CoreData.framework/Headers/CoreData.apinotes'
    size:            7789
    sdk_relative:    true
  - mtime:           1741414769000000000
    path:            'System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            42596
    sdk_relative:    true
  - mtime:           1741753036000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            52901
    sdk_relative:    true
  - mtime:           1741837811000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            53005
    sdk_relative:    true
  - mtime:           1741415937000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1741414891000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            20571
    sdk_relative:    true
  - mtime:           1741412425000000000
    path:            'usr/include/os.apinotes'
    size:            1658
    sdk_relative:    true
  - mtime:           1741413880000000000
    path:            'usr/lib/swift/os.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            108026
    sdk_relative:    true
  - mtime:           1741417276000000000
    path:            'System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            22032
    sdk_relative:    true
  - mtime:           1741415499000000000
    path:            'System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            10881
    sdk_relative:    true
  - mtime:           1741415534000000000
    path:            'usr/lib/swift/OSLog.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1259
    sdk_relative:    true
  - mtime:           1741413599000000000
    path:            'usr/lib/swift/simd.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            229466
    sdk_relative:    true
  - mtime:           1741413744000000000
    path:            'usr/lib/swift/Spatial.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            99176
    sdk_relative:    true
  - mtime:           1741415113000000000
    path:            'System/Library/Frameworks/OpenGLES.framework/Headers/OpenGLES.apinotes'
    size:            1192
    sdk_relative:    true
  - mtime:           1738796627000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            77528
    sdk_relative:    true
  - mtime:           1741412646000000000
    path:            'System/Library/Frameworks/CoreImage.framework/Headers/CoreImage.apinotes'
    size:            36883
    sdk_relative:    true
  - mtime:           1741415969000000000
    path:            'System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes'
    size:            1662
    sdk_relative:    true
  - mtime:           1742001086000000000
    path:            'System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes'
    size:            7428
    sdk_relative:    true
  - mtime:           1741415954000000000
    path:            'System/Library/Frameworks/UserNotifications.framework/Headers/UserNotifications.apinotes'
    size:            326
    sdk_relative:    true
  - mtime:           1742181995000000000
    path:            'System/Library/Frameworks/UIKit.framework/Headers/UIKit.apinotes'
    size:            162201
    sdk_relative:    true
  - mtime:           1741415951000000000
    path:            'System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            21279
    sdk_relative:    true
  - mtime:           1741416337000000000
    path:            'System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1543
    sdk_relative:    true
  - mtime:           1741414926000000000
    path:            'usr/lib/swift/DataDetection.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            697
    sdk_relative:    true
  - mtime:           1741415417000000000
    path:            'usr/lib/swift/Metal.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            25973
    sdk_relative:    true
  - mtime:           1741415816000000000
    path:            'usr/lib/swift/QuartzCore.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1770
    sdk_relative:    true
  - mtime:           1741416471000000000
    path:            'System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            21557
    sdk_relative:    true
  - mtime:           1741579994000000000
    path:            'System/Library/Frameworks/FileProvider.framework/Modules/FileProvider.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1429
    sdk_relative:    true
  - mtime:           1741415818000000000
    path:            'usr/lib/swift/CoreImage.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            701
    sdk_relative:    true
  - mtime:           1742182042000000000
    path:            'System/Library/Frameworks/UIKit.framework/Modules/UIKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            219899
    sdk_relative:    true
  - mtime:           1741417392000000000
    path:            'System/Library/Frameworks/SwiftUICore.framework/Modules/SwiftUICore.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1005192
    sdk_relative:    true
  - mtime:           1741756111000000000
    path:            'System/Library/Frameworks/SwiftUI.framework/Modules/SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1322341
    sdk_relative:    true
  - mtime:           1741422165000000000
    path:            'System/Library/Frameworks/Charts.framework/Modules/Charts.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            170517
    sdk_relative:    true
version:         1
...
