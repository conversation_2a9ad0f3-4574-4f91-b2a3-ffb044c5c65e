import Foundation
import UIKit

// MARK: - 媒体管理统一配置中心
struct MediaManagementConfig {
    
    // MARK: - 内存管理配置
    struct Memory {
        // 内存阈值配置（统一使用绝对值，便于跨组件协调）
        static let warningThreshold: UInt64 = 300 * 1024 * 1024      // 300MB
        static let criticalThreshold: UInt64 = 450 * 1024 * 1024     // 450MB
        static let emergencyThreshold: UInt64 = 600 * 1024 * 1024    // 600MB
        static let optimalTarget: UInt64 = 200 * 1024 * 1024         // 200MB 目标
        
        // 系统预留内存
        static let systemReservedMemory: UInt64 = 1024 * 1024 * 1024 // 1GB
        
        // 清理间隔
        static let cleanupInterval: TimeInterval = 300 // 5分钟
        static let aggressiveCleanupInterval: TimeInterval = 60 // 1分钟（高压状态）
        
        // 内存压力百分比阈值（用于兼容性）
        static let warningPercentage: Double = 0.75   // 75%
        static let criticalPercentage: Double = 0.85  // 85%
        
        // 内存监控配置
        static let monitoringInterval: TimeInterval = 10.0 // 10秒
        static let historyLimit: Int = 50 // 保留50个历史记录
    }
    
    // MARK: - 缓存配置
    struct Cache {
        // 统一缓存大小限制
        static let memoryLimit: Int64 = 100 * 1024 * 1024     // 100MB内存缓存
        static let diskLimit: Int64 = 500 * 1024 * 1024       // 500MB磁盘缓存
        static let databaseCacheLimit: Int64 = 200 * 1024 * 1024 // 200MB数据库缓存
        
        // 缓存项目数量限制
        static let memoryItemLimit: Int = 200      // 内存缓存项目限制
        static let diskItemLimit: Int = 1000       // 磁盘缓存项目限制
        static let metadataItemLimit: Int = 5000   // 元数据缓存项目限制
        
        // 缓存清理阈值
        static let cleanupThreshold: Double = 0.9  // 90%时开始清理
        static let targetCleanupRatio: Double = 0.7 // 清理到70%
        
        // 缓存过期时间
        static let memoryExpiration: TimeInterval = 1800    // 30分钟
        static let diskExpiration: TimeInterval = 7 * 24 * 3600 // 7天
        static let metadataExpiration: TimeInterval = 14 * 24 * 3600 // 14天
    }
    
    // MARK: - 并发控制配置
    struct Concurrency {
        // 全局并发限制
        static let maxGlobalConcurrentOperations: Int = ProcessInfo.processInfo.processorCount
        
        // 导入服务并发限制
        static let maxImportOperations: Int = min(4, maxGlobalConcurrentOperations)
        static let maxImportBatchSize: Int = 50
        static let largeFileBatchSize: Int = 10
        
        // 缩略图服务并发限制
        static let maxThumbnailOperations: Int = min(3, maxGlobalConcurrentOperations - 1)
        static let maxThumbnailBatchSize: Int = 20
        
        // 数据库操作并发限制
        static let maxDatabaseOperations: Int = 2
        static let maxDatabaseBatchSize: Int = 100
        
        // 网络请求并发限制
        static let maxNetworkOperations: Int = 6
        
        // 自适应并发因子
        static let adaptiveFactor: Double = 0.5
        
        // 并发操作间隔
        static let operationDelay: TimeInterval = 0.05      // 50ms
        static let batchDelay: TimeInterval = 2.0           // 2秒
    }
    
    // MARK: - 缩略图配置
    struct Thumbnail {
        // 缩略图尺寸配置
        enum Size: String, CaseIterable {
            case small = "small"     // 150x150
            case medium = "medium"   // 300x300  
            case large = "large"     // 600x600
            
            var size: CGSize {
                switch self {
                case .small: return CGSize(width: 150, height: 150)
                case .medium: return CGSize(width: 300, height: 300)
                case .large: return CGSize(width: 600, height: 600)
                }
            }
            
            var directory: String { return rawValue }
        }
        
        // 缩略图质量
        static let jpegQuality: CGFloat = 0.8
        static let pngCompressionLevel: Int = 6
        
        // 生成优先级
        static let defaultSizes: [Size] = [.medium, .small]
        static let preloadSizes: [Size] = [.small, .medium]
        static let highQualitySizes: [Size] = [.large, .medium, .small]
    }
    
    // MARK: - 性能配置
    struct Performance {
        // UI性能配置
        static let virtualScrollBufferSize: Int = 20
        static let virtualScrollPreloadDistance: CGFloat = 300
        static let listItemHeight: CGFloat = 80
        
        // 动画配置
        static let defaultAnimationDuration: TimeInterval = 0.25
        static let fastAnimationDuration: TimeInterval = 0.15
        static let slowAnimationDuration: TimeInterval = 0.35
        
        // 性能监控
        static let performanceAnalysisInterval: TimeInterval = 10.0
        static let performanceHistoryLimit: Int = 100
        static let fpsTargetThreshold: Double = 55.0
        static let cpuWarningThreshold: Double = 80.0
    }
    
    // MARK: - 错误处理配置
    struct ErrorHandling {
        // 重试配置
        static let maxRetries: Int = 3
        static let retryDelay: TimeInterval = 2.0
        static let exponentialBackoffFactor: Double = 2.0
        
        // 错误记录限制
        static let maxErrorHistory: Int = 100
        static let errorReportingInterval: TimeInterval = 300 // 5分钟
    }
    
    // MARK: - 日志配置
    struct Logging {
        static let enableDetailedLogging: Bool = {
            #if DEBUG
            return true
            #else
            return false
            #endif
        }()
        
        static let enablePerformanceLogging: Bool = enableDetailedLogging
        static let maxLogFileSize: Int64 = 10 * 1024 * 1024 // 10MB
        static let maxLogFiles: Int = 5
    }
    
    // MARK: - 设备适配配置
    struct DeviceAdaptation {
        // 根据设备性能调整配置
        static var adaptiveMemoryLimit: UInt64 {
            let totalMemory = ProcessInfo.processInfo.physicalMemory
            if totalMemory >= 8 * 1024 * 1024 * 1024 { // 8GB+
                return Memory.warningThreshold
            } else if totalMemory >= 4 * 1024 * 1024 * 1024 { // 4GB+
                return Memory.warningThreshold * 8 / 10 // 80%
            } else {
                return Memory.warningThreshold * 6 / 10 // 60%
            }
        }
        
        static var adaptiveConcurrency: Int {
            let processorCount = ProcessInfo.processInfo.processorCount
            return max(2, Int(Double(processorCount) * Concurrency.adaptiveFactor))
        }
        
        static var adaptiveCacheSize: Int64 {
            let totalMemory = ProcessInfo.processInfo.physicalMemory
            if totalMemory >= 8 * 1024 * 1024 * 1024 { // 8GB+
                return Cache.memoryLimit
            } else {
                return Cache.memoryLimit * 7 / 10 // 70%
            }
        }
    }
    
    // MARK: - 配置验证
    static func validateConfiguration() -> [String] {
        var warnings: [String] = []
        
        // 验证内存配置
        if Memory.warningThreshold >= Memory.criticalThreshold {
            warnings.append("⚠️ 内存警告阈值不应大于等于紧急阈值")
        }
        
        if Memory.criticalThreshold >= Memory.emergencyThreshold {
            warnings.append("⚠️ 内存紧急阈值不应大于等于极限阈值")
        }
        
        // 验证缓存配置
        let totalCacheLimit = Cache.memoryLimit + Cache.diskLimit + Cache.databaseCacheLimit
        if totalCacheLimit > 1024 * 1024 * 1024 { // 1GB
            warnings.append("⚠️ 总缓存限制超过1GB: \(totalCacheLimit / 1024 / 1024)MB")
        }
        
        // 验证并发配置
        let totalConcurrency = Concurrency.maxImportOperations + 
                              Concurrency.maxThumbnailOperations + 
                              Concurrency.maxDatabaseOperations + 
                              Concurrency.maxNetworkOperations
        if totalConcurrency > Concurrency.maxGlobalConcurrentOperations * 3 {
            warnings.append("⚠️ 总并发操作数可能过高: \(totalConcurrency)")
        }
        
        return warnings
    }
    
    // MARK: - 配置更新通知
    static let configurationDidChange = Notification.Name("MediaManagementConfigurationDidChange")
    
    // MARK: - 动态配置更新
    private static var _dynamicMemoryFactor: Double = 1.0
    private static var _dynamicConcurrencyFactor: Double = 1.0
    
    static var dynamicMemoryWarningThreshold: UInt64 {
        return UInt64(Double(Memory.warningThreshold) * _dynamicMemoryFactor)
    }
    
    static var dynamicMaxConcurrentOperations: Int {
        return max(1, Int(Double(Concurrency.maxImportOperations) * _dynamicConcurrencyFactor))
    }
    
    static func updateDynamicFactors(memoryFactor: Double, concurrencyFactor: Double) {
        _dynamicMemoryFactor = max(0.5, min(2.0, memoryFactor))
        _dynamicConcurrencyFactor = max(0.5, min(2.0, concurrencyFactor))
        
        NotificationCenter.default.post(name: configurationDidChange, object: nil)
    }
}

// MARK: - 配置协议
protocol ConfigurableService {
    func updateConfiguration()
}

// MARK: - 配置管理器
class MediaConfigurationManager: ObservableObject {
    static let shared = MediaConfigurationManager()
    
    @Published var isOptimizedForLowMemory: Bool = false
    @Published var isOptimizedForPerformance: Bool = true
    
    private init() {
        setupConfigurationObserver()
        performInitialValidation()
    }
    
    private func setupConfigurationObserver() {
        NotificationCenter.default.addObserver(
            forName: MediaManagementConfig.configurationDidChange,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.objectWillChange.send()
        }
    }
    
    private func performInitialValidation() {
        let warnings = MediaManagementConfig.validateConfiguration()
        if !warnings.isEmpty {
            print("⚠️ 配置验证警告:")
            warnings.forEach { print($0) }
        }
    }
    
    func optimizeForLowMemory() {
        isOptimizedForLowMemory = true
        isOptimizedForPerformance = false
        MediaManagementConfig.updateDynamicFactors(memoryFactor: 0.7, concurrencyFactor: 0.8)
    }
    
    func optimizeForPerformance() {
        isOptimizedForLowMemory = false
        isOptimizedForPerformance = true
        MediaManagementConfig.updateDynamicFactors(memoryFactor: 1.2, concurrencyFactor: 1.3)
    }
    
    func resetToDefaults() {
        isOptimizedForLowMemory = false
        isOptimizedForPerformance = true
        MediaManagementConfig.updateDynamicFactors(memoryFactor: 1.0, concurrencyFactor: 1.0)
    }
} 