import Foundation
import os.log

// MARK: - 内存工具类（统一内存计算逻辑）
final class MemoryUtils {
    
    // MARK: - 私有属性
    private static let logger = Logger(subsystem: "com.cop.app", category: "MemoryUtils")
    
    // MARK: - 内存信息获取
    
    /// 获取当前应用内存使用量
    static func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if result == KERN_SUCCESS {
            return info.resident_size
        } else {
            logger.warning("获取内存使用失败，返回0")
            return 0
        }
    }
    
    /// 获取当前应用虚拟内存使用量
    static func getVirtualMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        return result == KERN_SUCCESS ? info.virtual_size : 0
    }
    
    /// 获取系统总内存
    static func getTotalSystemMemory() -> UInt64 {
        return ProcessInfo.processInfo.physicalMemory
    }
    
    /// 获取系统可用内存（简化版本）
    static func getAvailableSystemMemory() -> UInt64 {
        let totalMemory = ProcessInfo.processInfo.physicalMemory
        let currentUsage = getCurrentMemoryUsage()
        return totalMemory > currentUsage ? totalMemory - currentUsage : 0
    }
    
    // MARK: - 内存压力计算
    
    /// 计算内存使用率
    static func calculateMemoryUtilization() -> Double {
        let currentUsage = getCurrentMemoryUsage()
        let totalMemory = getTotalSystemMemory()
        
        guard totalMemory > 0 else { return 0.0 }
        return Double(currentUsage) / Double(totalMemory)
    }
    
    /// 判断内存压力级别
    static func getMemoryPressureLevel() -> MemoryPressureLevel {
        let currentUsage = getCurrentMemoryUsage()
        let config = MediaManagementConfig.Memory.self
        
        if currentUsage >= config.emergencyThreshold {
            return .emergency
        } else if currentUsage >= config.criticalThreshold {
            return .critical
        } else if currentUsage >= config.warningThreshold {
            return .warning
        } else {
            return .normal
        }
    }
    
    /// 判断内存压力级别（基于百分比）
    static func getMemoryPressureLevelByPercentage() -> MemoryPressureLevel {
        let utilization = calculateMemoryUtilization()
        let config = MediaManagementConfig.Memory.self
        
        if utilization >= 0.90 { // 90%+
            return .emergency
        } else if utilization >= config.criticalPercentage {
            return .critical
        } else if utilization >= config.warningPercentage {
            return .warning
        } else {
            return .normal
        }
    }
    
    /// 获取综合内存压力级别（取两种计算方式的较高级别）
    static func getComprehensiveMemoryPressureLevel() -> MemoryPressureLevel {
        let absoluteLevel = getMemoryPressureLevel()
        let percentageLevel = getMemoryPressureLevelByPercentage()
        
        // 返回较高的压力级别
        return max(absoluteLevel, percentageLevel)
    }
    
    // MARK: - 内存状态检查
    
    /// 检查是否需要内存清理
    static func shouldPerformMemoryCleanup() -> Bool {
        let pressureLevel = getComprehensiveMemoryPressureLevel()
        return pressureLevel.rawValue >= MemoryPressureLevel.warning.rawValue
    }
    
    /// 检查是否应该暂停高内存操作
    static func shouldPauseHighMemoryOperations() -> Bool {
        let pressureLevel = getComprehensiveMemoryPressureLevel()
        return pressureLevel.rawValue >= MemoryPressureLevel.critical.rawValue
    }
    
    /// 检查是否处于内存紧急状态
    static func isInMemoryEmergency() -> Bool {
        return getComprehensiveMemoryPressureLevel() == .emergency
    }
    
    // MARK: - 内存预测
    
    /// 预测内存使用增长
    static func predictMemoryGrowth(basedOnHistory history: [UInt64]) -> UInt64? {
        guard history.count >= 3 else { return nil }
        
        // 简单线性预测
        let recentHistory = Array(history.suffix(5))
        let averageGrowth = calculateAverageGrowth(recentHistory)
        let currentUsage = getCurrentMemoryUsage()
        
        return UInt64(max(0, Int64(currentUsage) + Int64(averageGrowth)))
    }
    
    /// 计算平均增长率
    private static func calculateAverageGrowth(_ values: [UInt64]) -> Int64 {
        guard values.count >= 2 else { return 0 }
        
        var totalGrowth: Int64 = 0
        for i in 1..<values.count {
            totalGrowth += Int64(values[i]) - Int64(values[i-1])
        }
        
        return totalGrowth / Int64(values.count - 1)
    }
    
    // MARK: - 内存信息格式化
    
    /// 格式化字节数为可读字符串
    static func formatBytes(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .decimal
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    /// 格式化内存使用率为百分比字符串
    static func formatMemoryUtilization(_ utilization: Double) -> String {
        return String(format: "%.1f%%", utilization * 100)
    }
    
    // MARK: - 内存详细信息
    
    /// 获取详细的内存信息
    static func getDetailedMemoryInfo() -> DetailedMemoryInfo {
        let currentUsage = getCurrentMemoryUsage()
        let virtualUsage = getVirtualMemoryUsage()
        let totalMemory = getTotalSystemMemory()
        let availableMemory = getAvailableSystemMemory()
        let utilization = calculateMemoryUtilization()
        let pressureLevel = getComprehensiveMemoryPressureLevel()
        
        return DetailedMemoryInfo(
            currentUsage: currentUsage,
            virtualUsage: virtualUsage,
            totalMemory: totalMemory,
            availableMemory: availableMemory,
            utilization: utilization,
            pressureLevel: pressureLevel,
            timestamp: Date()
        )
    }
    
    // MARK: - 内存监控工具
    
    /// 开始内存监控
    static func startMemoryMonitoring(interval: TimeInterval = 10.0, callback: @escaping (DetailedMemoryInfo) -> Void) -> Timer {
        return Timer.scheduledTimer(withTimeInterval: interval, repeats: true) { _ in
            let memoryInfo = getDetailedMemoryInfo()
            callback(memoryInfo)
        }
    }
    
    /// 记录内存使用峰值
    private static var _peakMemoryUsage: UInt64 = 0
    
    static var peakMemoryUsage: UInt64 {
        let current = getCurrentMemoryUsage()
        if current > _peakMemoryUsage {
            _peakMemoryUsage = current
        }
        return _peakMemoryUsage
    }
    
    /// 重置峰值记录
    static func resetPeakMemoryUsage() {
        _peakMemoryUsage = 0
    }
    
    // MARK: - 内存优化建议
    
    /// 获取内存优化建议
    static func getMemoryOptimizationSuggestions() -> [MemoryOptimizationSuggestion] {
        var suggestions: [MemoryOptimizationSuggestion] = []
        let currentUsage = getCurrentMemoryUsage()
        let pressureLevel = getComprehensiveMemoryPressureLevel()
        
        switch pressureLevel {
        case .normal:
            if currentUsage > MediaManagementConfig.Memory.optimalTarget {
                suggestions.append(.init(
                    priority: .low,
                    title: "预防性清理",
                    description: "内存使用正常，建议执行预防性缓存清理",
                    action: "清理超过30分钟的缓存"
                ))
            }
            
        case .warning:
            suggestions.append(.init(
                priority: .medium,
                title: "内存警告",
                description: "内存使用接近阈值，建议清理不必要的缓存",
                action: "清理图片缓存和缩略图缓存"
            ))
            
        case .critical:
            suggestions.append(.init(
                priority: .high,
                title: "内存紧急",
                description: "内存使用过高，需要立即采取行动",
                action: "暂停导入操作，清理所有缓存"
            ))
            
        case .emergency:
            suggestions.append(.init(
                priority: .urgent,
                title: "内存极限",
                description: "内存即将耗尽，需要紧急处理",
                action: "强制清理所有缓存，重启部分服务"
            ))
        }
        
        return suggestions
    }
    
    // MARK: - 调试工具
    
    /// 打印当前内存状态
    static func logCurrentMemoryState() {
        let info = getDetailedMemoryInfo()
        logger.info("""
        📊 内存状态报告:
        - 当前使用: \(formatBytes(info.currentUsage))
        - 虚拟内存: \(formatBytes(info.virtualUsage))
        - 系统总内存: \(formatBytes(info.totalMemory))
        - 使用率: \(formatMemoryUtilization(info.utilization))
        - 压力级别: \(info.pressureLevel.description)
        - 峰值使用: \(formatBytes(peakMemoryUsage))
        """)
    }
}

// MARK: - 支持数据结构

/// 内存压力级别
enum MemoryPressureLevel: Int, Comparable, CaseIterable {
    case normal = 0
    case warning = 1
    case critical = 2
    case emergency = 3
    
    var description: String {
        switch self {
        case .normal: return "正常"
        case .warning: return "警告"
        case .critical: return "紧急"
        case .emergency: return "极限"
        }
    }
    
    var emoji: String {
        switch self {
        case .normal: return "🟢"
        case .warning: return "🟡"
        case .critical: return "🟠"
        case .emergency: return "🔴"
        }
    }
    
    static func < (lhs: MemoryPressureLevel, rhs: MemoryPressureLevel) -> Bool {
        return lhs.rawValue < rhs.rawValue
    }
}

/// 详细内存信息
struct DetailedMemoryInfo {
    let currentUsage: UInt64
    let virtualUsage: UInt64
    let totalMemory: UInt64
    let availableMemory: UInt64
    let utilization: Double
    let pressureLevel: MemoryPressureLevel
    let timestamp: Date
    
    var formattedCurrentUsage: String {
        return MemoryUtils.formatBytes(currentUsage)
    }
    
    var formattedUtilization: String {
        return MemoryUtils.formatMemoryUtilization(utilization)
    }
}

/// 内存优化建议
struct MemoryOptimizationSuggestion {
    enum Priority {
        case low, medium, high, urgent
        
        var emoji: String {
            switch self {
            case .low: return "💡"
            case .medium: return "⚠️"
            case .high: return "🚨"
            case .urgent: return "🔥"
            }
        }
    }
    
    let priority: Priority
    let title: String
    let description: String
    let action: String
} 