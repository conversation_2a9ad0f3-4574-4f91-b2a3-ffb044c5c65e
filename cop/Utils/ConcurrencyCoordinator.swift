import Foundation
import os.log

// MARK: - 并发协调器
/// 统一管理全局并发操作，避免资源冲突和过度并发
final class ConcurrencyCoordinator: ConfigurableService, @unchecked Sendable {
    
    static let shared = ConcurrencyCoordinator()
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.app", category: "ConcurrencyCoordinator")
    private let coordinatorQueue = DispatchQueue(label: "com.cop.concurrency.coordinator", qos: .utility)
    
    // 操作计数器
    private var activeOperations: [OperationType: Int] = [:]
    private let operationLock = NSLock()
    
    // 等待队列
    private var waitingOperations: [OperationType: [() -> Void]] = [:]
    
    // 动态配置
    private var dynamicConfig = DynamicConcurrencyConfig()
    
    private init() {
        setupConfigurationObserver()
        initializeOperationCounters()
        logInfo("🔀 并发协调器已启动")
    }
    
    // MARK: - ConfigurableService
    func updateConfiguration() {
        coordinatorQueue.async { [weak self] in
            self?.dynamicConfig = DynamicConcurrencyConfig()
            self?.logger.info("📝 更新并发协调器配置")
        }
    }
    
    private func setupConfigurationObserver() {
        NotificationCenter.default.addObserver(
            forName: MediaManagementConfig.configurationDidChange,
            object: nil,
            queue: OperationQueue.main
        ) { [weak self] _ in
            self?.updateConfiguration()
        }
    }
    
    private func initializeOperationCounters() {
        for operationType in OperationType.allCases {
            activeOperations[operationType] = 0
            waitingOperations[operationType] = []
        }
    }
    
    // MARK: - 并发控制API
    
    /// 请求执行操作
    /// - Parameters:
    ///   - type: 操作类型
    ///   - priority: 优先级
    ///   - operation: 要执行的操作
    /// - Returns: 操作ID，用于后续跟踪
    @discardableResult
    func requestOperation<T>(
        type: OperationType,
        priority: OperationPriority = .normal,
        operation: @escaping () async -> T
    ) async -> T? {
        
        return await withCheckedContinuation { continuation in
            coordinatorQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: nil)
                    return
                }
                
                if self.canExecuteOperation(type: type, priority: priority) {
                    // 可以立即执行
                    self.incrementOperationCount(for: type)
                    
                    Task {
                        let result = await operation()
                        self.coordinatorQueue.async {
                            self.decrementOperationCount(for: type)
                        }
                        continuation.resume(returning: result)
                    }
                } else {
                    // 需要等待
                    self.queueOperation(type: type, priority: priority) {
                        Task {
                            let result = await operation()
                            self.coordinatorQueue.async {
                                self.decrementOperationCount(for: type)
                            }
                            continuation.resume(returning: result)
                        }
                    }
                }
            }
        }
    }
    
    /// 批量操作控制
    func requestBatchOperation<T>(
        type: OperationType,
        items: [T],
        batchSize: Int? = nil,
        operation: @escaping (T) async -> Void
    ) async {
        let effectiveBatchSize = batchSize ?? getBatchSize(for: type)
        
        for batch in items.chunked(into: effectiveBatchSize) {
            await withTaskGroup(of: Void.self) { group in
                for item in batch {
                    group.addTask { [weak self] in
                        await self?.requestOperation(type: type) {
                            await operation(item)
                        }
                    }
                }
            }
            
            // 批次间延迟
            let delay = MediaManagementConfig.Concurrency.batchDelay
            try? await Task.sleep(for: .seconds(delay))
        }
    }
    
    // MARK: - 内部逻辑
    
    private func canExecuteOperation(type: OperationType, priority: OperationPriority) -> Bool {
        operationLock.lock()
        defer { operationLock.unlock() }
        
        let currentCount = activeOperations[type] ?? 0
        let maxAllowed = getMaxOperations(for: type)
        
        // 高优先级操作可以超出一定限制
        if priority == .high && currentCount < maxAllowed * 2 {
            return true
        }
        
        // 内存压力检查
        if MemoryUtils.shouldPauseHighMemoryOperations() && type.isMemoryIntensive {
            return false
        }
        
        return currentCount < maxAllowed
    }
    
    private func queueOperation(type: OperationType, priority: OperationPriority, operation: @escaping () -> Void) {
        operationLock.lock()
        defer { operationLock.unlock() }
        
        if waitingOperations[type] == nil {
            waitingOperations[type] = []
        }
        
        // 根据优先级插入到适当位置
        if priority == .high {
            waitingOperations[type]?.insert(operation, at: 0)
        } else {
            waitingOperations[type]?.append(operation)
        }
        
        logInfo("⏳ 操作已加入等待队列: \(type.rawValue), 队列长度: \(waitingOperations[type]?.count ?? 0)")
    }
    
    private func incrementOperationCount(for type: OperationType) {
        operationLock.lock()
        defer { operationLock.unlock() }
        
        activeOperations[type] = (activeOperations[type] ?? 0) + 1
        logDebug("▲ \(type.rawValue) 操作数: \(activeOperations[type] ?? 0)")
    }
    
    private func decrementOperationCount(for type: OperationType) {
        operationLock.lock()
        defer { operationLock.unlock() }
        
        let currentCount = activeOperations[type] ?? 0
        activeOperations[type] = max(0, currentCount - 1)
        
        logDebug("▼ \(type.rawValue) 操作数: \(activeOperations[type] ?? 0)")
        
        // 检查是否可以执行等待中的操作
        checkWaitingOperations(for: type)
    }
    
    private func checkWaitingOperations(for type: OperationType) {
        guard var waitingOps = waitingOperations[type], !waitingOps.isEmpty else { return }
        
        if canExecuteOperation(type: type, priority: .normal) {
            let operation = waitingOps.removeFirst()
            waitingOperations[type] = waitingOps
            
            incrementOperationCount(for: type)
            
            // 延迟一小段时间执行，避免竞争
            DispatchQueue.main.asyncAfter(deadline: .now() + MediaManagementConfig.Concurrency.operationDelay) {
                operation()
            }
        }
    }
    
    // MARK: - 配置获取
    
    private func getMaxOperations(for type: OperationType) -> Int {
        let config = MediaManagementConfig.Concurrency.self
        
        switch type {
        case .mediaImport:
            return MediaManagementConfig.dynamicMaxConcurrentOperations
        case .thumbnail:
            return config.maxThumbnailOperations
        case .database:
            return config.maxDatabaseOperations
        case .network:
            return config.maxNetworkOperations
        case .cache:
            return min(config.maxImportOperations, 2)
        case .fileIO:
            return config.maxImportOperations
        }
    }
    
    private func getBatchSize(for type: OperationType) -> Int {
        let config = MediaManagementConfig.Concurrency.self
        
        switch type {
        case .mediaImport:
            return config.maxImportBatchSize
        case .thumbnail:
            return config.maxThumbnailBatchSize
        case .database:
            return config.maxDatabaseBatchSize
        case .network:
            return 10
        case .cache:
            return 50
        case .fileIO:
            return config.largeFileBatchSize
        }
    }
    
    // MARK: - 状态查询
    
    /// 获取当前并发状态
    func getCurrentConcurrencyState() -> ConcurrencyState {
        operationLock.lock()
        defer { operationLock.unlock() }
        
        let totalActive = activeOperations.values.reduce(0, +)
        let totalWaiting = waitingOperations.values.reduce(0) { $0 + $1.count }
        
        return ConcurrencyState(
            activeOperations: activeOperations,
            waitingOperations: waitingOperations.mapValues { $0.count },
            totalActiveCount: totalActive,
            totalWaitingCount: totalWaiting,
            memoryPressureLevel: MemoryUtils.getComprehensiveMemoryPressureLevel()
        )
    }
    
    /// 强制清理等待队列
    func clearWaitingQueues() {
        operationLock.lock()
        defer { operationLock.unlock() }
        
        for type in OperationType.allCases {
            waitingOperations[type] = []
        }
        
        logWarning("🧹 已清理所有等待队列")
    }
    
    /// 等待所有操作完成
    func waitForAllOperationsToComplete(timeout: TimeInterval = 30.0) async -> Bool {
        let startTime = Date()
        
        while Date().timeIntervalSince(startTime) < timeout {
            let state = getCurrentConcurrencyState()
            if state.totalActiveCount == 0 {
                return true
            }
            
            try? await Task.sleep(for: .milliseconds(100))
        }
        
        logWarning("⏰ 等待操作完成超时")
        return false
    }
    
    // MARK: - 日志辅助
    
    private func logInfo(_ message: String) {
        if MediaManagementConfig.Logging.enableDetailedLogging {
            logger.info("\(message)")
        }
    }
    
    private func logWarning(_ message: String) {
        logger.warning("\(message)")
    }
    
    private func logDebug(_ message: String) {
        if MediaManagementConfig.Logging.enableDetailedLogging {
            logger.debug("\(message)")
        }
    }
}

// MARK: - 支持数据结构

/// 操作类型
enum OperationType: String, CaseIterable {
    case mediaImport = "导入"
    case thumbnail = "缩略图"
    case database = "数据库"
    case network = "网络"
    case cache = "缓存"
    case fileIO = "文件IO"
    
    var isMemoryIntensive: Bool {
        switch self {
        case .mediaImport, .thumbnail, .cache:
            return true
        case .database, .network, .fileIO:
            return false
        }
    }
}

/// 操作优先级
enum OperationPriority {
    case low, normal, high
}

/// 并发状态
struct ConcurrencyState {
    let activeOperations: [OperationType: Int]
    let waitingOperations: [OperationType: Int]
    let totalActiveCount: Int
    let totalWaitingCount: Int
    let memoryPressureLevel: MemoryPressureLevel
    
    var isUnderPressure: Bool {
        return memoryPressureLevel.rawValue >= MemoryPressureLevel.warning.rawValue ||
               totalActiveCount > MediaManagementConfig.Concurrency.maxGlobalConcurrentOperations
    }
}

/// 动态并发配置
private struct DynamicConcurrencyConfig {
    let maxOperations: Int
    let batchSize: Int
    let memoryFactor: Double
    
    init() {
        let memoryPressure = MemoryUtils.getComprehensiveMemoryPressureLevel()
        
        // 根据内存压力调整并发
        switch memoryPressure {
        case .normal:
            self.maxOperations = MediaManagementConfig.Concurrency.maxGlobalConcurrentOperations
            self.batchSize = MediaManagementConfig.Concurrency.maxImportBatchSize
            self.memoryFactor = 1.0
            
        case .warning:
            self.maxOperations = MediaManagementConfig.Concurrency.maxGlobalConcurrentOperations * 8 / 10
            self.batchSize = MediaManagementConfig.Concurrency.maxImportBatchSize * 8 / 10
            self.memoryFactor = 0.8
            
        case .critical:
            self.maxOperations = MediaManagementConfig.Concurrency.maxGlobalConcurrentOperations * 6 / 10
            self.batchSize = MediaManagementConfig.Concurrency.maxImportBatchSize * 6 / 10
            self.memoryFactor = 0.6
            
        case .emergency:
            self.maxOperations = max(1, MediaManagementConfig.Concurrency.maxGlobalConcurrentOperations * 4 / 10)
            self.batchSize = max(5, MediaManagementConfig.Concurrency.maxImportBatchSize * 4 / 10)
            self.memoryFactor = 0.4
        }
    }
}

// MARK: - Array扩展
extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0 ..< Swift.min($0 + size, count)])
        }
    }
} 