//
//  BrowserManager.swift
//  cop
//
//  Created by Augment Agent on 2025/6/15.
//

import Foundation
import SwiftUI
import WebKit
import Network
import OSLog

// MARK: - 简化的浏览器性能指标
struct SimpleBrowserMetrics {
    var activeWebViews: Int = 0
    var memoryUsage: UInt64 = 0

    var description: String {
        return "WebViews: \(activeWebViews), Memory: \(ByteCountFormatter.string(fromByteCount: Int64(memoryUsage), countStyle: .memory))"
    }

    var healthStatus: String {
        if memoryUsage > 500 * 1024 * 1024 { // 500MB for iPad mini A17 Pro 8GB RAM - 更宽松的阈值
            return "内存使用过高"
        } else {
            return "运行正常"
        }
    }
}

/// 统一的浏览器管理器 - 整合WebView管理、网络监控和错误处理
@MainActor
final class BrowserManager: ObservableObject {
    static let shared = BrowserManager()
    
    // MARK: - 核心状态
    @Published private(set) var activeWebViews: [UUID: WKWebView] = [:]
    @Published private(set) var memoryUsage: UInt64 = 0
    @Published private(set) var currentMetrics = SimpleBrowserMetrics()
    // MARK: - 整合的性能监控（来自BrowserOptimizer）
    @Published private(set) var performanceGrade: PerformanceGrade = .good
    @Published private(set) var optimizationMetrics = OptimizationMetrics()
    private var interactionTimes: [TimeInterval] = []

    // MARK: - 服务依赖
    private let logger = Logger(subsystem: "com.cop.browser", category: "BrowserManager")
    private let contentBlockerManager = ContentBlockerManager.shared

    // MARK: - 配置管理
    private let baseConfiguration: WKWebViewConfiguration
    
    // MARK: - 性能监控
    private var memoryCheckTimer: Timer?
    private var isMonitoring = false
    
    private init() {
        // 创建基础WebView配置
        let config = WKWebViewConfiguration()
        
        // 安全配置
        config.preferences.javaScriptCanOpenWindowsAutomatically = false
        config.preferences.isFraudulentWebsiteWarningEnabled = true
        config.preferences.minimumFontSize = 9.0

        // WebKit性能优化 - 减少消息队列拥堵
        if #available(iOS 18.0, *) {
            // 移除无效的setValue调用，使用正确的WKWebViewConfiguration属性
            config.allowsInlineMediaPlayback = true
            config.mediaTypesRequiringUserActionForPlayback = [.video, .audio]
            
            // iOS 18.4特定优化：减少WebKit网络扩展冲突
            config.websiteDataStore = WKWebsiteDataStore.nonPersistent()
            config.processPool = WKProcessPool() // 使用独立进程池避免扩展冲突
            
            // 注意：在iOS 18.4中，许多WebKit优化应该通过JavaScript而非Preferences设置
            // 移除了可能导致NSUnknownKeyException的setValue调用
        } else {
            // iOS 18以下的媒体配置
            config.mediaTypesRequiringUserActionForPlayback = [.video, .audio]
        }
        
        // 优化的JavaScript错误处理和消息队列控制
        let jsErrorHandlingScript = """
        (function() {
            // 全局错误捕获
            window.addEventListener('error', function(e) {
                console.log('🛡️ [COP] JavaScript错误已捕获:', e.message, 'at', e.filename, ':', e.lineno);
                return true; // 阻止默认错误处理
            });
            
            window.addEventListener('unhandledrejection', function(e) {
                console.log('🛡️ [COP] Promise rejection已捕获:', e.reason);
                e.preventDefault();
            });
            
            // 优化动画和Canvas操作以减少消息队列拥堵
            const originalRequestAnimationFrame = window.requestAnimationFrame;
            let animationFrameCounter = 0;
            const MAX_ANIMATION_FRAMES_PER_SECOND = 30; // 限制为30fps而非60fps
            
            window.requestAnimationFrame = function(callback) {
                animationFrameCounter++;
                if (animationFrameCounter % 2 === 0) { // 每隔一帧执行，减少消息频率
                    return originalRequestAnimationFrame.call(window, function(timestamp) {
                        try {
                            callback(timestamp);
                        } catch (error) {
                            console.log('🛡️ [COP] Animation frame错误已捕获:', error.message);
                        }
                    });
                } else {
                    return setTimeout(() => {
                        try {
                            callback(performance.now());
                        } catch (error) {
                            console.log('🛡️ [COP] Animation frame错误已捕获:', error.message);
                        }
                    }, 16); // ~60fps -> 30fps
                }
            };
            
            // 限制Canvas操作频率以减少DrawingArea消息
            if (window.HTMLCanvasElement) {
                const originalGetContext = HTMLCanvasElement.prototype.getContext;
                HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
                    if (contextType === '2d' || contextType === 'webgl' || contextType === 'webgl2') {
                        // 添加操作限制以减少消息队列负载
                        console.log('🎨 [COP] Canvas context创建已优化:', contextType);
                    }
                    return originalGetContext.call(this, contextType, contextAttributes);
                };
            }
            
            // 优化WebGL操作
            const originalCreateShader = WebGLRenderingContext.prototype?.createShader;
            if (originalCreateShader) {
                WebGLRenderingContext.prototype.createShader = function(type) {
                    try {
                        return originalCreateShader.call(this, type);
                    } catch (error) {
                        console.log('🛡️ [COP] WebGL shader错误已捕获:', error.message);
                        return null;
                    }
                };
            }
            
            console.log('🛡️ [COP] JavaScript错误处理和消息队列优化已初始化');
        })();
        """
        
        // 添加禁用自动播放的JavaScript
        let disableAutoplayScript = """
        (function() {
            function disableAutoplay() {
                const videos = document.querySelectorAll('video');
                videos.forEach(video => {
                    video.autoplay = false;
                    video.muted = false;
                    if (video.hasAttribute('autoplay')) {
                        video.removeAttribute('autoplay');
                    }
                });
            }
            
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', disableAutoplay);
            } else {
                disableAutoplay();
            }
            
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1 && node.tagName === 'VIDEO') {
                            node.autoplay = false;
                            node.muted = false;
                            if (node.hasAttribute('autoplay')) {
                                node.removeAttribute('autoplay');
                            }
                        }
                    });
                });
            });
            
            observer.observe(document.body || document.documentElement, {
                childList: true,
                subtree: true
            });
        })();
        """
        
        // 注入JavaScript错误处理脚本
        let jsErrorUserScript = WKUserScript(
            source: jsErrorHandlingScript,
            injectionTime: .atDocumentStart,
            forMainFrameOnly: false
        )
        config.userContentController.addUserScript(jsErrorUserScript)
        
        // 注入自动播放禁用脚本
        let userScript = WKUserScript(
            source: disableAutoplayScript,
            injectionTime: .atDocumentStart,
            forMainFrameOnly: false
        )
        config.userContentController.addUserScript(userScript)
        
        self.baseConfiguration = config
        
        // 注册WebKit消息队列优化通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleWebKitMessageQueueOptimization(_:)),
            name: NSNotification.Name("WebKitMessageQueueOptimization"),
            object: nil
        )
        
        startMonitoring()
        logger.info("✅ BrowserManager 初始化完成")
    }

    deinit {
        memoryCheckTimer?.invalidate()
    }

    // MARK: - 公共接口

    /// 创建WebView - 统一入口点
    func createWebView(for tabId: UUID, userAgent: String) -> WKWebView {
        // 复制基础配置，避免共享冲突
        let configuration = baseConfiguration.copy() as! WKWebViewConfiguration

        // 创建WebView
        let webView = WKWebView(frame: .zero, configuration: configuration)

        // 🔑 优先配置广告屏蔽，确保在任何导航前就已生效
        contentBlockerManager.configureWebView(webView)
        
        // 然后配置其他WebView属性
        configureWebView(webView, userAgent: userAgent)
        
        // 添加调试日志
        print("🛡️ [BrowserManager] WebView(\(tabId.uuidString.prefix(8)))创建完成，已应用广告屏蔽规则")

        // 注册管理
        activeWebViews[tabId] = webView
        updateMetrics()

        logger.info("🚀 WebView创建: \(tabId.uuidString.prefix(8))")
        return webView
    }

    /// 清理WebView - 统一处理
    func cleanupWebView(_ webView: WKWebView, for tabId: UUID) {
        webView.stopLoading()

        // 清理资源
        Task {
            await clearWebViewMemory(webView)
        }

        // 移除管理
        activeWebViews.removeValue(forKey: tabId)
        updateMetrics()

        logger.info("🧹 WebView清理: \(tabId.uuidString.prefix(8))")
    }

    /// 加载URL - 统一处理
    func loadURL(_ url: URL, in webView: WKWebView) {
        // 简单的HTTPS升级
        let secureURL = enforceHTTPS(for: url)

        // 创建优化请求
        let request = createNetworkRequest(for: secureURL)

        // 确保WebView已配置广告屏蔽（防御性编程）
        contentBlockerManager.configureWebView(webView)

        // 加载
        webView.load(request)

        logger.info("🌐 URL加载: \(secureURL.absoluteString.prefix(50))... (已应用广告屏蔽)")
        print("🛡️ [BrowserManager] 加载URL时已确保广告屏蔽配置: \(secureURL.host ?? "unknown")")
    }

    /// 统一URL处理
    func processURLInput(_ input: String) -> URL? {
        let trimmedInput = input.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedInput.isEmpty else { return nil }

        // 1. 完整URL检查
        if let url = URL(string: trimmedInput), url.scheme != nil {
            return enforceHTTPS(for: url)
        }

        // 2. 域名格式检查
        if isValidDomain(trimmedInput) {
            if let url = URL(string: "https://\(trimmedInput)") {
                return enforceHTTPS(for: url)
            }
        }

        // 3. 搜索查询
        let query = trimmedInput.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        return URL(string: "https://www.google.com/search?q=\(query)")
    }

    /// 统一URL安全验证
    func validateURLSecurity(_ url: URL) -> Bool {
        // 简化版本：基本的URL验证
        return url.scheme == "https" || url.scheme == "http"
    }

    /// 简单的HTTPS强制升级
    private func enforceHTTPS(for url: URL) -> URL {
        if url.scheme == "http" {
            var components = URLComponents(url: url, resolvingAgainstBaseURL: false)
            components?.scheme = "https"
            return components?.url ?? url
        }
        return url
    }

    /// 内存优化（针对iPad mini A17 Pro 8GB RAM优化）
    func optimizeMemory() async {
        let usage = getCurrentMemoryUsage()
        memoryUsage = usage
        currentMetrics.memoryUsage = usage

        // 优化的内存压力检测 - 适应8GB设备
        let threshold: UInt64 = 500 * 1024 * 1024 // 从300MB增加到500MB，更适合8GB设备
        if usage > threshold {
            await performMemoryCleanup()
        }
    }

    // MARK: - 网络错误处理

    /// 简化的网络错误处理
    func handleNetworkError(_ error: Error, context: String = "", webView: WKWebView? = nil) {
        logger.error("🚨 网络错误: \(error.localizedDescription) - 上下文: \(context)")

        // 如果有WebView，显示错误页面
        if let webView = webView {
            Task { @MainActor in
                await self.handleWebViewError(error, webView: webView)
            }
        }
    }

    /// 创建简化的网络请求
    func createNetworkRequest(for url: URL) -> URLRequest {
        var request = URLRequest(url: url)

        // 基础配置
        request.timeoutInterval = 30.0
        request.cachePolicy = .useProtocolCachePolicy

        // 添加基础请求头
        request.setValue("cop Browser/1.0", forHTTPHeaderField: "User-Agent")
        request.setValue("gzip, deflate, br", forHTTPHeaderField: "Accept-Encoding")
        request.setValue("text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", forHTTPHeaderField: "Accept")

        return request
    }

    // MARK: - 监控和性能管理

    /// 开始性能监控
    func startMonitoring() {
        guard !isMonitoring else { return }
        isMonitoring = true

        // 内存检查 - 每30秒
        memoryCheckTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.optimizeMemory()
            }
        }

        logger.info("🔄 开始性能监控")
    }

    /// 停止性能监控
    func stopMonitoring() {
        isMonitoring = false
        memoryCheckTimer?.invalidate()
        memoryCheckTimer = nil
        logger.info("⏹️ 停止性能监控")
    }

    // MARK: - 私有方法

    private func updateMetrics() {
        currentMetrics.activeWebViews = activeWebViews.count
        currentMetrics.memoryUsage = getCurrentMemoryUsage()
    }

    private func configureWebView(_ webView: WKWebView, userAgent: String) {
        // 用户代理
        webView.customUserAgent = userAgent

        // 基础属性
        webView.allowsLinkPreview = true
        webView.allowsBackForwardNavigationGestures = true
    }

    private func handleWebViewError(_ error: Error, webView: WKWebView) async {
        let nsError = error as NSError

        switch nsError.code {
        case NSURLErrorNotConnectedToInternet, NSURLErrorNetworkConnectionLost:
            await showOfflineErrorPage(webView: webView)
        case NSURLErrorTimedOut:
            await showTimeoutErrorPage(webView: webView)
        default:
            break
        }
    }

    private func showOfflineErrorPage(webView: WKWebView) async {
        let errorHTML = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>网络连接错误</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; text-align: center; padding: 40px; }
                .error-icon { font-size: 64px; margin-bottom: 20px; }
                .error-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                .error-message { font-size: 16px; color: #666; margin-bottom: 30px; }
                .retry-button { background: #007AFF; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 16px; cursor: pointer; }
            </style>
        </head>
        <body>
            <div class="error-icon">📡</div>
            <div class="error-title">网络连接失败</div>
            <div class="error-message">请检查您的网络连接后重试</div>
            <button class="retry-button" onclick="window.location.reload()">重新加载</button>
        </body>
        </html>
        """
        webView.loadHTMLString(errorHTML, baseURL: nil)
    }

    private func showTimeoutErrorPage(webView: WKWebView) async {
        let errorHTML = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>连接超时</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; text-align: center; padding: 40px; }
                .error-icon { font-size: 64px; margin-bottom: 20px; }
                .error-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                .error-message { font-size: 16px; color: #666; margin-bottom: 30px; }
                .retry-button { background: #007AFF; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 16px; cursor: pointer; }
            </style>
        </head>
        <body>
            <div class="error-icon">⏱️</div>
            <div class="error-title">连接超时</div>
            <div class="error-message">服务器响应时间过长，请稍后重试</div>
            <button class="retry-button" onclick="window.location.reload()">重新加载</button>
        </body>
        </html>
        """
        webView.loadHTMLString(errorHTML, baseURL: nil)
    }

    private func isValidDomain(_ input: String) -> Bool {
        let domainRegex = "^([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)+(com|net|org|edu|gov|mil|biz|info|mobi|name|aero|jobs|museum|[a-z]{2})$"
        let domainPredicate = NSPredicate(format: "SELF MATCHES %@", domainRegex)
        return domainPredicate.evaluate(with: input.lowercased())
    }

    private func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }

        if kerr == KERN_SUCCESS {
            return info.resident_size
        } else {
            return 0
        }
    }

    private func clearWebViewMemory(_ webView: WKWebView) async {
        // 清理WebView内存
        await webView.configuration.websiteDataStore.removeData(
            ofTypes: WKWebsiteDataStore.allWebsiteDataTypes(),
            modifiedSince: Date.distantPast
        )
    }

    private func performMemoryCleanup() async {
        logger.info("🧹 执行内存清理")

        // 清理不活跃的WebView
        let inactiveWebViews = activeWebViews.filter { _, webView in
            webView.superview == nil
        }

        for (tabId, webView) in inactiveWebViews {
            await clearWebViewMemory(webView)
            activeWebViews.removeValue(forKey: tabId)
        }

        // 强制垃圾回收
        autoreleasepool {
            // 触发内存回收
        }

        updateMetrics()
    }

    // MARK: - 整合的性能监控方法（来自BrowserOptimizer）

    /// 记录用户交互时间
    func recordInteractionTime(_ time: TimeInterval) {
        interactionTimes.append(time)

        // 保持历史记录在合理范围内
        if interactionTimes.count > 20 {
            interactionTimes.removeFirst()
        }

        // 更新性能等级
        updatePerformanceGrade()

        // 更新优化指标
        optimizationMetrics.updateAverageTime(interactionTimes)
    }

    /// 获取推荐的动画持续时间
    func getRecommendedAnimationDuration() -> TimeInterval {
        switch performanceGrade {
        case .excellent:
            return 0.3
        case .good:
            return 0.25
        case .fair:
            return 0.2
        case .poor:
            return 0.15
        }
    }

    /// 获取性能建议
    func getPerformanceRecommendations() -> [String] {
        var recommendations: [String] = []

        switch performanceGrade {
        case .excellent:
            recommendations.append("✅ 性能优秀，继续保持")
        case .good:
            recommendations.append("👍 性能良好")
        case .fair:
            recommendations.append("⚠️ 建议减少同时打开的标签页")
        case .poor:
            recommendations.append("🔴 建议关闭部分标签页以提升性能")
            recommendations.append("🔴 考虑重启应用以释放内存")
        }

        return recommendations
    }

    private func updatePerformanceGrade() {
        guard !interactionTimes.isEmpty else { return }

        let averageTime = interactionTimes.reduce(0, +) / Double(interactionTimes.count)

        switch averageTime {
        case 0...0.05:
            performanceGrade = .excellent
        case 0.05...0.1:
            performanceGrade = .good
        case 0.1...0.2:
            performanceGrade = .fair
        default:
            performanceGrade = .poor
        }
    }

    func createNewWebView() -> WKWebView {
        let configuration = baseConfiguration.copy() as! WKWebViewConfiguration
        
        // 应用安全配置
        SecurityService.shared.applySecurityConfiguration(to: configuration)
        let webView = WKWebView(frame: .zero, configuration: configuration)
        
        // 🔑 关键修复：应用广告屏蔽规则
        ContentBlockerManager.shared.configureWebView(webView)
        
        return webView
    }

    @objc private func handleWebKitMessageQueueOptimization(_ notification: Notification) {
        // 处理WebKit消息队列优化通知
        logger.info("🔄 WebKit消息队列优化通知")
    }
}

// MARK: - 性能监控相关类型（整合自BrowserOptimizer）

enum PerformanceGrade: String, CaseIterable {
    case excellent = "优秀"
    case good = "良好"
    case fair = "一般"
    case poor = "需要改进"

    var color: Color {
        switch self {
        case .excellent: return .green
        case .good: return .blue
        case .fair: return .orange
        case .poor: return .red
        }
    }
}

struct OptimizationMetrics {
    var averageInteractionTime: TimeInterval = 0.0
    var optimizationCount: Int = 0
    var lastOptimizationDate: Date = Date()

    mutating func updateAverageTime(_ times: [TimeInterval]) {
        guard !times.isEmpty else { return }
        averageInteractionTime = times.reduce(0, +) / Double(times.count)
        optimizationCount += 1
        lastOptimizationDate = Date()
    }
}

// MARK: - WebView内存管理集成优化（第二阶段完善）
extension BrowserManager {
    
    /// 集成内存管理器
    func integrateWithMemoryManager() {
        let memoryManager = MediaMemoryManager.shared
        
        // 注册BrowserManager为内存管理组件
        memoryManager.registerWebViewManager(self)
        
        // 监听内存压力事件
        NotificationCenter.default.addObserver(
            forName: .memoryPressureReleaseViews,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.handleMemoryPressureFromManager()
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: .memoryEmergencyState,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.handleEmergencyMemoryState()
            }
        }
        
        logger.info("🔗 BrowserManager已与内存管理器集成")
    }
    
    /// 优化WebView内存使用（扩展版本）
    func optimizeWebViewMemory() async {
        logger.info("🧹 开始WebView内存优化")
        
        // 1. 清理不活跃的WebView
        await cleanupInactiveWebViews()
        
        // 2. 优化活跃WebView的内存使用
        await optimizeActiveWebViews()
        
        // 3. 清理WebView缓存
        await cleanupWebViewCaches()
        
        // 4. 更新内存使用统计
        await updateMemoryMetrics()
        
        logger.info("✅ WebView内存优化完成")
    }
    
    /// 获取WebView内存使用详情
    func getDetailedMemoryUsage() async -> WebViewMemoryUsage {
        var totalMemory: UInt64 = 0
        var webViewDetails: [WebViewMemoryDetail] = []
        
        for (id, webView) in activeWebViews {
            let detail = await getWebViewMemoryDetail(id: id, webView: webView)
            webViewDetails.append(detail)
            totalMemory += detail.memoryUsage
        }
        
        return WebViewMemoryUsage(
            totalMemory: totalMemory,
            activeWebViewCount: activeWebViews.count,
            details: webViewDetails,
            cacheSize: await getWebViewCacheSize()
        )
    }
    
    /// 智能WebView挂起
    func suspendInactiveWebViews() async {
        let inactivityThreshold: TimeInterval = 300 // 5分钟不活跃
        let currentTime = Date()
        
        for (id, webView) in activeWebViews {
            let lastActivity = getLastActivityTime(for: id)
            
            if currentTime.timeIntervalSince(lastActivity) > inactivityThreshold {
                await suspendWebView(id: id, webView: webView)
            }
        }
    }
    
    /// 预测性WebView内存管理
    func predictiveMemoryManagement() async {
        let memoryManager = MediaMemoryManager.shared
        let currentMemoryUsage = memoryManager.currentMemoryUsage
        
        // 如果内存使用接近警告阈值，主动优化
        if currentMemoryUsage > 150 * 1024 * 1024 { // 150MB
            logger.info("🔮 触发预测性WebView内存管理")
            
            // 根据内存压力级别采取不同策略
            switch memoryManager.memoryPressureLevel {
            case .normal:
                await optimizeBackgroundWebViews()
                
            case .warning:
                await suspendInactiveWebViews()
                await cleanupWebViewCaches()
                
            case .critical:
                await forceCleanupWebViews()
                
            case .emergency:
                await emergencyWebViewCleanup()
            }
        }
    }
    
    // MARK: - 私有优化方法
    
    private func cleanupInactiveWebViews() async {
        let inactiveWebViews = activeWebViews.filter { (id, _) in
            let lastActivity = getLastActivityTime(for: id)
            return Date().timeIntervalSince(lastActivity) > 600 // 10分钟不活跃
        }
        
        for (id, webView) in inactiveWebViews {
            await removeWebView(id: id, webView: webView)
        }
        
        logger.info("🗑️ 清理了 \(inactiveWebViews.count) 个不活跃的WebView")
    }
    
    private func optimizeActiveWebViews() async {
        for (id, webView) in activeWebViews {
            await optimizeSingleWebView(id: id, webView: webView)
        }
    }
    
    private func optimizeSingleWebView(id: UUID, webView: WKWebView) async {
        // 清理JavaScript缓存
        try? await webView.evaluateJavaScript("""
            // 清理JavaScript对象引用
            if (window.gc) { window.gc(); }
            
            // 清理图片缓存
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                if (img.complete && img.naturalHeight !== 0) {
                    img.removeAttribute('src');
                }
            });
            
            // 清理不必要的DOM节点
            const unusedElements = document.querySelectorAll('[data-cleanup="true"]');
            unusedElements.forEach(el => el.remove());
            
            console.log('🧹 WebView JavaScript内存优化完成');
        """)
        
        // 设置内存压力优化配置
        webView.configuration.preferences.minimumFontSize = 12.0 // 减少渲染负担
    }
    
    private func cleanupWebViewCaches() async {
        let dataStore = WKWebsiteDataStore.default()
        let dataTypes = WKWebsiteDataStore.allWebsiteDataTypes()
        
        // 清理过期的网站数据
        let oneWeekAgo = Date().addingTimeInterval(-7 * 24 * 60 * 60)
        await dataStore.removeData(ofTypes: dataTypes, modifiedSince: oneWeekAgo)
        
        logger.info("🧽 WebView缓存清理完成")
    }
    
    private func getWebViewMemoryDetail(id: UUID, webView: WKWebView) async -> WebViewMemoryDetail {
        // 获取WebView的内存使用情况（估算）
        let pageMemoryUsage = await estimateWebViewMemoryUsage(webView)
        
        return WebViewMemoryDetail(
            id: id,
            url: webView.url?.absoluteString ?? "about:blank",
            memoryUsage: pageMemoryUsage,
            lastActivity: getLastActivityTime(for: id),
            isVisible: webView.superview != nil
        )
    }
    
    private func estimateWebViewMemoryUsage(_ webView: WKWebView) async -> UInt64 {
        // 基础WebView内存占用估算
        var estimatedUsage: UInt64 = 30 * 1024 * 1024 // 30MB 基础使用
        
        // 根据页面复杂度调整估算
        let pageInfo = await getPageComplexityInfo(webView)
        
        // DOM节点数量影响
        estimatedUsage += UInt64(pageInfo.domNodeCount) * 1024 // 每个节点1KB
        
        // 图片数量影响
        estimatedUsage += UInt64(pageInfo.imageCount) * 50 * 1024 // 每张图片50KB
        
        // JavaScript对象影响
        estimatedUsage += UInt64(pageInfo.jsObjectCount) * 512 // 每个JS对象512B
        
        return min(estimatedUsage, 200 * 1024 * 1024) // 最大200MB
    }
    
    private func getPageComplexityInfo(_ webView: WKWebView) async -> PageComplexityInfo {
        let jsCode = """
        (function() {
            return {
                domNodeCount: document.getElementsByTagName('*').length,
                imageCount: document.images.length,
                jsObjectCount: Object.keys(window).length
            };
        })();
        """
        
        do {
            let result = try await webView.evaluateJavaScript(jsCode) as? [String: Any]
            return PageComplexityInfo(
                domNodeCount: result?["domNodeCount"] as? Int ?? 0,
                imageCount: result?["imageCount"] as? Int ?? 0,
                jsObjectCount: result?["jsObjectCount"] as? Int ?? 0
            )
        } catch {
            return PageComplexityInfo(domNodeCount: 0, imageCount: 0, jsObjectCount: 0)
        }
    }
    
    private func getWebViewCacheSize() async -> UInt64 {
        let dataStore = WKWebsiteDataStore.default()
        let dataTypes = Set([WKWebsiteDataTypeDiskCache, WKWebsiteDataTypeMemoryCache])
        
        let records = await dataStore.dataRecords(ofTypes: dataTypes)
        
        // 估算缓存大小（实际获取比较复杂）
        return UInt64(records.count) * 100 * 1024 // 每个记录估算100KB
    }
    
    private func handleMemoryPressureFromManager() async {
        logger.warning("⚠️ 收到内存管理器的压力信号")
        
        // 立即优化WebView内存使用
        await optimizeWebViewMemory()
        
        // 暂停非关键的WebView操作
        pauseNonCriticalWebViewOperations()
    }
    
    private func handleEmergencyMemoryState() async {
        logger.error("🚨 收到紧急内存状态信号")
        
        // 紧急清理所有可清理的WebView
        await emergencyWebViewCleanup()
    }
    
    private func optimizeBackgroundWebViews() async {
        for (id, webView) in activeWebViews {
            if webView.superview == nil { // 后台WebView
                await optimizeSingleWebView(id: id, webView: webView)
            }
        }
    }
    
    private func forceCleanupWebViews() async {
        // 强制清理超过一半的不活跃WebView
        let sortedWebViews = activeWebViews.sorted { (lhs, rhs) in
            getLastActivityTime(for: lhs.key) < getLastActivityTime(for: rhs.key)
        }
        
        let webViewsToRemove = sortedWebViews.prefix(sortedWebViews.count / 2)
        
        for (id, webView) in webViewsToRemove {
            await removeWebView(id: id, webView: webView)
        }
    }
    
    private func emergencyWebViewCleanup() async {
        // 紧急状态：只保留当前活跃的WebView
        let activeWebView = activeWebViews.first(where: { $0.value.superview != nil })
        
        for (id, webView) in activeWebViews {
            if id != activeWebView?.key {
                await removeWebView(id: id, webView: webView)
            }
        }
        
        // 清理所有缓存
        await cleanupWebViewCaches()
    }
    
    private func suspendWebView(id: UUID, webView: WKWebView) async {
        // 挂起WebView（减少内存使用但保持状态）
        await MainActor.run {
            webView.setAllMediaPlaybackSuspended(true)
            
            // 移除从视图层级但保持引用
            webView.removeFromSuperview()
        }
        
        logger.info("💤 WebView \(id) 已挂起")
    }
    
    private func removeWebView(id: UUID, webView: WKWebView) async {
        webView.stopLoading()
        webView.removeFromSuperview()
        activeWebViews.removeValue(forKey: id)
        
        logger.info("🗑️ WebView \(id) 已移除")
    }
    
    private func pauseNonCriticalWebViewOperations() {
        // 暂停自动刷新、后台任务等
        Task { @MainActor in
            for (_, webView) in activeWebViews {
                webView.setAllMediaPlaybackSuspended(true)
            }
        }
    }
    
    private func getLastActivityTime(for id: UUID) -> Date {
        // 简化实现：返回当前时间减去随机间隔（实际应该跟踪真实活动时间）
        return Date().addingTimeInterval(-Double.random(in: 0...3600))
    }
    
    private func updateMemoryMetrics() async {
        let newMemoryUsage = getCurrentMemoryUsage()
        
        await MainActor.run {
            memoryUsage = newMemoryUsage
            currentMetrics = SimpleBrowserMetrics(
                activeWebViews: activeWebViews.count,
                memoryUsage: newMemoryUsage
            )
        }
    }
}

// MARK: - WebView内存使用数据结构
struct WebViewMemoryUsage {
    let totalMemory: UInt64
    let activeWebViewCount: Int
    let details: [WebViewMemoryDetail]
    let cacheSize: UInt64
    
    var formattedTotalMemory: String {
        ByteCountFormatter.string(fromByteCount: Int64(totalMemory), countStyle: .memory)
    }
    
    var averageMemoryPerWebView: UInt64 {
        guard activeWebViewCount > 0 else { return 0 }
        return totalMemory / UInt64(activeWebViewCount)
    }
}

struct WebViewMemoryDetail {
    let id: UUID
    let url: String
    let memoryUsage: UInt64
    let lastActivity: Date
    let isVisible: Bool
    
    var formattedMemoryUsage: String {
        ByteCountFormatter.string(fromByteCount: Int64(memoryUsage), countStyle: .memory)
    }
    
    var inactivityDuration: TimeInterval {
        Date().timeIntervalSince(lastActivity)
    }
}

struct PageComplexityInfo {
    let domNodeCount: Int
    let imageCount: Int
    let jsObjectCount: Int
    
    var complexityScore: Int {
        return domNodeCount + (imageCount * 10) + (jsObjectCount * 5)
    }
}
