import Foundation
import OSLog
import UIKit

// MARK: - 缩略图磁盘缓存管理器（第二阶段性能优化）
final class ThumbnailDiskCache: @unchecked Sendable {
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.browser", category: "ThumbnailDiskCache")
    private let fileManager = FileManager.default
    private let cacheDirectory: URL
    private let ioQueue = DispatchQueue(label: "thumbnail.disk.io", qos: .utility)
    
    // 配置
    private struct Config {
        static let maxCacheSize: Int64 = 500 * 1024 * 1024 // 500MB
        static let cleanupThreshold: Double = 0.9 // 90%时开始清理
        static let targetCleanupRatio: Double = 0.7 // 清理到70%
        static let cacheExpirationDays = 30 // 30天过期
        static let metadataFileName = ".cache_metadata"
    }
    
    // 缓存元数据
    private struct CacheMetadata: Codable {
        let fileSize: Int64
        let creationDate: Date
        let lastAccessDate: Date
        let accessCount: Int
        
        init(fileSize: Int64) {
            self.fileSize = fileSize
            self.creationDate = Date()
            self.lastAccessDate = Date()
            self.accessCount = 1
        }
        
        init(fileSize: Int64, creationDate: Date, lastAccessDate: Date, accessCount: Int) {
            self.fileSize = fileSize
            self.creationDate = creationDate
            self.lastAccessDate = lastAccessDate
            self.accessCount = accessCount
        }
        
        func updatingAccess() -> CacheMetadata {
            return CacheMetadata(
                fileSize: fileSize,
                creationDate: creationDate,
                lastAccessDate: Date(),
                accessCount: accessCount + 1
            )
        }
    }
    
    // MARK: - 初始化
    
    init() {
        // 设置缓存目录结构
        let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        self.cacheDirectory = documentsURL.appendingPathComponent("ThumbnailCache")
        
        setupCacheDirectory()
        logger.info("💾 磁盘缓存管理器已启动，目录: \(self.cacheDirectory.path)")
    }
    
    // MARK: - 公共接口
    
    /// 获取缩略图URL路径
    func getThumbnailURL(for mediaFileID: UUID, size: OptimizedThumbnailManager.ThumbnailSize) -> URL {
        let sizeDirectory = cacheDirectory.appendingPathComponent(size.directory)
        return sizeDirectory.appendingPathComponent("\(mediaFileID.uuidString).jpg")
    }
    
    /// 获取缩略图图片
    func getImage(for mediaFileID: UUID, size: OptimizedThumbnailManager.ThumbnailSize) async -> UIImage? {
        let fileURL = getThumbnailURL(for: mediaFileID, size: size)
        
        return await withCheckedContinuation { continuation in
            ioQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: nil)
                    return
                }
                
                // 检查文件是否存在
                guard self.fileManager.fileExists(atPath: fileURL.path) else {
                    continuation.resume(returning: nil)
                    return
                }
                
                // 读取图片
                guard let imageData = try? Data(contentsOf: fileURL),
                      let image = UIImage(data: imageData) else {
                    // 文件损坏，删除
                    try? self.fileManager.removeItem(at: fileURL)
                    continuation.resume(returning: nil)
                    return
                }
                
                // 更新访问元数据
                Task {
                    await self.updateAccessMetadata(for: fileURL)
                }
                
                continuation.resume(returning: image)
            }
        }
    }
    
    /// 保存缩略图
    func saveImage(_ image: UIImage, for mediaFileID: UUID, size: OptimizedThumbnailManager.ThumbnailSize) async throws {
        let fileURL = getThumbnailURL(for: mediaFileID, size: size)
        
        guard let imageData = image.jpegData(compressionQuality: size.compressionQuality) else {
            throw ThumbnailDiskCacheError.imageEncodingFailed
        }
        
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            ioQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: ThumbnailDiskCacheError.cacheUnavailable)
                    return
                }
                
                do {
                    // 确保目录存在
                    try self.ensureDirectoryExists(fileURL.deletingLastPathComponent())
                    
                    // 写入文件
                    try imageData.write(to: fileURL)
                    
                    // 保存元数据
                    let metadata = CacheMetadata(fileSize: Int64(imageData.count))
                    try self.saveMetadata(metadata, for: fileURL)
                    
                    continuation.resume()
                    
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    /// 删除指定缩略图
    func removeImage(for mediaFileID: UUID, size: OptimizedThumbnailManager.ThumbnailSize) async {
        let fileURL = getThumbnailURL(for: mediaFileID, size: size)
        
        await withCheckedContinuation { continuation in
            ioQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                try? self.fileManager.removeItem(at: fileURL)
                self.removeMetadata(for: fileURL)
                
                continuation.resume()
            }
        }
    }
    
    /// 清理过期文件
    func clearOldItems(olderThan interval: TimeInterval) async {
        await withCheckedContinuation { continuation in
            ioQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                let cutoffDate = Date().addingTimeInterval(-interval)
                
                do {
                    let fileURLs = try FileManager.default.contentsOfDirectory(at: self.cacheDirectory, includingPropertiesForKeys: [.contentModificationDateKey])
                    
                    for fileURL in fileURLs {
                        if let resourceValues = try? fileURL.resourceValues(forKeys: [.contentModificationDateKey]),
                           let modificationDate = resourceValues.contentModificationDate,
                           modificationDate < cutoffDate {
                            do {
                                try FileManager.default.removeItem(at: fileURL)
                                self.logger.info("删除过期缓存文件: \(fileURL.lastPathComponent)")
                            } catch {
                                self.logger.error("删除过期缓存文件失败: \(error)")
                            }
                        }
                    }
                } catch {
                    self.logger.error("清理过期缓存失败: \(error)")
                }
                
                continuation.resume()
            }
        }
    }
    
    /// 计算缓存大小
    func calculateCacheSize() async -> Int64 {
        return await withCheckedContinuation { continuation in
            ioQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: 0)
                    return
                }
                
                var totalSize: Int64 = 0
                
                self.enumerateAllCacheFiles { _, metadata in
                    totalSize += metadata.fileSize
                }
                
                continuation.resume(returning: totalSize)
            }
        }
    }
    
    /// 清理到指定大小
    func cleanupToSize(_ targetSize: Int64) async {
        await withCheckedContinuation { continuation in
            ioQueue.async(flags: .barrier) { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                do {
                    let fileURLs = try FileManager.default.contentsOfDirectory(at: self.cacheDirectory, includingPropertiesForKeys: [.fileSizeKey, .contentModificationDateKey])
                    
                    // 按修改时间排序（最老的在前）
                    let sortedFiles = fileURLs.compactMap { url -> (URL, Date, Int64)? in
                        guard let resourceValues = try? url.resourceValues(forKeys: [.fileSizeKey, .contentModificationDateKey]),
                              let fileSize = resourceValues.fileSize,
                              let modificationDate = resourceValues.contentModificationDate else {
                            return nil
                        }
                        return (url, modificationDate, Int64(fileSize))
                    }.sorted { $0.1 < $1.1 }
                    
                    var currentSize = sortedFiles.reduce(0) { result, item in
                        result + item.2
                    }
                    
                    for (url, _, fileSize) in sortedFiles {
                        if currentSize <= targetSize {
                            break
                        }
                        
                        do {
                            try FileManager.default.removeItem(at: url)
                            currentSize -= fileSize
                            self.logger.info("删除缓存文件: \(url.lastPathComponent)")
                        } catch {
                            self.logger.error("删除缓存文件失败: \(error)")
                        }
                    }
                } catch {
                    self.logger.error("清理缓存失败: \(error)")
                }
                
                continuation.resume()
            }
        }
    }
    
    /// 清理所有缓存
    func clearAll() async {
        await withCheckedContinuation { continuation in
            ioQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                do {
                    let contents = try self.fileManager.contentsOfDirectory(at: self.cacheDirectory, includingPropertiesForKeys: nil)
                    var removedCount = 0
                    
                    for itemURL in contents {
                        try self.fileManager.removeItem(at: itemURL)
                        removedCount += 1
                    }
                    
                    self.logger.info("🧹 清理了所有缓存，共 \(removedCount) 个项目")
                    
                } catch {
                    self.logger.error("清理所有缓存失败: \(error.localizedDescription)")
                }
                
                continuation.resume()
            }
        }
    }
    
    /// 获取当前缓存大小
    func getCurrentCacheSize() async -> UInt64 {
        return await withCheckedContinuation { continuation in
            ioQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: 0)
                    return
                }
                
                var totalSize: UInt64 = 0
                
                // 遍历缓存目录计算总大小
                do {
                    let fileURLs = try FileManager.default.contentsOfDirectory(at: self.cacheDirectory, includingPropertiesForKeys: [.fileSizeKey])
                    
                    for fileURL in fileURLs {
                        if let resourceValues = try? fileURL.resourceValues(forKeys: [.fileSizeKey]),
                           let fileSize = resourceValues.fileSize {
                            totalSize += UInt64(fileSize)
                        }
                    }
                } catch {
                    self.logger.error("计算缓存大小失败: \(error)")
                }
                
                continuation.resume(returning: totalSize)
            }
        }
    }
    
    /// 优化性能
    func optimizeForPerformance() async {
        let currentSize = await getCurrentCacheSize()
        
        // 如果缓存大小超过最大限制，清理到80%
        if currentSize > UInt64(Config.maxCacheSize) {
            let targetSize = Int64(Double(Config.maxCacheSize) * 0.8)
            await cleanupToSize(targetSize)
        }
        
        // 清理超过30天的文件
        await clearOldItems(olderThan: 30 * 24 * 3600) // 30天
    }
    
    // MARK: - 私有方法
    
    private func setupCacheDirectory() {
        do {
            try fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
            
            // 创建子目录
            for size in OptimizedThumbnailManager.ThumbnailSize.allCases {
                let sizeDirectory = cacheDirectory.appendingPathComponent(size.directory)
                try fileManager.createDirectory(at: sizeDirectory, withIntermediateDirectories: true)
            }
            
        } catch {
            logger.error("创建缓存目录失败: \(error.localizedDescription)")
        }
    }
    
    private func ensureDirectoryExists(_ directory: URL) throws {
        if !fileManager.fileExists(atPath: directory.path) {
            try fileManager.createDirectory(at: directory, withIntermediateDirectories: true)
        }
    }
    
    private func getMetadataURL(for fileURL: URL) -> URL {
        let metadataName = "\(fileURL.lastPathComponent).meta"
        return fileURL.deletingLastPathComponent()
            .appendingPathComponent(Config.metadataFileName)
            .appendingPathComponent(metadataName)
    }
    
    private func saveMetadata(_ metadata: CacheMetadata, for fileURL: URL) throws {
        let metadataURL = getMetadataURL(for: fileURL)
        try ensureDirectoryExists(metadataURL.deletingLastPathComponent())
        
        let data = try JSONEncoder().encode(metadata)
        try data.write(to: metadataURL)
    }
    
    private func loadMetadata(for fileURL: URL) -> CacheMetadata? {
        let metadataURL = getMetadataURL(for: fileURL)
        
        guard let data = try? Data(contentsOf: metadataURL),
              let metadata = try? JSONDecoder().decode(CacheMetadata.self, from: data) else {
            // 如果没有元数据，创建默认的
            if let attributes = try? fileManager.attributesOfItem(atPath: fileURL.path),
               let fileSize = attributes[.size] as? Int64 {
                return CacheMetadata(fileSize: fileSize)
            }
            return nil
        }
        
        return metadata
    }
    
    private func updateAccessMetadata(for fileURL: URL) async {
        await withCheckedContinuation { continuation in
            ioQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                if let metadata = self.loadMetadata(for: fileURL) {
                    let updatedMetadata = metadata.updatingAccess()
                    try? self.saveMetadata(updatedMetadata, for: fileURL)
                }
                
                continuation.resume()
            }
        }
    }
    
    private func removeMetadata(for fileURL: URL) {
        let metadataURL = getMetadataURL(for: fileURL)
        try? fileManager.removeItem(at: metadataURL)
    }
    
    // MARK: - 文件枚举
    
    private func enumerateAllCacheFiles(_ handler: (URL, CacheMetadata) -> Void) {
        guard let enumerator = fileManager.enumerator(at: cacheDirectory, includingPropertiesForKeys: [.isRegularFileKey]) else {
            return
        }
        
        for case let fileURL as URL in enumerator {
            // 跳过元数据目录
            if fileURL.path.contains(Config.metadataFileName) {
                continue
            }
            
            // 只处理常规文件
            guard let resourceValues = try? fileURL.resourceValues(forKeys: [.isRegularFileKey]),
                  resourceValues.isRegularFile == true else {
                continue
            }
            
            // 只处理图片文件
            guard fileURL.pathExtension.lowercased() == "jpg" else {
                continue
            }
            
            if let metadata = loadMetadata(for: fileURL) {
                handler(fileURL, metadata)
            }
        }
    }
    
    // MARK: - LRU评分算法
    
    private func calculateLRUScore(_ metadata: CacheMetadata) -> Double {
        let now = Date()
        let timeSinceLastAccess = now.timeIntervalSince(metadata.lastAccessDate)
        let timeSinceCreation = now.timeIntervalSince(metadata.creationDate)
        
        // 综合考虑最后访问时间、访问频率和文件年龄
        let accessFrequency = Double(metadata.accessCount)
        let recency = 1.0 / (timeSinceLastAccess + 1.0)
        let frequency = accessFrequency / (timeSinceCreation + 1.0)
        
        // 分数越低，越容易被清理
        return recency * 0.7 + frequency * 0.3
    }
    
    // MARK: - 工具方法
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .decimal
        return formatter.string(fromByteCount: bytes)
    }
    
    // MARK: - 统计信息
    
    func getCacheStatistics() async -> String {
        let totalSize = await calculateCacheSize()
        
        return await withCheckedContinuation { continuation in
            ioQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: "缓存不可用")
                    return
                }
                
                var fileCount = 0
                var oldestFile: Date?
                var newestFile: Date?
                
                self.enumerateAllCacheFiles { _, metadata in
                    fileCount += 1
                    
                    if oldestFile == nil || metadata.creationDate < oldestFile! {
                        oldestFile = metadata.creationDate
                    }
                    
                    if newestFile == nil || metadata.creationDate > newestFile! {
                        newestFile = metadata.creationDate
                    }
                }
                
                let result = """
                💾 缩略图磁盘缓存统计
                ========================
                
                📊 基本信息:
                - 缓存文件数: \(fileCount)
                - 总大小: \(self.formatBytes(totalSize))
                - 使用率: \(String(format: "%.1f", Double(totalSize) / Double(Config.maxCacheSize) * 100))%
                
                📅 时间信息:
                - 最旧文件: \(oldestFile?.formatted(date: .abbreviated, time: .shortened) ?? "无")
                - 最新文件: \(newestFile?.formatted(date: .abbreviated, time: .shortened) ?? "无")
                
                📍 缓存路径: \(self.cacheDirectory.path)
                """
                
                continuation.resume(returning: result)
            }
        }
    }
}

// MARK: - 错误类型

enum ThumbnailDiskCacheError: Error, LocalizedError {
    case cacheUnavailable
    case imageEncodingFailed
    case directoryCreationFailed
    case fileWriteFailed
    
    var errorDescription: String? {
        switch self {
        case .cacheUnavailable:
            return "磁盘缓存不可用"
        case .imageEncodingFailed:
            return "图片编码失败"
        case .directoryCreationFailed:
            return "目录创建失败"
        case .fileWriteFailed:
            return "文件写入失败"
        }
    }
}

// MARK: - 扩展：ThumbnailSize
// 注意：compressionQuality 已在 OptimizedThumbnailManager.swift 中定义