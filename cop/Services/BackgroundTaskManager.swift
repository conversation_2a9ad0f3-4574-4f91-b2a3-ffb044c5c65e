import Foundation
import UIKit
import BackgroundTasks
import OSLog

// MARK: - 后台任务管理器（第二阶段性能优化）
@MainActor
final class BackgroundTaskManager: ObservableObject {
    
    // MARK: - 单例
    static let shared = BackgroundTaskManager()
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.browser", category: "BackgroundTaskManager")
    private var backgroundTaskIdentifiers: Set<UIBackgroundTaskIdentifier> = []
    private let taskQueue = DispatchQueue(label: "background.tasks", qos: .background)
    
    // MARK: - 发布属性
    @Published var isBackgroundOptimizationEnabled: Bool = true
    @Published var lastBackgroundOptimization: Date? = nil
    @Published var backgroundTasksCount: Int = 0
    
    // MARK: - 后台任务标识符
    private struct BackgroundTaskIDs {
        static let memoryOptimization = "com.cop.browser.memory-optimization"
        static let cacheCleanup = "com.cop.browser.cache-cleanup"
        static let thumbnailGeneration = "com.cop.browser.thumbnail-generation"
        static let databaseMaintenance = "com.cop.browser.database-maintenance"
    }
    
    // MARK: - 任务配置
    private struct TaskConfig {
        static let maxBackgroundTime: TimeInterval = 30.0     // 30秒后台时间
        static let cleanupInterval: TimeInterval = 300.0      // 5分钟清理间隔
        static let optimizationInterval: TimeInterval = 1800.0 // 30分钟优化间隔
    }
    
    // MARK: - 初始化
    private init() {
        setupBackgroundTasks()
        registerForAppStateChanges()
        logger.info("🌙 后台任务管理器已启动")
    }
    
    // MARK: - 公共接口
    
    /// 启动后台优化
    func enableBackgroundOptimization() {
        isBackgroundOptimizationEnabled = true
        scheduleBackgroundTasks()
        logger.info("✅ 后台优化已启用")
    }
    
    /// 禁用后台优化
    func disableBackgroundOptimization() {
        isBackgroundOptimizationEnabled = false
        cancelAllBackgroundTasks()
        logger.info("❌ 后台优化已禁用")
    }
    
    /// 立即执行后台清理
    func performImmediateBackgroundCleanup() {
        guard isBackgroundOptimizationEnabled else { return }
        
        let taskIdentifier = beginBackgroundTask(name: "ImmediateCleanup")
        
        Task {
            await performBackgroundCleanup()
            endBackgroundTask(taskIdentifier)
        }
    }
    
    /// 调度后台任务
    func scheduleBackgroundTasks() {
        guard isBackgroundOptimizationEnabled else { return }
        
        scheduleMemoryOptimization()
        scheduleCacheCleanup()
        scheduleThumbnailGeneration()
        scheduleDatabaseMaintenance()
        
        logger.info("📅 后台任务已调度")
    }
    
    /// 取消所有后台任务
    func cancelAllBackgroundTasks() {
        BGTaskScheduler.shared.cancelAllTaskRequests()
        
        // 结束所有正在进行的后台任务
        for taskIdentifier in backgroundTaskIdentifiers {
            endBackgroundTask(taskIdentifier)
        }
        
        backgroundTaskIdentifiers.removeAll()
        backgroundTasksCount = 0
        
        logger.info("🛑 所有后台任务已取消")
    }
    
    // MARK: - 私有方法
    
    private func setupBackgroundTasks() {
        // 注册后台任务
        registerBackgroundTask(identifier: BackgroundTaskIDs.memoryOptimization) {
            await self.handleMemoryOptimization()
        }
        
        registerBackgroundTask(identifier: BackgroundTaskIDs.cacheCleanup) {
            await self.handleCacheCleanup()
        }
        
        registerBackgroundTask(identifier: BackgroundTaskIDs.thumbnailGeneration) {
            await self.handleThumbnailGeneration()
        }
        
        registerBackgroundTask(identifier: BackgroundTaskIDs.databaseMaintenance) {
            await self.handleDatabaseMaintenance()
        }
    }
    
    private func registerBackgroundTask(identifier: String, handler: @escaping () async -> Void) {
        BGTaskScheduler.shared.register(forTaskWithIdentifier: identifier, using: nil) { task in
            Task {
                await handler()
                task.setTaskCompleted(success: true)
            }
        }
    }
    
    private func registerForAppStateChanges() {
        NotificationCenter.default.addObserver(
            forName: UIApplication.didEnterBackgroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleAppDidEnterBackground()
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: UIApplication.willEnterForegroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleAppWillEnterForeground()
            }
        }
    }
    
    private func scheduleMemoryOptimization() {
        let request = BGAppRefreshTaskRequest(identifier: BackgroundTaskIDs.memoryOptimization)
        request.earliestBeginDate = Date(timeIntervalSinceNow: TaskConfig.optimizationInterval)
        
        do {
            try BGTaskScheduler.shared.submit(request)
            logger.info("📝 内存优化任务已调度")
        } catch {
            logger.error("调度内存优化任务失败: \(error.localizedDescription)")
        }
    }
    
    private func scheduleCacheCleanup() {
        let request = BGAppRefreshTaskRequest(identifier: BackgroundTaskIDs.cacheCleanup)
        request.earliestBeginDate = Date(timeIntervalSinceNow: TaskConfig.cleanupInterval)
        
        do {
            try BGTaskScheduler.shared.submit(request)
            logger.info("📝 缓存清理任务已调度")
        } catch {
            logger.error("调度缓存清理任务失败: \(error.localizedDescription)")
        }
    }
    
    private func scheduleThumbnailGeneration() {
        let request = BGProcessingTaskRequest(identifier: BackgroundTaskIDs.thumbnailGeneration)
        request.requiresNetworkConnectivity = false
        request.requiresExternalPower = false
        request.earliestBeginDate = Date(timeIntervalSinceNow: 60) // 1分钟后
        
        do {
            try BGTaskScheduler.shared.submit(request)
            logger.info("📝 缩略图生成任务已调度")
        } catch {
            logger.error("调度缩略图生成任务失败: \(error.localizedDescription)")
        }
    }
    
    private func scheduleDatabaseMaintenance() {
        let request = BGProcessingTaskRequest(identifier: BackgroundTaskIDs.databaseMaintenance)
        request.requiresNetworkConnectivity = false
        request.requiresExternalPower = true  // 需要外部电源
        request.earliestBeginDate = Date(timeIntervalSinceNow: 3600) // 1小时后
        
        do {
            try BGTaskScheduler.shared.submit(request)
            logger.info("📝 数据库维护任务已调度")
        } catch {
            logger.error("调度数据库维护任务失败: \(error.localizedDescription)")
        }
    }
    
    // MARK: - 后台任务处理器
    
    private func handleMemoryOptimization() async {
        logger.info("🧠 开始后台内存优化")
        
        // 执行内存优化
        await MainActor.run {
            Task {
                await MediaMemoryManager.shared.predictiveMemoryCleanup()
                await MediaMemoryManager.shared.smartResourceRelease()
            }
        }
        
        // 重新调度下次优化
        scheduleMemoryOptimization()
        
        logger.info("✅ 后台内存优化完成")
    }
    
    private func handleCacheCleanup() async {
        logger.info("🗑️ 开始后台缓存清理")
        
        // 执行缓存清理
        let reclaimedBytes = await ResourceOptimizer.shared.cleanupTemporaryFiles()
        logger.info("回收缓存空间: \(self.formatBytes(reclaimedBytes))")
        
        // 重新调度下次清理
        scheduleCacheCleanup()
        
        logger.info("✅ 后台缓存清理完成")
    }
    
    private func handleThumbnailGeneration() async {
        logger.info("🖼️ 开始后台缩略图生成")
        
        // 执行缩略图生成优化
        await OptimizedThumbnailManager.shared.optimizeExistingThumbnails()
        
        // 重新调度下次生成
        scheduleThumbnailGeneration()
        
        logger.info("✅ 后台缩略图生成完成")
    }
    
    private func handleDatabaseMaintenance() async {
        logger.info("🗄️ 开始后台数据库维护")
        
        // 执行数据库维护
        await DatabaseManager.shared.performBackgroundMaintenance()
        
        // 重新调度下次维护
        scheduleDatabaseMaintenance()
        
        logger.info("✅ 后台数据库维护完成")
    }
    
    // MARK: - 应用状态处理
    
    private func handleAppDidEnterBackground() {
        guard isBackgroundOptimizationEnabled else { return }
        
        let taskIdentifier = beginBackgroundTask(name: "BackgroundOptimization")
        
        Task {
            await performBackgroundCleanup()
            endBackgroundTask(taskIdentifier)
            
            await MainActor.run {
                lastBackgroundOptimization = Date()
            }
        }
        
        logger.info("📱 应用进入后台，开始后台优化")
    }
    
    private func handleAppWillEnterForeground() {
        // 应用即将进入前台，取消不必要的后台任务
        logger.info("📱 应用即将进入前台")
    }
    
    // MARK: - 后台任务管理
    
    private func beginBackgroundTask(name: String) -> UIBackgroundTaskIdentifier {
        let taskIdentifier = UIApplication.shared.beginBackgroundTask(withName: name) { [weak self] in
            // 后台任务即将过期，进行清理
            self?.logger.warning("⏰ 后台任务即将过期: \(name)")
        }
        
        backgroundTaskIdentifiers.insert(taskIdentifier)
        backgroundTasksCount = backgroundTaskIdentifiers.count
        
        logger.info("🚀 开始后台任务: \(name)")
        return taskIdentifier
    }
    
    private func endBackgroundTask(_ taskIdentifier: UIBackgroundTaskIdentifier) {
        guard taskIdentifier != .invalid else { return }
        
        backgroundTaskIdentifiers.remove(taskIdentifier)
        backgroundTasksCount = backgroundTaskIdentifiers.count
        
        UIApplication.shared.endBackgroundTask(taskIdentifier)
        logger.info("✅ 结束后台任务")
    }
    
    // MARK: - 后台清理逻辑
    
    private func performBackgroundCleanup() async {
        let startTime = Date()
        
        // 执行轻量级清理操作
        await performLightweightCleanup()
        
        // 检查剩余时间
        let elapsedTime = Date().timeIntervalSince(startTime)
        let remainingTime = TaskConfig.maxBackgroundTime - elapsedTime
        
        if remainingTime > 5.0 {
            // 还有足够时间，执行更深度的清理
            await performDeepCleanup()
        }
        
        logger.info("🌙 后台清理完成，耗时: \(String(format: "%.2f", Date().timeIntervalSince(startTime)))秒")
    }
    
    private func performLightweightCleanup() async {
        // 轻量级清理：内存优化
        await MainActor.run {
            Task {
                await MediaMemoryManager.shared.smartResourceRelease()
            }
        }
        
        // 清理临时文件
        _ = await ResourceOptimizer.shared.cleanupTemporaryFiles()
    }
    
    private func performDeepCleanup() async {
        // 深度清理：全面资源优化
        _ = await ResourceOptimizer.shared.immediateCleanup()
        
        // 数据库优化
        await DatabaseManager.shared.optimizeForBackground()
    }
    
    // MARK: - 工具方法
    
    private func formatBytes(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .decimal
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        // cancelAllBackgroundTasks() // 在deinit中调用main actor方法会有问题
        for taskIdentifier in backgroundTaskIdentifiers {
            if taskIdentifier != .invalid {
                UIApplication.shared.endBackgroundTask(taskIdentifier)
            }
        }
    }
}

// MARK: - OptimizedThumbnailManager 扩展支持
extension OptimizedThumbnailManager {
    func optimizeExistingThumbnails() async {
        // 优化现有缩略图
        print("🖼️ 优化现有缩略图")
    }
}

// MARK: - DatabaseManager 扩展支持
extension DatabaseManager {
    func performBackgroundMaintenance() async {
        // 执行后台数据库维护
        print("🗄️ 执行后台数据库维护")
    }
    
    func optimizeForBackground() async {
        // 为后台操作优化数据库
        print("🗄️ 为后台操作优化数据库")
    }
} 