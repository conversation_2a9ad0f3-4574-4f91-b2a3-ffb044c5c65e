import Foundation
import OSLog
import UIKit
import AVFoundation
import Combine

// MARK: - 优化缩略图管理器（第二阶段性能优化）
@MainActor
class OptimizedThumbnailManager: ObservableObject {
    static let shared = OptimizedThumbnailManager()
    
    // MARK: - 多尺寸支持
    enum ThumbnailSize: String, CaseIterable {
        case small = "small"     // 150x150 - 列表视图
        case medium = "medium"   // 300x300 - 网格视图  
        case large = "large"     // 600x600 - 详情预览
        
        var size: CGSize {
            switch self {
            case .small: return CGSize(width: 150, height: 150)
            case .medium: return CGSize(width: 300, height: 300)
            case .large: return CGSize(width: 600, height: 600)
            }
        }
        
        var directory: String {
            return rawValue
        }
        
        var compressionQuality: CGFloat {
            switch self {
            case .small: return 0.6
            case .medium: return 0.8
            case .large: return 0.9
            }
        }
    }
    
    // MARK: - 缩略图任务优先级
    enum TaskPriority: Int, Comparable {
        case low = 0
        case normal = 1
        case high = 2
        case urgent = 3
        
        static func < (lhs: TaskPriority, rhs: TaskPriority) -> Bool {
            return lhs.rawValue < rhs.rawValue
        }
    }
    
    // MARK: - 缩略图生成任务
    struct ThumbnailTask {
        let id: UUID
        let mediaFileID: UUID
        let mediaType: MediaType
        let sourceURL: URL
        let targetSizes: [ThumbnailSize]
        let priority: TaskPriority
        let completion: (Result<[ThumbnailSize: URL], ThumbnailError>) -> Void
        
        init(mediaFileID: UUID, mediaType: MediaType, sourceURL: URL, targetSizes: [ThumbnailSize], priority: TaskPriority, completion: @escaping (Result<[ThumbnailSize: URL], ThumbnailError>) -> Void) {
            self.id = UUID()
            self.mediaFileID = mediaFileID
            self.mediaType = mediaType
            self.sourceURL = sourceURL
            self.targetSizes = targetSizes
            self.priority = priority
            self.completion = completion
        }
    }
    
    // MARK: - 错误类型
    enum ThumbnailError: Error, LocalizedError {
        case generationFailed(String)
        case fileNotFound
        case unsupportedFormat
        case memoryPressure
        case cancelled
        
        var errorDescription: String? {
            switch self {
            case .generationFailed(let reason):
                return "缩略图生成失败: \(reason)"
            case .fileNotFound:
                return "源文件不存在"
            case .unsupportedFormat:
                return "不支持的文件格式"
            case .memoryPressure:
                return "内存压力过高，生成已暂停"
            case .cancelled:
                return "任务已取消"
            }
        }
    }
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.browser", category: "OptimizedThumbnailManager")
    private let thumbnailCache = ThumbnailMemoryCache()
    private let diskCache: ThumbnailDiskCache
    private let generationQueue = DispatchQueue(label: "thumbnail.generation", qos: .utility)
    private let priorityQueue = DispatchQueue(label: "thumbnail.priority", qos: .userInitiated)
    
    // 任务管理
    private var pendingTasks: [ThumbnailTask] = []
    private var activeTasks: [UUID: Task<Void, Never>] = [:]
    private let maxConcurrentTasks = 3
    private var cancellables = Set<AnyCancellable>()
    
    // 统计信息
    @Published private(set) var statistics = ThumbnailStatistics()
    
    // MARK: - 配置
    private struct Config {
        static let memoryPressureThreshold: Double = 0.8  // 80% 内存使用率暂停生成
        static let cacheCleanupInterval: TimeInterval = 300 // 5分钟清理一次
        static let maxDiskCacheSize: Int64 = 500 * 1024 * 1024 // 500MB
        static let maxMemoryCacheCount = 200 // 最多缓存200张图片
        static let statisticsUpdateInterval: TimeInterval = 10.0
    }
    
    // MARK: - 初始化
    private init() {
        self.diskCache = ThumbnailDiskCache()
        setupCacheCleanupTimer()
        setupMemoryPressureMonitoring()
        setupStatisticsTimer()
        logger.info("🖼️ 优化缩略图管理器已启动")
    }
    
    deinit {
        cancellables.removeAll()
        for task in activeTasks.values {
            task.cancel()
        }
    }
    
    // MARK: - 公共接口
    
    /// 获取缩略图（优先从缓存获取）
    func getThumbnail(for mediaFileID: UUID, size: ThumbnailSize) async -> UIImage? {
        statistics.requestCount += 1
        
        // 1. 检查内存缓存
        if let cachedImage = thumbnailCache.getImage(for: mediaFileID, size: size) {
            statistics.cacheHits += 1
            return cachedImage
        }
        
        // 2. 检查磁盘缓存
        if let diskImage = await diskCache.getImage(for: mediaFileID, size: size) {
            // 添加到内存缓存
            thumbnailCache.setImage(diskImage, for: mediaFileID, size: size)
            statistics.diskHits += 1
            return diskImage
        }
        
        statistics.cacheMisses += 1
        return nil
    }
    
    /// 生成缩略图（支持多尺寸和优先级）
    func generateThumbnails(
        for mediaFileID: UUID,
        mediaType: MediaType,
        sourceURL: URL,
        sizes: [ThumbnailSize] = [.medium],
        priority: TaskPriority = .normal
    ) async throws -> [ThumbnailSize: URL] {
        
        return try await withCheckedThrowingContinuation { continuation in
            let task = ThumbnailTask(
                mediaFileID: mediaFileID,
                mediaType: mediaType,
                sourceURL: sourceURL,
                targetSizes: sizes,
                priority: priority
            ) { result in
                continuation.resume(with: result)
            }
            
            Task { @MainActor in
                await enqueueTask(task)
            }
        }
    }
    
    /// 预生成缩略图（低优先级）
    func preloadThumbnails(
        for mediaFiles: [(UUID, MediaType, URL)],
        sizes: [ThumbnailSize] = [.small, .medium]
    ) async {
        for (mediaFileID, mediaType, sourceURL) in mediaFiles {
            let task = ThumbnailTask(
                mediaFileID: mediaFileID,
                mediaType: mediaType,
                sourceURL: sourceURL,
                targetSizes: sizes,
                priority: .low
            ) { _ in
                // 预加载不需要回调
            }
            
            await enqueueTask(task)
        }
    }
    
    /// 取消指定媒体文件的生成任务
    func cancelGeneration(for mediaFileID: UUID) {
        pendingTasks.removeAll { $0.mediaFileID == mediaFileID }
        
        if let task = activeTasks[mediaFileID] {
            task.cancel()
            activeTasks.removeValue(forKey: mediaFileID)
        }
    }
    
    /// 获取缓存统计信息
    func getCacheStatistics() async -> (memoryUsage: UInt64, diskUsage: UInt64, itemCount: Int) {
        let memoryUsage = await thumbnailCache.getCurrentMemoryUsage()
        let diskUsage = await diskCache.getCurrentCacheSize()
        let itemCount = thumbnailCache.currentSize
        
        return (memoryUsage: memoryUsage, diskUsage: diskUsage, itemCount: itemCount)
    }
    
    /// 清理缓存（指定时间之前的项目）
    func clearCache(olderThan interval: TimeInterval = 0) async {
        await thumbnailCache.clearOldItems(olderThan: interval)
        await diskCache.clearOldItems(olderThan: interval)
        statistics.cacheCleanups += 1
    }
    
    // MARK: - 任务管理
    
    private func enqueueTask(_ task: ThumbnailTask) async {
        // 检查是否已存在相同的任务
        if pendingTasks.contains(where: { $0.mediaFileID == task.mediaFileID }) {
            return
        }
        
        // 按优先级插入任务
        if let index = pendingTasks.firstIndex(where: { $0.priority < task.priority }) {
            pendingTasks.insert(task, at: index)
        } else {
            pendingTasks.append(task)
        }
        
        await processNextTask()
    }
    
    private func processNextTask() async {
        // 检查并发限制
        guard activeTasks.count < maxConcurrentTasks else { return }
        
        // 检查内存压力
        let memoryManager = UnifiedMemoryManager.shared
        if memoryManager.isMemoryUnderPressure() {
            logger.warning("⚠️ 内存压力过高，暂停缩略图生成")
            return
        }
        
        // 获取下一个任务
        guard let task = pendingTasks.first else { return }
        pendingTasks.removeFirst()
        
        // 执行任务
        let asyncTask = Task {
            await executeTask(task)
        }
        
        activeTasks[task.mediaFileID] = asyncTask
    }
    
    private func executeTask(_ task: ThumbnailTask) async {
        defer {
            Task { @MainActor in
                activeTasks.removeValue(forKey: task.mediaFileID)
                await processNextTask()
            }
        }
        
        do {
            statistics.generationStarted += 1
            let startTime = Date()
            
            let results = try await generateThumbnailsForTask(task)
            
            let duration = Date().timeIntervalSince(startTime)
            statistics.averageGenerationTime = (statistics.averageGenerationTime * 0.9) + (duration * 0.1)
            statistics.generationCompleted += 1
            
            task.completion(.success(results))
            
        } catch {
            statistics.generationFailed += 1
            
            if error is CancellationError {
                task.completion(.failure(.cancelled))
            } else {
                task.completion(.failure(.generationFailed(error.localizedDescription)))
            }
        }
    }
    
    private func generateThumbnailsForTask(_ task: ThumbnailTask) async throws -> [ThumbnailSize: URL] {
        var results: [ThumbnailSize: URL] = [:]
        
        for size in task.targetSizes {
            try Task.checkCancellation()
            
            let thumbnailURL = diskCache.getThumbnailURL(for: task.mediaFileID, size: size)
            
            // 如果文件已存在，直接返回
            if FileManager.default.fileExists(atPath: thumbnailURL.path) {
                results[size] = thumbnailURL
                continue
            }
            
            // 生成缩略图
            let image = try await generateSingleThumbnail(
                from: task.sourceURL,
                mediaType: task.mediaType,
                size: size
            )
            
            // 保存到磁盘
            try await saveThumbnailToDisk(image, url: thumbnailURL, size: size)
            
            // 添加到内存缓存
            await MainActor.run {
                thumbnailCache.setImage(image, for: task.mediaFileID, size: size)
            }
            
            results[size] = thumbnailURL
        }
        
        return results
    }
    
    private func generateSingleThumbnail(
        from sourceURL: URL,
        mediaType: MediaType,
        size: ThumbnailSize
    ) async throws -> UIImage {
        switch mediaType {
        case .image:
            return try await generateImageThumbnail(from: sourceURL, targetSize: size.size)
        case .video:
            return try await generateVideoThumbnail(from: sourceURL, targetSize: size.size)
        }
    }
    
    // MARK: - 图片缩略图生成
    
    private func generateImageThumbnail(from sourceURL: URL, targetSize: CGSize) async throws -> UIImage {
        return try await withCheckedThrowingContinuation { continuation in
            generationQueue.async {
                guard let imageSource = CGImageSourceCreateWithURL(sourceURL as CFURL, nil) else {
                    continuation.resume(throwing: ThumbnailError.generationFailed("无法创建图片源"))
                    return
                }
                
                let options: [CFString: Any] = [
                    kCGImageSourceCreateThumbnailFromImageIfAbsent: true,
                    kCGImageSourceCreateThumbnailWithTransform: true,
                    kCGImageSourceThumbnailMaxPixelSize: max(targetSize.width, targetSize.height)
                ]
                
                guard let thumbnail = CGImageSourceCreateThumbnailAtIndex(imageSource, 0, options as CFDictionary) else {
                    continuation.resume(throwing: ThumbnailError.generationFailed("缩略图创建失败"))
                    return
                }
                
                let image = UIImage(cgImage: thumbnail)
                continuation.resume(returning: image)
            }
        }
    }
    
    // MARK: - 视频缩略图生成
    
    private func generateVideoThumbnail(from sourceURL: URL, targetSize: CGSize) async throws -> UIImage {
        let asset = AVURLAsset(url: sourceURL)
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        imageGenerator.maximumSize = targetSize
        
        // 尝试多个时间点
        let timePoints: [CMTime] = [
            CMTime(seconds: 3.0, preferredTimescale: 600),
            CMTime(seconds: 1.0, preferredTimescale: 600),
            CMTime(seconds: 0.5, preferredTimescale: 600)
        ]
        
        for timePoint in timePoints {
            do {
                let cgImage = try await imageGenerator.image(at: timePoint).image
                return UIImage(cgImage: cgImage)
            } catch {
                // 继续尝试下一个时间点
                continue
            }
        }
        
        // 如果所有时间点都失败，生成默认缩略图
        return generateDefaultVideoThumbnail(targetSize: targetSize)
    }
    
    private func generateDefaultVideoThumbnail(targetSize: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: targetSize)
        return renderer.image { context in
            // 设置背景色
            UIColor.systemGray5.setFill()
            context.fill(CGRect(origin: .zero, size: targetSize))
            
            // 绘制视频图标
            let iconSize: CGFloat = min(targetSize.width, targetSize.height) * 0.4
            let iconRect = CGRect(
                x: (targetSize.width - iconSize) / 2,
                y: (targetSize.height - iconSize) / 2,
                width: iconSize,
                height: iconSize
            )
            
            UIColor.systemGray.setFill()
            let path = UIBezierPath(roundedRect: iconRect, cornerRadius: iconSize * 0.1)
            path.fill()
            
            // 绘制播放按钮
            let playSize = iconSize * 0.3
            let playRect = CGRect(
                x: iconRect.midX - playSize / 2 + playSize * 0.1,
                y: iconRect.midY - playSize / 2,
                width: playSize,
                height: playSize
            )
            
            UIColor.white.setFill()
            let playPath = UIBezierPath()
            playPath.move(to: CGPoint(x: playRect.minX, y: playRect.minY))
            playPath.addLine(to: CGPoint(x: playRect.maxX, y: playRect.midY))
            playPath.addLine(to: CGPoint(x: playRect.minX, y: playRect.maxY))
            playPath.close()
            playPath.fill()
        }
    }
    
    // MARK: - 磁盘操作
    
    private func saveThumbnailToDisk(_ image: UIImage, url: URL, size: ThumbnailSize) async throws {
        guard let imageData = image.jpegData(compressionQuality: size.compressionQuality) else {
            throw ThumbnailError.generationFailed("图片数据转换失败")
        }
        
        try imageData.write(to: url)
    }
    
    // MARK: - 定时器设置
    
    private func setupCacheCleanupTimer() {
        Timer.scheduledTimer(withTimeInterval: Config.cacheCleanupInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performPeriodicCleanup()
            }
        }
    }
    
    private func setupMemoryPressureMonitoring() {
        UnifiedMemoryManager.shared.$currentMemoryState
            .sink { [weak self] memoryState in
                Task { @MainActor in
                    await self?.handleMemoryPressureChange(memoryState.pressureLevel)
                }
            }
            .store(in: &cancellables)
    }
    
    private func setupStatisticsTimer() {
        Timer.scheduledTimer(withTimeInterval: Config.statisticsUpdateInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateStatistics()
            }
        }
    }
    
    // MARK: - 清理和优化
    
    private func performPeriodicCleanup() async {
        // 清理5分钟前的缓存
        await clearCache(olderThan: 300)
        
        // 检查磁盘缓存大小
        let cacheSize = await diskCache.calculateCacheSize()
        if cacheSize > Config.maxDiskCacheSize {
            await diskCache.cleanupToSize(Config.maxDiskCacheSize * 8 / 10) // 清理到80%
        }
    }
    
    private func handleMemoryPressureChange(_ level: SimplifiedMemoryPressureLevel) async {
        switch level {
        case .normal:
            // 正常状态，可以继续处理任务
            await processNextTask()
            
        case .warning:
            // 警告状态，清理部分缓存
            await thumbnailCache.clearOldItems(olderThan: 60) // 清理1分钟前的缓存
            
        case .critical:
            // 紧急状态，取消所有待处理任务并清理缓存
            pendingTasks.removeAll()
            for task in activeTasks.values {
                task.cancel()
            }
            activeTasks.removeAll()
            await clearCache()
        }
    }
    
    private func updateStatistics() {
        statistics.pendingTaskCount = pendingTasks.count
        statistics.activeTaskCount = activeTasks.count
        statistics.memoryCacheSize = thumbnailCache.currentSize
    }
}

// MARK: - 统计信息
struct ThumbnailStatistics {
    var requestCount: Int = 0
    var cacheHits: Int = 0
    var diskHits: Int = 0
    var cacheMisses: Int = 0
    var generationStarted: Int = 0
    var generationCompleted: Int = 0
    var generationFailed: Int = 0
    var cacheCleanups: Int = 0
    var averageGenerationTime: TimeInterval = 0.0
    var pendingTaskCount: Int = 0
    var activeTaskCount: Int = 0
    var memoryCacheSize: Int = 0
    
    var cacheHitRate: Double {
        let totalRequests = cacheHits + diskHits + cacheMisses
        return totalRequests > 0 ? Double(cacheHits + diskHits) / Double(totalRequests) : 0.0
    }
    
    var successRate: Double {
        let totalGeneration = generationCompleted + generationFailed
        return totalGeneration > 0 ? Double(generationCompleted) / Double(totalGeneration) : 0.0
    }
}