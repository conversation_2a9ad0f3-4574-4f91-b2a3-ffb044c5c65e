import Foundation
import UIKit
import OSLog
import Combine

// MARK: - 性能分析器（第二阶段性能优化）
@MainActor
final class PerformanceAnalyzer: ObservableObject {
    
    // MARK: - 单例
    static let shared = PerformanceAnalyzer()
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.browser", category: "PerformanceAnalyzer")
    private let analysisQueue = DispatchQueue(label: "performance.analysis", qos: .utility)
    private var analysisTimer: Timer?
    private var startTime: Date?
    
    // MARK: - 发布属性
    @Published var currentMetrics: PerformanceMetrics = PerformanceMetrics()
    @Published var isAnalyzing: Bool = false
    @Published var analysisHistory: [PerformanceSnapshot] = []
    @Published var performanceGrade: PerformanceGrade = .fair
    
    // MARK: - 分析配置
    private struct AnalysisConfig {
        static let analysisInterval: TimeInterval = 10.0     // 10秒分析一次
        static let historyLimit: Int = 100                   // 保留100个历史记录
        static let memoryWarningThreshold: UInt64 = 300 * 1024 * 1024  // 300MB
        static let cpuUsageWarningThreshold: Double = 80.0   // 80% CPU使用率
        static let frameRateWarningThreshold: Double = 45.0  // 45 FPS
    }
    
    // MARK: - 性能基准
    private struct PerformanceBenchmarks {
        static let excellentMemory: UInt64 = 150 * 1024 * 1024  // 150MB
        static let goodMemory: UInt64 = 250 * 1024 * 1024       // 250MB
        static let excellentCPU: Double = 30.0                  // 30%
        static let goodCPU: Double = 60.0                       // 60%
        static let excellentFPS: Double = 58.0                  // 58 FPS
        static let goodFPS: Double = 50.0                       // 50 FPS
    }
    
    // MARK: - 初始化
    private init() {
        setupPerformanceAnalysis()
        registerForAppStateChanges()
        logger.info("📊 性能分析器已启动")
    }
    
    // MARK: - 公共接口
    
    /// 开始性能分析
    func startAnalysis() {
        guard !isAnalyzing else { return }
        
        isAnalyzing = true
        startTime = Date()
        
        analysisTimer = Timer.scheduledTimer(withTimeInterval: AnalysisConfig.analysisInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performAnalysis()
            }
        }
        
        logger.info("🚀 性能分析已开始")
    }
    
    /// 停止性能分析
    func stopAnalysis() {
        isAnalyzing = false
        analysisTimer?.invalidate()
        analysisTimer = nil
        
        logger.info("⏹️ 性能分析已停止")
    }
    
    /// 立即执行性能分析
    func analyzeNow() async -> PerformanceSnapshot {
        let snapshot = await createPerformanceSnapshot()
        await updateAnalysisHistory(snapshot)
        return snapshot
    }
    
    /// 获取性能报告
    func generatePerformanceReport() async -> PerformanceReport {
        let snapshot = await createPerformanceSnapshot()
        let report = await createPerformanceReport(from: snapshot)
        return report
    }
    
    /// 检测性能问题
    func detectPerformanceIssues() -> [PerformanceIssue] {
        var issues: [PerformanceIssue] = []
        
        // 检查内存使用
        if currentMetrics.memoryUsage > AnalysisConfig.memoryWarningThreshold {
            issues.append(PerformanceIssue(
                type: .highMemoryUsage,
                severity: .high,
                description: "内存使用过高: \(formatBytes(currentMetrics.memoryUsage))",
                suggestion: "建议清理缓存或释放不必要的资源"
            ))
        }
        
        // 检查CPU使用率
        if currentMetrics.cpuUsage > AnalysisConfig.cpuUsageWarningThreshold {
            issues.append(PerformanceIssue(
                type: .highCpuUsage,
                severity: .medium,
                description: "CPU使用率过高: \(String(format: "%.1f", currentMetrics.cpuUsage))%",
                suggestion: "建议优化计算密集型操作或减少后台任务"
            ))
        }
        
        // 检查帧率
        if currentMetrics.frameRate < AnalysisConfig.frameRateWarningThreshold {
            issues.append(PerformanceIssue(
                type: .lowFrameRate,
                severity: .medium,
                description: "帧率过低: \(String(format: "%.1f", currentMetrics.frameRate)) FPS",
                suggestion: "建议减少UI复杂度或优化渲染逻辑"
            ))
        }
        
        return issues
    }
    
    /// 获取性能优化建议
    func getOptimizationSuggestions() -> [OptimizationSuggestion] {
        var suggestions: [OptimizationSuggestion] = []
        
        // 基于当前指标提供建议
        if currentMetrics.memoryUsage > PerformanceBenchmarks.goodMemory {
            suggestions.append(OptimizationSuggestion(
                category: .memory,
                priority: .high,
                title: "内存优化",
                description: "当前内存使用较高，建议启用智能内存管理",
                action: "启用后台内存清理"
            ))
        }
        
        if currentMetrics.cacheSize > 200 * 1024 * 1024 {
            suggestions.append(OptimizationSuggestion(
                category: .storage,
                priority: .medium,
                title: "缓存清理",
                description: "缓存占用空间较大，建议定期清理",
                action: "执行缓存清理"
            ))
        }
        
        if currentMetrics.frameRate < PerformanceBenchmarks.goodFPS {
            suggestions.append(OptimizationSuggestion(
                category: .performance,
                priority: .medium,
                title: "UI性能优化",
                description: "界面渲染性能有待改善",
                action: "启用高性能模式"
            ))
        }
        
        return suggestions
    }
    
    // MARK: - 私有方法
    
    private func setupPerformanceAnalysis() {
        // 初始化性能指标
        Task {
            await updateCurrentMetrics()
        }
    }
    
    private func registerForAppStateChanges() {
        NotificationCenter.default.addObserver(
            forName: UIApplication.didBecomeActiveNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.startAnalysis()
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: UIApplication.didEnterBackgroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.stopAnalysis()
            }
        }
    }
    
    private func performAnalysis() async {
        await updateCurrentMetrics()
        
        let snapshot = await createPerformanceSnapshot()
        await updateAnalysisHistory(snapshot)
        
        // 评估性能等级
        performanceGrade = calculatePerformanceGrade(from: currentMetrics)
        
        // 检测并记录性能问题
        let issues = detectPerformanceIssues()
        if !issues.isEmpty {
            logger.warning("检测到性能问题: \(issues.count)个")
        }
    }
    
    private func updateCurrentMetrics() async {
        currentMetrics = PerformanceMetrics(
            memoryUsage: getCurrentMemoryUsage(),
            cpuUsage: await getCurrentCPUUsage(),
            frameRate: getCurrentFrameRate(),
            cacheSize: await getCurrentCacheSize(),
            networkLatency: await measureNetworkLatency(),
            diskIORate: await getCurrentDiskIORate(),
            batteryLevel: getCurrentBatteryLevel(),
            thermalState: getCurrentThermalState()
        )
    }
    
    private func createPerformanceSnapshot() async -> PerformanceSnapshot {
        let uptime = startTime?.timeIntervalSinceNow ?? 0
        
        return PerformanceSnapshot(
            timestamp: Date(),
            metrics: currentMetrics,
            uptime: abs(uptime),
            performanceGrade: calculatePerformanceGrade(from: currentMetrics),
            issues: detectPerformanceIssues(),
            suggestions: getOptimizationSuggestions()
        )
    }
    
    private func updateAnalysisHistory(_ snapshot: PerformanceSnapshot) async {
        analysisHistory.append(snapshot)
        
        // 限制历史记录数量
        if analysisHistory.count > AnalysisConfig.historyLimit {
            analysisHistory.removeFirst(analysisHistory.count - AnalysisConfig.historyLimit)
        }
    }
    
    private func createPerformanceReport(from snapshot: PerformanceSnapshot) async -> PerformanceReport {
        let averageMetrics = calculateAverageMetrics()
        let trendAnalysis = analyzeTrends()
        
        return PerformanceReport(
            generatedAt: Date(),
            currentSnapshot: snapshot,
            averageMetrics: averageMetrics,
            trendAnalysis: trendAnalysis,
            recommendations: generateRecommendations()
        )
    }
    
    // MARK: - 性能指标计算
    
    private func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        return result == KERN_SUCCESS ? info.resident_size : 0
    }
    
    private func getCurrentCPUUsage() async -> Double {
        return await withCheckedContinuation { continuation in
            analysisQueue.async {
                var info = task_basic_info()
                var count = mach_msg_type_number_t(MemoryLayout<task_basic_info>.size / MemoryLayout<natural_t>.size)
                
                let result = withUnsafeMutablePointer(to: &info) {
                    $0.withMemoryRebound(to: integer_t.self, capacity: Int(count)) {
                        task_info(mach_task_self_, task_flavor_t(TASK_BASIC_INFO), $0, &count)
                    }
                }
                
                if result == KERN_SUCCESS {
                    // 计算 CPU 使用率
                    let totalTime = info.user_time.seconds + info.user_time.microseconds / 1000000 +
                                   info.system_time.seconds + info.system_time.microseconds / 1000000
                    
                    // 简化的 CPU 使用率计算
                    let usage = min(Double(totalTime) / 10.0 * 100.0, 100.0)
                    continuation.resume(returning: usage)
                } else {
                    continuation.resume(returning: 0.0)
                }
            }
        }
    }
    
    private func getCurrentFrameRate() -> Double {
        return frameRateMonitor.currentFrameRate
    }
    
    // MARK: - 帧率监控器
    private let frameRateMonitor = FrameRateMonitor()
    
    private class FrameRateMonitor {
        private var displayLink: CADisplayLink?
        private var frameCount = 0
        private var lastTimestamp: CFTimeInterval = 0
        private var _currentFrameRate: Double = 60.0
        
        var currentFrameRate: Double {
            return _currentFrameRate
        }
        
        init() {
            startMonitoring()
        }
        
        deinit {
            stopMonitoring()
        }
        
        private func startMonitoring() {
            displayLink = CADisplayLink(target: self, selector: #selector(displayLinkCallback))
            displayLink?.add(to: .main, forMode: .common)
        }
        
        private func stopMonitoring() {
            displayLink?.invalidate()
            displayLink = nil
        }
        
        @objc private func displayLinkCallback(displayLink: CADisplayLink) {
            if lastTimestamp == 0 {
                lastTimestamp = displayLink.timestamp
                return
            }
            
            frameCount += 1
            let elapsed = displayLink.timestamp - lastTimestamp
            
            // 每秒更新一次帧率计算
            if elapsed >= 1.0 {
                _currentFrameRate = Double(frameCount) / elapsed
                frameCount = 0
                lastTimestamp = displayLink.timestamp
            }
        }
    }
    
    private func getCurrentCacheSize() async -> UInt64 {
        var totalSize: UInt64 = 0
        
        // URL缓存大小
        totalSize += UInt64(URLCache.shared.currentDiskUsage + URLCache.shared.currentMemoryUsage)
        
        // 缩略图缓存大小
        if let thumbnailCache = try? await OptimizedThumbnailManager.shared.getCacheStatistics() {
            totalSize += thumbnailCache.diskUsage + thumbnailCache.memoryUsage
        }
        
        // 数据库使用统计（暂时注释掉，因为方法不存在）
        /*
        if let metadataStats = await DatabaseManager.shared.getDatabaseStatistics() {
            totalSize += UInt64(metadataStats.diskUsage) + UInt64(metadataStats.memoryUsage)
        }
        */
        
        // 应用缓存目录大小
        if let cacheDirectory = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first {
            totalSize += getDirectorySize(cacheDirectory)
        }
        
        return totalSize
    }
    
    private func measureNetworkLatency() async -> TimeInterval {
        // 实际的网络延迟测量
        return await withCheckedContinuation { continuation in
            let startTime = CFAbsoluteTimeGetCurrent()
            
            // 使用系统DNS查询来测量网络延迟
            let url = URL(string: "https://www.apple.com")!
            
            let task = URLSession.shared.dataTask(with: url) { _, _, _ in
                let endTime = CFAbsoluteTimeGetCurrent()
                let latency = endTime - startTime
                continuation.resume(returning: latency)
            }
            
            task.resume()
            
            // 设置超时
            DispatchQueue.global().asyncAfter(deadline: .now() + 5.0) {
                task.cancel()
                continuation.resume(returning: 5.0) // 超时返回5秒
            }
        }
    }
    
    private func getCurrentDiskIORate() async -> Double {
        return await withCheckedContinuation { continuation in
            analysisQueue.async {
                // 使用系统调用获取磁盘I/O统计
                var statfs_buf = statfs()
                let result = statfs("/", &statfs_buf)
                
                guard result == 0 else {
                    continuation.resume(returning: 0.0)
                    return
                }
                
                // 计算磁盘I/O速率（简化实现）
                let freeSpace = UInt64(statfs_buf.f_bavail) * UInt64(statfs_buf.f_bsize)
                let totalSpace = UInt64(statfs_buf.f_blocks) * UInt64(statfs_buf.f_bsize)
                let usedSpace = totalSpace - freeSpace
                
                // 估算I/O速率（基于使用率）
                let usageRatio = Double(usedSpace) / Double(totalSpace)
                let estimatedIORate = usageRatio * 10.0 // 简化的估算
                
                continuation.resume(returning: estimatedIORate)
            }
        }
    }
    
    private func getCurrentBatteryLevel() -> Float {
        UIDevice.current.isBatteryMonitoringEnabled = true
        return UIDevice.current.batteryLevel
    }
    
    private func getCurrentThermalState() -> ProcessInfo.ThermalState {
        return ProcessInfo.processInfo.thermalState
    }
    
    // MARK: - 分析方法
    
    private func calculatePerformanceGrade(from metrics: PerformanceMetrics) -> PerformanceGrade {
        var score = 0
        
        // 内存使用评分
        if metrics.memoryUsage <= PerformanceBenchmarks.excellentMemory {
            score += 25
        } else if metrics.memoryUsage <= PerformanceBenchmarks.goodMemory {
            score += 15
        } else {
            score += 5
        }
        
        // CPU使用评分
        if metrics.cpuUsage <= PerformanceBenchmarks.excellentCPU {
            score += 25
        } else if metrics.cpuUsage <= PerformanceBenchmarks.goodCPU {
            score += 15
        } else {
            score += 5
        }
        
        // 帧率评分
        if metrics.frameRate >= PerformanceBenchmarks.excellentFPS {
            score += 25
        } else if metrics.frameRate >= PerformanceBenchmarks.goodFPS {
            score += 15
        } else {
            score += 5
        }
        
        // 热状态评分
        switch metrics.thermalState {
        case .nominal:
            score += 25
        case .fair:
            score += 15
        case .serious:
            score += 10
        case .critical:
            score += 0
        @unknown default:
            score += 10
        }
        
        // 根据总分确定等级
        switch score {
        case 80...100:
            return .excellent
        case 60...79:
            return .good
        case 40...59:
            return .fair
        case 20...39:
            return .poor
        default:
            return .poor
        }
    }
    
    private func calculateAverageMetrics() -> PerformanceMetrics {
        guard !analysisHistory.isEmpty else { return PerformanceMetrics() }
        
        let totalMetrics = analysisHistory.reduce(PerformanceMetrics()) { result, snapshot in
            var metrics = result
            metrics.memoryUsage += snapshot.metrics.memoryUsage
            metrics.cpuUsage += snapshot.metrics.cpuUsage
            metrics.frameRate += snapshot.metrics.frameRate
            metrics.cacheSize += snapshot.metrics.cacheSize
            metrics.networkLatency += snapshot.metrics.networkLatency
            metrics.diskIORate += snapshot.metrics.diskIORate
            return metrics
        }
        
        let count = UInt64(analysisHistory.count)
        
        return PerformanceMetrics(
            memoryUsage: totalMetrics.memoryUsage / count,
            cpuUsage: totalMetrics.cpuUsage / Double(count),
            frameRate: totalMetrics.frameRate / Double(count),
            cacheSize: totalMetrics.cacheSize / count,
            networkLatency: totalMetrics.networkLatency / Double(count),
            diskIORate: totalMetrics.diskIORate / Double(count),
            batteryLevel: totalMetrics.batteryLevel / Float(count),
            thermalState: .nominal
        )
    }
    
    private func analyzeTrends() -> TrendAnalysis {
        guard analysisHistory.count >= 2 else {
            return TrendAnalysis(memoryTrend: .stable, cpuTrend: .stable, frameRateTrend: .stable)
        }
        
        let recent = analysisHistory.suffix(10)
        let older = analysisHistory.prefix(max(1, analysisHistory.count - 10))
        
        let recentAvgMemory = recent.map { $0.metrics.memoryUsage }.reduce(0, +) / UInt64(recent.count)
        let olderAvgMemory = older.map { $0.metrics.memoryUsage }.reduce(0, +) / UInt64(max(1, older.count))
        
        let recentAvgCPU = recent.map { $0.metrics.cpuUsage }.reduce(0, +) / Double(recent.count)
        let olderAvgCPU = older.map { $0.metrics.cpuUsage }.reduce(0, +) / Double(max(1, older.count))
        
        let recentAvgFPS = recent.map { $0.metrics.frameRate }.reduce(0, +) / Double(recent.count)
        let olderAvgFPS = older.map { $0.metrics.frameRate }.reduce(0, +) / Double(max(1, older.count))
        
        return TrendAnalysis(
            memoryTrend: determineTrend(current: recentAvgMemory, previous: olderAvgMemory),
            cpuTrend: determineTrend(current: recentAvgCPU, previous: olderAvgCPU),
            frameRateTrend: determineTrend(current: recentAvgFPS, previous: olderAvgFPS, inverse: true)
        )
    }
    
    private func determineTrend<T: Comparable>(current: T, previous: T, inverse: Bool = false) -> Trend {
        if current == previous {
            return .stable
        } else if (!inverse && current > previous) || (inverse && current < previous) {
            return .improving
        } else {
            return .degrading
        }
    }
    
    private func generateRecommendations() -> [String] {
        var recommendations: [String] = []
        
        if currentMetrics.memoryUsage > PerformanceBenchmarks.goodMemory {
            recommendations.append("建议启用自动内存清理功能")
        }
        
        if currentMetrics.cacheSize > 300 * 1024 * 1024 {
            recommendations.append("建议清理应用缓存以释放存储空间")
        }
        
        if currentMetrics.frameRate < PerformanceBenchmarks.goodFPS {
            recommendations.append("建议减少界面复杂度以提高渲染性能")
        }
        
        if currentMetrics.thermalState == .serious || currentMetrics.thermalState == .critical {
            recommendations.append("设备温度过高，建议暂停高强度操作")
        }
        
        return recommendations
    }
    
    // MARK: - 工具方法
    
    private func getDirectorySize(_ directory: URL) -> UInt64 {
        var totalSize: UInt64 = 0
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(
                at: directory,
                includingPropertiesForKeys: [.fileSizeKey],
                options: .skipsHiddenFiles
            )
            
            for fileURL in fileURLs {
                let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                totalSize += UInt64(resourceValues.fileSize ?? 0)
            }
        } catch {
            // 忽略错误
        }
        
        return totalSize
    }
    
    private func formatBytes(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .decimal
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    deinit {
        analysisTimer?.invalidate()
        analysisTimer = nil
        // isAnalyzing = false // 在deinit中不能修改@Published属性
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - 性能指标
struct PerformanceMetrics {
    var memoryUsage: UInt64 = 0
    var cpuUsage: Double = 0.0
    var frameRate: Double = 60.0
    var cacheSize: UInt64 = 0
    var networkLatency: TimeInterval = 0.0
    var diskIORate: Double = 0.0
    var batteryLevel: Float = 1.0
    var thermalState: ProcessInfo.ThermalState = .nominal
    
    var formattedMemoryUsage: String {
        ByteCountFormatter().string(fromByteCount: Int64(memoryUsage))
    }
    
    var formattedCacheSize: String {
        ByteCountFormatter().string(fromByteCount: Int64(cacheSize))
    }
}

// MARK: - 性能快照
struct PerformanceSnapshot {
    let timestamp: Date
    let metrics: PerformanceMetrics
    let uptime: TimeInterval
    let performanceGrade: PerformanceGrade
    let issues: [PerformanceIssue]
    let suggestions: [OptimizationSuggestion]
}

// MARK: - 性能问题
struct PerformanceIssue {
    let type: IssueType
    let severity: Severity
    let description: String
    let suggestion: String
    
    enum IssueType {
        case highMemoryUsage
        case highCpuUsage
        case lowFrameRate
        case highThermalState
        case networkLatency
        case diskIOBottleneck
    }
    
    enum Severity {
        case low
        case medium
        case high
        case critical
    }
}

// MARK: - 优化建议
struct OptimizationSuggestion {
    let category: Category
    let priority: Priority
    let title: String
    let description: String
    let action: String
    
    enum Category {
        case memory
        case performance
        case storage
        case network
        case battery
    }
    
    enum Priority {
        case low
        case medium
        case high
    }
}

// MARK: - 性能报告
struct PerformanceReport {
    let generatedAt: Date
    let currentSnapshot: PerformanceSnapshot
    let averageMetrics: PerformanceMetrics
    let trendAnalysis: TrendAnalysis
    let recommendations: [String]
}

// MARK: - 趋势分析
struct TrendAnalysis {
    let memoryTrend: Trend
    let cpuTrend: Trend
    let frameRateTrend: Trend
}

enum Trend {
    case improving
    case stable
    case degrading
} 