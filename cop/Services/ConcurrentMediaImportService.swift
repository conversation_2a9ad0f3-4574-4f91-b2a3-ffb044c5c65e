//
//  ConcurrentMediaImportService.swift
//  cop
//
//  Created by 阿亮 on 2025/6/19.
//  第三阶段性能优化 - 并发文件导入服务
//

import Foundation
import SwiftUI
import UIKit
import AVFoundation
import UniformTypeIdentifiers
import os.log

// MARK: - 并发导入进度追踪
struct ConcurrentImportProgress {
    var currentBatch: Int = 0
    var totalBatches: Int = 0
    var batchProgress: Double = 0.0
    var overallProgress: Double = 0.0
    var processedFiles: Int = 0
    var totalFiles: Int = 0
    var successfulFiles: Int = 0
    var failedFiles: Int = 0
    var currentFile: String = ""
    var startTime: Date? = nil
    var estimatedTimeRemaining: TimeInterval? = nil
    var avgProcessingSpeed: Double = 0.0 // 文件/秒
    var isPaused: Bool = false
    var errors: [ImportError] = []
    
    mutating func reset() {
        currentBatch = 0
        totalBatches = 0
        batchProgress = 0.0
        overallProgress = 0.0
        processedFiles = 0
        totalFiles = 0
        successfulFiles = 0
        failedFiles = 0
        currentFile = ""
        startTime = nil
        estimatedTimeRemaining = nil
        avgProcessingSpeed = 0.0
        isPaused = false
        errors.removeAll()
    }
    
    mutating func updateTimeEstimate() {
        guard let startTime = startTime, processedFiles > 0 else { return }
        
        let elapsed = Date().timeIntervalSince(startTime)
        avgProcessingSpeed = Double(processedFiles) / elapsed
        
        let remainingFiles = totalFiles - processedFiles
        if avgProcessingSpeed > 0 {
            estimatedTimeRemaining = Double(remainingFiles) / avgProcessingSpeed
        }
    }
}

// MARK: - 导入错误记录
struct ImportError: Identifiable {
    let id = UUID()
    let fileName: String
    let folderPath: String
    let error: Error
    let timestamp: Date
    
    var description: String {
        "\(fileName): \(error.localizedDescription)"
    }
}

// MARK: - 文件批次
struct FileBatch {
    let id = UUID()
    let files: [FileToImport]
    let batchIndex: Int
    var status: BatchStatus = .pending
    var processedCount: Int = 0
    var failedCount: Int = 0
    var startTime: Date?
    var endTime: Date?
    
    var progress: Double {
        guard !files.isEmpty else { return 0.0 }
        return Double(processedCount + failedCount) / Double(files.count)
    }
    
    var isCompleted: Bool {
        return processedCount + failedCount == files.count
    }
}

enum BatchStatus {
    case pending
    case processing
    case completed
    case failed
    case paused
}

// MARK: - 待导入文件信息
struct FileToImport {
    let sourceURL: URL
    let folderPath: String
    let fileSize: Int64
    let isLargeFile: Bool // >100MB
    let priority: ImportPriority
    
    init(sourceURL: URL, folderPath: String) throws {
        self.sourceURL = sourceURL
        self.folderPath = folderPath
        
        let resourceValues = try sourceURL.resourceValues(forKeys: [.fileSizeKey])
        self.fileSize = Int64(resourceValues.fileSize ?? 0)
        self.isLargeFile = fileSize > 100 * 1024 * 1024 // 100MB
        self.priority = isLargeFile ? .low : .normal
    }
}

enum ImportPriority {
    case high
    case normal  
    case low
}

// MARK: - 并发媒体导入服务
@MainActor
class ConcurrentMediaImportService: ObservableObject {
    static let shared = ConcurrentMediaImportService()
    
    // MARK: - Published Properties
    @Published var isImporting = false
    @Published var progress = ConcurrentImportProgress()
    @Published var currentBatches: [FileBatch] = []
    @Published var canPause = false
    @Published var canResume = false
    
    // MARK: - Configuration
    private let maxConcurrentOperations = 4
    private let batchSize = 50
    private let largeFileBatchSize = 10
    private let maxRetries = 3
    private let retryDelay: TimeInterval = 2.0
    
    // MARK: - Private Properties
    private let fileManager = FileManager.default
    private let logger = Logger(subsystem: "com.cop.app", category: "ConcurrentImport")
    private let importQueue = DispatchQueue(label: "concurrent.import", qos: .utility)
    private let backgroundThumbnailService = BackgroundThumbnailService.shared
    
    // 并发控制
    private var importOperations: [UUID: Task<MediaFileInfo?, Never>] = [:]
    private var pauseState = false
    private var resumeContinuation: CheckedContinuation<Void, Never>?
    
    // 目录配置
    private var mediaDirectory: URL {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        return documentsPath.appendingPathComponent("MediaFiles")
    }
    
    private var thumbnailDirectory: URL {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        return documentsPath.appendingPathComponent("Thumbnails")
    }
    
    private init() {
        createDirectoriesIfNeeded()
    }
    
    // MARK: - Directory Setup
    private func createDirectoriesIfNeeded() {
        try? fileManager.createDirectory(at: mediaDirectory, withIntermediateDirectories: true)
        try? fileManager.createDirectory(at: thumbnailDirectory, withIntermediateDirectories: true)
    }
    
    // MARK: - 主要导入方法
    func importMediaFolder(from sourceURL: URL) async throws -> [MediaFileInfo] {
        logger.info("开始并发导入: \(sourceURL.path)")
        
        // 重置状态
        await MainActor.run {
            isImporting = true
            progress.reset()
            progress.startTime = Date()
            currentBatches.removeAll()
            canPause = true
            canResume = false
        }
        
        defer {
            Task { @MainActor in
                isImporting = false
                canPause = false
                canResume = false
                importOperations.removeAll()
            }
        }
        
        // 获取安全访问权限
        guard sourceURL.startAccessingSecurityScopedResource() else {
            throw MediaImportError.accessDenied
        }
        defer { sourceURL.stopAccessingSecurityScopedResource() }
        
        // 第一步：扫描和分类文件
        let filesToImport = try await scanAndCategorizeFiles(in: sourceURL)
        
        await MainActor.run {
            progress.totalFiles = filesToImport.count
        }
        
        // 第二步：创建批次
        let batches = createBatches(from: filesToImport)
        
        await MainActor.run {
            progress.totalBatches = batches.count
            currentBatches = batches
        }
        
        // 第三步：并发处理批次
        let importedFiles = try await processBatchesConcurrently(batches)
        
        // 第四步：启动后台缩略图生成
        await startBackgroundThumbnailGeneration(for: importedFiles)
        
        // 第五步：保存导入历史
        await saveImportHistory(
            folderName: sourceURL.lastPathComponent,
            fileCount: importedFiles.count,
            totalSize: calculateTotalSize(importedFiles)
        )
        
        // 发送完成通知
        NotificationCenter.default.post(name: .concurrentMediaImportCompleted, object: nil)
        
        logger.info("并发导入完成: 成功 \(importedFiles.count) 个文件")
        return importedFiles
    }
    
    // MARK: - 扫描媒体文件并按叶子文件夹分组
    private func scanMediaFilesByLeafFolders(in directory: URL) async throws -> [String: [URL]] {
        var mediaFilesByFolder: [String: [URL]] = [:]
        
        let resourceKeys: [URLResourceKey] = [.isRegularFileKey, .isDirectoryKey, .typeIdentifierKey]
        
        // 递归扫描所有子目录
        var allMediaFiles: [URL] = []
        try await scanDirectory(directory, into: &allMediaFiles, resourceKeys: resourceKeys)
        
        // 按照每个媒体文件所在的直接父文件夹分组
        for mediaFile in allMediaFiles {
            let parentFolder = mediaFile.deletingLastPathComponent()
            let folderName = parentFolder.lastPathComponent
            
            if mediaFilesByFolder[folderName] == nil {
                mediaFilesByFolder[folderName] = []
            }
            mediaFilesByFolder[folderName]?.append(mediaFile)
        }
        
        return mediaFilesByFolder
    }
    
    // MARK: - 递归扫描目录
    private func scanDirectory(_ directory: URL, into results: inout [URL], resourceKeys: [URLResourceKey]) async throws {
        let fileURLs = try fileManager.contentsOfDirectory(
            at: directory,
            includingPropertiesForKeys: resourceKeys,
            options: [.skipsHiddenFiles, .skipsPackageDescendants]
        )
        
        for fileURL in fileURLs {
            let resourceValues = try fileURL.resourceValues(forKeys: Set(resourceKeys))
            
            if resourceValues.isDirectory == true {
                // 递归扫描子目录
                try await scanDirectory(fileURL, into: &results, resourceKeys: resourceKeys)
            } else if resourceValues.isRegularFile == true,
                      isSupportedMediaFile(fileURL) != nil {
                results.append(fileURL)
            }
        }
    }
    
    // MARK: - 检查支持的媒体类型
    private func isSupportedMediaFile(_ url: URL) -> MediaType? {
        return MediaFormatDetector.detectMediaType(from: url)
    }
    
    // MARK: - 文件扫描和分类
    private func scanAndCategorizeFiles(in directory: URL) async throws -> [FileToImport] {
        logger.info("开始扫描文件...")
        
        var filesToImport: [FileToImport] = []
        let mediaFilesByFolder = try await scanMediaFilesByLeafFolders(in: directory)
        
        for (folderName, fileURLs) in mediaFilesByFolder {
            for fileURL in fileURLs {
                do {
                    let fileToImport = try FileToImport(sourceURL: fileURL, folderPath: folderName)
                    filesToImport.append(fileToImport)
                } catch {
                    logger.warning("跳过文件 \(fileURL.lastPathComponent): \(error.localizedDescription)")
                }
            }
        }
        
        // 按优先级和大小排序
        filesToImport.sort { lhs, rhs in
            if lhs.priority != rhs.priority {
                return lhs.priority == .high
            }
            return lhs.fileSize < rhs.fileSize // 小文件优先
        }
        
        logger.info("扫描完成: 找到 \(filesToImport.count) 个文件")
        return filesToImport
    }
    
    // MARK: - 创建批次
    private func createBatches(from files: [FileToImport]) -> [FileBatch] {
        var batches: [FileBatch] = []
        var currentBatch: [FileToImport] = []
        var batchIndex = 0
        
        for file in files {
            let effectiveBatchSize = file.isLargeFile ? largeFileBatchSize : batchSize
            
            currentBatch.append(file)
            
            if currentBatch.count >= effectiveBatchSize {
                batches.append(FileBatch(files: currentBatch, batchIndex: batchIndex))
                currentBatch.removeAll()
                batchIndex += 1
            }
        }
        
        // 处理剩余文件
        if !currentBatch.isEmpty {
            batches.append(FileBatch(files: currentBatch, batchIndex: batchIndex))
        }
        
        logger.info("创建了 \(batches.count) 个批次")
        return batches
    }
    
    // MARK: - 并发处理批次
    private func processBatchesConcurrently(_ batches: [FileBatch]) async throws -> [MediaFileInfo] {
        var allImportedFiles: [MediaFileInfo] = []
        
        // 使用 TaskGroup 限制并发数
        await withTaskGroup(of: [MediaFileInfo].self) { group in
            var activeTasks = 0
            var batchIndex = 0
            
            while batchIndex < batches.count {
                // 控制并发数
                while activeTasks < maxConcurrentOperations && batchIndex < batches.count {
                    let batch = batches[batchIndex]
                    
                    group.addTask { [weak self] in
                        guard let self = self else { return [] }
                        return await self.processSingleBatch(batch)
                    }
                    
                    activeTasks += 1
                    batchIndex += 1
                }
                
                // 等待至少一个任务完成
                if let batchResult = await group.next() {
                    allImportedFiles.append(contentsOf: batchResult)
                    activeTasks -= 1
                    
                    // 更新进度
                    await updateOverallProgress()
                }
            }
            
            // 等待所有剩余任务完成
            for await batchResult in group {
                allImportedFiles.append(contentsOf: batchResult)
            }
        }
        
        return allImportedFiles
    }
    
    // MARK: - 处理单个批次
    private func processSingleBatch(_ batch: FileBatch) async -> [MediaFileInfo] {
        var batchCopy = batch
        batchCopy.status = .processing
        batchCopy.startTime = Date()
        
        await MainActor.run {
            if let index = currentBatches.firstIndex(where: { $0.id == batch.id }) {
                currentBatches[index] = batchCopy
            }
        }
        
        var importedFiles: [MediaFileInfo] = []
        
        for file in batch.files {
            // 检查暂停状态
            await checkPauseState()
            
            do {
                // 更新当前文件状态
                await MainActor.run {
                    progress.currentFile = file.sourceURL.lastPathComponent
                }
                
                // 导入单个文件（带重试机制）
                if let importedFile = await importSingleFileWithRetry(file) {
                    importedFiles.append(importedFile)
                    batchCopy.processedCount += 1
                    
                    await MainActor.run {
                        progress.successfulFiles += 1
                    }
                } else {
                    batchCopy.failedCount += 1
                    
                    await MainActor.run {
                        progress.failedFiles += 1
                    }
                }
                
                // 更新批次进度
                await MainActor.run {
                    progress.processedFiles += 1
                    if let index = currentBatches.firstIndex(where: { $0.id == batch.id }) {
                        currentBatches[index] = batchCopy
                    }
                }
                
            } catch {
                logger.error("批次处理错误: \(error.localizedDescription)")
                batchCopy.failedCount += 1
                
                await MainActor.run {
                    progress.failedFiles += 1
                    progress.errors.append(ImportError(
                        fileName: file.sourceURL.lastPathComponent,
                        folderPath: file.folderPath,
                        error: error,
                        timestamp: Date()
                    ))
                }
            }
        }
        
        // 标记批次完成
        batchCopy.status = .completed
        batchCopy.endTime = Date()
        
        await MainActor.run {
            if let index = currentBatches.firstIndex(where: { $0.id == batch.id }) {
                currentBatches[index] = batchCopy
            }
        }
        
        return importedFiles
    }
    
    // MARK: - 带重试的单文件导入
    private func importSingleFileWithRetry(_ file: FileToImport) async -> MediaFileInfo? {
        var lastError: Error?
        
        for attempt in 1...maxRetries {
            do {
                let importedFile = try await importSingleFile(file)
                if attempt > 1 {
                    logger.info("文件 \(file.sourceURL.lastPathComponent) 在第 \(attempt) 次尝试成功导入")
                }
                return importedFile
            } catch {
                lastError = error
                logger.warning("文件 \(file.sourceURL.lastPathComponent) 第 \(attempt) 次导入失败: \(error.localizedDescription)")
                
                if attempt < maxRetries {
                    // 等待后重试
                    try? await Task.sleep(nanoseconds: UInt64(retryDelay * 1_000_000_000))
                }
            }
        }
        
        // 记录最终失败
        await MainActor.run {
            if let error = lastError {
                progress.errors.append(ImportError(
                    fileName: file.sourceURL.lastPathComponent,
                    folderPath: file.folderPath,
                    error: error,
                    timestamp: Date()
                ))
            }
        }
        
        return nil
    }
    
    // MARK: - 导入单个文件
    private func importSingleFile(_ file: FileToImport) async throws -> MediaFileInfo {
        guard let mediaType = MediaFormatDetector.detectMediaType(from: file.sourceURL) else {
            throw MediaImportError.unsupportedFileType
        }
        
        let fileName = file.sourceURL.lastPathComponent
        
        // 创建目标文件夹
        let targetFolderURL = mediaDirectory.appendingPathComponent(file.folderPath)
        try fileManager.createDirectory(at: targetFolderURL, withIntermediateDirectories: true)
        
        // 生成唯一文件名
        let targetFileURL = targetFolderURL.appendingPathComponent(fileName)
        let finalTargetURL = generateUniqueFileURL(targetFileURL)
        
        // 优化大文件复制
        if file.isLargeFile {
            try await copyLargeFile(from: file.sourceURL, to: finalTargetURL)
        } else {
            try fileManager.copyItem(at: file.sourceURL, to: finalTargetURL)
        }
        
        // 生成文件ID
        let fileID = generateStableID(for: finalTargetURL)
        
        // 获取文件信息
        let resourceValues = try finalTargetURL.resourceValues(forKeys: [
            .fileSizeKey, .creationDateKey, .contentModificationDateKey
        ])
        
        let fileSize = Int64(resourceValues.fileSize ?? 0)
        let creationDate = resourceValues.creationDate ?? Date()
        let modificationDate = resourceValues.contentModificationDate ?? Date()
        
        // 异步生成缩略图（不阻塞导入）
        let thumbnailURL = await generateQuickThumbnail(for: finalTargetURL, fileID: fileID, mediaType: mediaType)
        
        // 获取媒体信息
        let (duration, dimensions) = await getMediaInfo(for: finalTargetURL, mediaType: mediaType)
        
        return MediaFileInfo(
            id: fileID,
            name: fileName,
            type: mediaType,
            fileSize: fileSize,
            creationDate: creationDate,
            modificationDate: modificationDate,
            localURL: finalTargetURL,
            thumbnailURL: thumbnailURL,
            folderPath: file.folderPath,
            duration: duration,
            dimensions: dimensions
        )
    }
    
    // MARK: - 工具方法
    
    private func checkPauseState() async {
        if pauseState {
            await MainActor.run {
                progress.isPaused = true
            }
            
            await withCheckedContinuation { continuation in
                resumeContinuation = continuation
            }
            
            await MainActor.run {
                progress.isPaused = false
            }
        }
    }
    
    private func updateOverallProgress() async {
        await MainActor.run {
            let completedBatches = currentBatches.filter { $0.status == .completed }.count
            progress.currentBatch = completedBatches
            progress.overallProgress = progress.totalBatches > 0 ? 
                Double(completedBatches) / Double(progress.totalBatches) : 0.0
            progress.updateTimeEstimate()
        }
    }
    
    private func generateUniqueFileURL(_ baseURL: URL) -> URL {
        var finalURL = baseURL
        var counter = 1
        
        while fileManager.fileExists(atPath: finalURL.path) {
            let nameWithoutExtension = baseURL.deletingPathExtension().lastPathComponent
            let fileExtension = baseURL.pathExtension
            let newFileName = "\(nameWithoutExtension)_\(counter).\(fileExtension)"
            finalURL = baseURL.deletingLastPathComponent().appendingPathComponent(newFileName)
            counter += 1
        }
        
        return finalURL
    }
    
    private func copyLargeFile(from sourceURL: URL, to targetURL: URL) async throws {
        // 使用较小的块大小来避免内存压力
        let chunkSize = 1024 * 1024 // 1MB chunks
        
        let sourceHandle = try FileHandle(forReadingFrom: sourceURL)
        defer { try? sourceHandle.close() }
        
        fileManager.createFile(atPath: targetURL.path, contents: nil)
        let targetHandle = try FileHandle(forWritingTo: targetURL)
        defer { try? targetHandle.close() }
        
        while true {
            let chunk = sourceHandle.readData(ofLength: chunkSize)
            if chunk.isEmpty { break }
            
            targetHandle.write(chunk)
            
            // 让其他任务有机会运行
            await Task.yield()
        }
    }
    
    private func generateQuickThumbnail(for fileURL: URL, fileID: UUID, mediaType: MediaType) async -> URL? {
        // 快速生成小尺寸缩略图用于立即显示
        let thumbnailFileName = "\(fileID.uuidString)_quick.jpg"
        let thumbnailURL = thumbnailDirectory.appendingPathComponent(thumbnailFileName)
        
        do {
            switch mediaType {
            case .image:
                try await generateImageThumbnail(from: fileURL, to: thumbnailURL, size: CGSize(width: 150, height: 150))
            case .video:
                try await generateVideoThumbnail(from: fileURL, to: thumbnailURL, size: CGSize(width: 150, height: 150))
            }
            return thumbnailURL
        } catch {
            logger.warning("快速缩略图生成失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    private func getMediaInfo(for fileURL: URL, mediaType: MediaType) async -> (TimeInterval?, CGSize?) {
        var duration: TimeInterval?
        var dimensions: CGSize?
        
        switch mediaType {
        case .video:
            let asset = AVURLAsset(url: fileURL)
            do {
                let isPlayable = try await asset.load(.isPlayable)
                if isPlayable {
                    duration = try await asset.load(.duration).seconds
                    if let track = try await asset.loadTracks(withMediaType: .video).first {
                        dimensions = try await track.load(.naturalSize)
                    }
                }
            } catch {
                logger.warning("获取视频信息失败: \(error.localizedDescription)")
            }
        case .image:
            if let imageSource = CGImageSourceCreateWithURL(fileURL as CFURL, nil),
               let properties = CGImageSourceCopyPropertiesAtIndex(imageSource, 0, nil) as? [CFString: Any] {
                let width = properties[kCGImagePropertyPixelWidth] as? CGFloat ?? 0
                let height = properties[kCGImagePropertyPixelHeight] as? CGFloat ?? 0
                dimensions = CGSize(width: width, height: height)
            }
        }
        
        return (duration, dimensions)
    }
    
    // MARK: - 暂停/恢复控制
    
    func pauseImport() {
        guard isImporting && !pauseState else { return }
        
        pauseState = true
        canPause = false
        canResume = true
        
        logger.info("导入已暂停")
    }
    
    func resumeImport() {
        guard isImporting && pauseState else { return }
        
        pauseState = false
        canPause = true
        canResume = false
        
        resumeContinuation?.resume()
        resumeContinuation = nil
        
        logger.info("导入已恢复")
    }
    
    func cancelImport() {
        // 取消所有操作
        for (_, task) in importOperations {
            task.cancel()
        }
        
        pauseState = false
        resumeContinuation?.resume()
        resumeContinuation = nil
        
        Task { @MainActor in
            isImporting = false
            canPause = false
            canResume = false
            progress.reset()
            currentBatches.removeAll()
        }
        
        logger.info("导入已取消")
    }

    
    // MARK: - 缩略图生成
    private func generateImageThumbnail(from sourceURL: URL, to targetURL: URL, size: CGSize) async throws {
        guard let imageSource = CGImageSourceCreateWithURL(sourceURL as CFURL, nil) else {
            throw MediaImportError.thumbnailGenerationFailed
        }
        
        let options: [CFString: Any] = [
            kCGImageSourceCreateThumbnailFromImageIfAbsent: true,
            kCGImageSourceCreateThumbnailWithTransform: true,
            kCGImageSourceThumbnailMaxPixelSize: max(size.width, size.height)
        ]
        
        guard let thumbnail = CGImageSourceCreateThumbnailAtIndex(imageSource, 0, options as CFDictionary) else {
            throw MediaImportError.thumbnailGenerationFailed
        }
        
        let uiImage = UIImage(cgImage: thumbnail)
        guard let imageData = uiImage.jpegData(compressionQuality: 0.8) else {
            throw MediaImportError.thumbnailGenerationFailed
        }
        
        try imageData.write(to: targetURL)
    }
    
    private func generateVideoThumbnail(from sourceURL: URL, to targetURL: URL, size: CGSize) async throws {
        let asset = AVURLAsset(url: sourceURL)
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        imageGenerator.maximumSize = size
        
        let time = CMTimeMake(value: 1, timescale: 1)
        
        do {
            let result = try await imageGenerator.image(at: time)
            let cgImage = result.image
            let uiImage = UIImage(cgImage: cgImage)
            
            guard let imageData = uiImage.jpegData(compressionQuality: 0.8) else {
                throw MediaImportError.thumbnailGenerationFailed
            }
            
            try imageData.write(to: targetURL)
        } catch {
            // 生成默认缩略图
            try generateDefaultVideoThumbnail(to: targetURL, size: size)
        }
    }
    
    private func generateDefaultVideoThumbnail(to targetURL: URL, size: CGSize) throws {
        let renderer = UIGraphicsImageRenderer(size: size)
        let image = renderer.image { context in
            UIColor.systemGray5.setFill()
            context.fill(CGRect(origin: .zero, size: size))
            
            let iconSize: CGFloat = min(size.width, size.height) * 0.4
            let iconRect = CGRect(
                x: (size.width - iconSize) / 2,
                y: (size.height - iconSize) / 2,
                width: iconSize,
                height: iconSize
            )
            
            UIColor.systemBlue.setFill()
            context.fill(iconRect)
        }
        
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw MediaImportError.thumbnailGenerationFailed
        }
        
        try imageData.write(to: targetURL)
    }
    
    // MARK: - 后台缩略图生成
    private func startBackgroundThumbnailGeneration(for files: [MediaFileInfo]) async {
        Task.detached(priority: .background) {
            await self.backgroundThumbnailService.generateHighQualityThumbnails(for: files)
        }
    }
    
    // MARK: - 工具方法
    private func generateStableID(for url: URL) -> UUID {
        let pathString = url.standardizedFileURL.path
        let hash = pathString.hashValue
        
        let uuidString = String(format: "%08X-%04X-%04X-%04X-%012X",
                               abs(hash) & 0xFFFFFFFF,
                               (abs(hash) >> 16) & 0xFFFF,
                               (abs(hash) >> 8) & 0xFFFF,
                               abs(hash) & 0xFFFF,
                               abs(hash) & 0xFFFFFFFFFFFF)
        
        return UUID(uuidString: uuidString) ?? UUID()
    }
    
    private func saveImportHistory(folderName: String, fileCount: Int, totalSize: Int64) async {
        let historyItem = ImportHistoryItem(
            folderName: folderName,
            importDate: Date(),
            fileCount: fileCount,
            totalSize: totalSize
        )
        
        var history: [ImportHistoryItem] = []
        if let data = UserDefaults.standard.data(forKey: "ImportHistory"),
           let existingHistory = try? JSONDecoder().decode([ImportHistoryItem].self, from: data) {
            history = existingHistory
        }
        
        history.append(historyItem)
        
        if history.count > 50 {
            history = Array(history.suffix(50))
        }
        
        if let data = try? JSONEncoder().encode(history) {
            UserDefaults.standard.set(data, forKey: "ImportHistory")
        }
    }
    
    private func calculateTotalSize(_ files: [MediaFileInfo]) -> Int64 {
        return files.reduce(0) { $0 + $1.fileSize }
    }
}

// MARK: - 导入历史项扩展
extension ImportHistoryItem {
    init(folderName: String, importDate: Date, fileCount: Int, totalSize: Int64) {
        self.init(
            folderName: folderName,
            fileCount: fileCount,
            totalSize: totalSize,
            importDate: importDate,
            status: .success
        )
    }
}

// MARK: - 通知扩展
extension Notification.Name {
    static let concurrentMediaImportCompleted = Notification.Name("concurrentMediaImportCompleted")
} 