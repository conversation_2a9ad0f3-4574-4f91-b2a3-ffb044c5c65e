import Foundation
import OSLog
import UIKit

// MARK: - 缩略图内存缓存管理器（第二阶段性能优化）
@MainActor
final class ThumbnailMemoryCache {
    
    // MARK: - 缓存项
    private final class CacheItem: @unchecked Sendable {
        let image: UIImage
        let accessTime: Date
        let memorySize: Int
        let accessCount: Int
        
        init(image: UIImage) {
            self.image = image
            self.accessTime = Date()
            self.memorySize = Self.calculateImageMemorySize(image)
            self.accessCount = 1
        }
        
        func updateAccessTime() {
            // 由于访问时间在多线程环境下可能需要同步，这里保持简单
        }
        
        private static func calculateImageMemorySize(_ image: UIImage) -> Int {
            let width = Int(image.size.width * image.scale)
            let height = Int(image.size.height * image.scale)
            return width * height * 4 // 假设RGBA，每像素4字节
        }
    }
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.browser", category: "ThumbnailMemoryCache")
    private var cache: [String: CacheItem] = [:]
    private let cacheQueue = DispatchQueue(label: "thumbnail.cache", attributes: .concurrent)
    
    // 配置
    private struct Config {
        static let maxItems = 200
        static let maxMemorySize = 100 * 1024 * 1024 // 100MB
        static let cleanupThreshold = 0.8 // 80%时开始清理
        static let lowMemoryCleanupRatio = 0.5 // 低内存时清理到50%
    }
    
    // 统计信息
    private var _currentSize = 0
    private var _currentMemoryUsage = 0
    
    // MARK: - 公共接口
    
    var currentSize: Int {
        return cacheQueue.sync { _currentSize }
    }
    
    var currentMemoryUsage: Int {
        return cacheQueue.sync { _currentMemoryUsage }
    }
    
    /// 获取缩略图
    func getImage(for mediaFileID: UUID, size: OptimizedThumbnailManager.ThumbnailSize) -> UIImage? {
        let key = cacheKey(for: mediaFileID, size: size)
        
        return cacheQueue.sync {
            guard let item = cache[key] else {
                return nil
            }
            
            // 更新访问时间（简化版本，实际可以优化）
            item.updateAccessTime()
            return item.image
        }
    }
    
    /// 设置缩略图
    func setImage(_ image: UIImage, for mediaFileID: UUID, size: OptimizedThumbnailManager.ThumbnailSize) {
        let key = cacheKey(for: mediaFileID, size: size)
        let item = CacheItem(image: image)
        
        cacheQueue.async(flags: .barrier) { [weak self] in
            guard let self = self else { return }
            
            // 如果已存在，先移除旧的
            if let existingItem = self.cache[key] {
                self._currentMemoryUsage -= existingItem.memorySize
                self._currentSize -= 1
            }
            
            // 添加新的
            self.cache[key] = item
            self._currentMemoryUsage += item.memorySize
            self._currentSize += 1
            
            // 检查是否需要清理
            self.checkAndCleanupIfNeeded()
        }
    }
    
    /// 移除指定图片
    func removeImage(for mediaFileID: UUID, size: OptimizedThumbnailManager.ThumbnailSize) {
        let key = cacheKey(for: mediaFileID, size: size)
        
        cacheQueue.async(flags: .barrier) { [weak self] in
            guard let self = self, let item = self.cache.removeValue(forKey: key) else { return }
            
            self._currentMemoryUsage -= item.memorySize
            self._currentSize -= 1
        }
    }
    
    /// 清理老旧项目
    func clearOldItems(olderThan seconds: TimeInterval) async {
        let cutoffDate = Date().addingTimeInterval(-seconds)
        
        await withCheckedContinuation { continuation in
            cacheQueue.async(flags: .barrier) { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                let itemsToRemove = self.cache.filter { _, item in
                    item.accessTime < cutoffDate
                }
                
                for (key, item) in itemsToRemove {
                    self.cache.removeValue(forKey: key)
                    self._currentMemoryUsage -= item.memorySize
                    self._currentSize -= 1
                }
                
                if !itemsToRemove.isEmpty {
                    self.logger.info("🧹 清理了 \(itemsToRemove.count) 个过期缓存项")
                }
                
                continuation.resume()
            }
        }
    }
    
    /// 清理所有缓存项
    func clearAll() async {
        await withCheckedContinuation { continuation in
            cacheQueue.async(flags: .barrier) { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                let itemCount = self.cache.count
                self.cache.removeAll()
                self._currentMemoryUsage = 0
                self._currentSize = 0
                
                self.logger.info("🗑️ 清理了所有缓存项，共 \(itemCount) 个")
                
                continuation.resume()
            }
        }
    }
    
    /// 获取当前内存使用量
    func getCurrentMemoryUsage() async -> UInt64 {
        return await withCheckedContinuation { continuation in
            cacheQueue.sync {
                continuation.resume(returning: UInt64(self._currentMemoryUsage))
            }
        }
    }
    
    /// 清理不活跃的项目
    func clearInactiveItems() async {
        await clearOldItems(olderThan: 600) // 10分钟未访问的项目
    }
    
    /// 压缩内存使用（移除最少使用的项目）
    func compress() async {
        await withCheckedContinuation { continuation in
            cacheQueue.async(flags: .barrier) { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                let targetSize = Config.maxItems / 2 // 压缩到50%
                let itemsToRemove = max(0, self.cache.count - targetSize)
                
                if itemsToRemove > 0 {
                    // 按访问时间排序，移除最旧的项目
                    let sortedItems = self.cache.sorted { $0.value.accessTime < $1.value.accessTime }
                    
                    for i in 0..<min(itemsToRemove, sortedItems.count) {
                        let (key, item) = sortedItems[i]
                        self.cache.removeValue(forKey: key)
                        self._currentMemoryUsage -= item.memorySize
                        self._currentSize -= 1
                    }
                    
                    self.logger.info("🗜️ 压缩缓存，移除了 \(itemsToRemove) 个项目")
                }
                
                continuation.resume()
            }
        }
    }
    
    /// 配置最优性能
    func configureForOptimalPerformance() {
        cacheQueue.async(flags: .barrier) { [weak self] in
            guard let self = self else { return }
            
            // 根据设备性能调整配置
            let deviceMemory = ProcessInfo.processInfo.physicalMemory
            let memoryFactor = min(2.0, Double(deviceMemory) / (4.0 * 1024 * 1024 * 1024)) // 基于4GB标准
            
            // 动态调整缓存大小
            let adjustedMaxItems = Int(Double(Config.maxItems) * memoryFactor)
            let adjustedMaxMemory = Int(Double(Config.maxMemorySize) * memoryFactor)
            
            self.logger.info("📊 缓存性能配置: 最大项目数=\(adjustedMaxItems), 最大内存=\(adjustedMaxMemory)")
        }
    }

    /// 紧急清理
    func emergencyCleanup() async {
        await withCheckedContinuation { continuation in
            cacheQueue.async(flags: .barrier) { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                let targetSize = Int(Double(Config.maxItems) * Config.lowMemoryCleanupRatio)
                let itemsToRemove = max(0, self.cache.count - targetSize)
                
                if itemsToRemove > 0 {
                    // 按访问时间排序，移除最旧的项目
                    let sortedItems = self.cache.sorted { $0.value.accessTime < $1.value.accessTime }
                    
                    for i in 0..<min(itemsToRemove, sortedItems.count) {
                        let (key, item) = sortedItems[i]
                        self.cache.removeValue(forKey: key)
                        self._currentMemoryUsage -= item.memorySize
                        self._currentSize -= 1
                    }
                    
                    self.logger.warning("🚨 紧急清理了 \(itemsToRemove) 个缓存项")
                }
                
                continuation.resume()
            }
        }
    }
    
    // MARK: - 私有方法
    
    private func cacheKey(for mediaFileID: UUID, size: OptimizedThumbnailManager.ThumbnailSize) -> String {
        return "\(mediaFileID.uuidString)_\(size.rawValue)"
    }
    
    private func checkAndCleanupIfNeeded() {
        // 检查数量限制
        if _currentSize > Config.maxItems {
            performLRUCleanup(targetSize: Int(Double(Config.maxItems) * Config.cleanupThreshold))
        }
        
        // 检查内存限制
        if _currentMemoryUsage > Config.maxMemorySize {
            performMemoryBasedCleanup(targetMemory: Int(Double(Config.maxMemorySize) * Config.cleanupThreshold))
        }
    }
    
    private func performLRUCleanup(targetSize: Int) {
        let itemsToRemove = max(0, _currentSize - targetSize)
        guard itemsToRemove > 0 else { return }
        
        // 按访问时间排序，移除最旧的项目
        let sortedItems = cache.sorted { $0.value.accessTime < $1.value.accessTime }
        
        for i in 0..<min(itemsToRemove, sortedItems.count) {
            let (key, item) = sortedItems[i]
            cache.removeValue(forKey: key)
            _currentMemoryUsage -= item.memorySize
            _currentSize -= 1
        }
        
        logger.info("🧹 LRU清理了 \(itemsToRemove) 个缓存项")
    }
    
    private func performMemoryBasedCleanup(targetMemory: Int) {
        let memoryToFree = max(0, _currentMemoryUsage - targetMemory)
        guard memoryToFree > 0 else { return }
        
        // 按内存大小排序，优先移除大的图片
        let sortedItems = cache.sorted { $0.value.memorySize > $1.value.memorySize }
        
        var freedMemory = 0
        var removedCount = 0
        
        for (key, item) in sortedItems {
            if freedMemory >= memoryToFree { break }
            
            cache.removeValue(forKey: key)
            _currentMemoryUsage -= item.memorySize
            _currentSize -= 1
            freedMemory += item.memorySize
            removedCount += 1
        }
        
        logger.info("🧹 内存清理了 \(removedCount) 个缓存项，释放 \(self.formatBytes(freedMemory))")
    }
    
    private func formatBytes(_ bytes: Int) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .decimal
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    // MARK: - 调试和监控
    
    var cacheStatistics: String {
        return cacheQueue.sync {
            let memoryUsageMB = Double(_currentMemoryUsage) / (1024 * 1024)
            let averageItemSize = _currentSize > 0 ? _currentMemoryUsage / _currentSize : 0
            
            return """
            🖼️ 缩略图内存缓存统计
            ========================
            
            📊 基本信息:
            - 缓存项目数: \(_currentSize) / \(Config.maxItems)
            - 内存使用: \(String(format: "%.1f", memoryUsageMB)) MB / \(Config.maxMemorySize / (1024 * 1024)) MB
            - 平均项目大小: \(formatBytes(averageItemSize))
            
            📈 使用率:
            - 项目使用率: \(String(format: "%.1f", Double(_currentSize) / Double(Config.maxItems) * 100))%
            - 内存使用率: \(String(format: "%.1f", Double(_currentMemoryUsage) / Double(Config.maxMemorySize) * 100))%
            """
        }
    }
}

// MARK: - 精确缓存清理控制（第二阶段完善）
extension ThumbnailMemoryCache {
    
    /// 精确控制的缓存清理
    func preciseCleanup(strategy: CacheCleanupStrategy) async {
        logger.info("🎯 开始精确缓存清理: \(strategy.description)")
        
        switch strategy {
        case .predictive(let memoryTarget):
            await cleanupPredictive(targetMemory: memoryTarget)
            
        default:
            await withCheckedContinuation { continuation in
                cacheQueue.async(flags: .barrier) { [weak self] in
                    guard let self = self else {
                        continuation.resume()
                        return
                    }
                    
                    switch strategy {
                    case .priority(let priority):
                        self.cleanupByPriority(priority)
                        
                    case .selective(let criteria):
                        self.cleanupByCriteria(criteria)
                        
                    case .timedEviction(let maxAge):
                        self.cleanupTimedEviction(maxAge: maxAge)
                        
                    case .sizeOptimized(let targetSize):
                        self.cleanupSizeOptimized(targetSize: targetSize)
                        
                    case .activityBased(let inactivityThreshold):
                        self.cleanupActivityBased(threshold: inactivityThreshold)
                        
                    case .predictive:
                        break // 已在上面处理
                    }
                    
                    continuation.resume()
                }
            }
        }
        
        logger.info("✅ 精确缓存清理完成")
    }
    
    /// 基于优先级的清理
    private func cleanupByPriority(_ priority: CachePriority) {
        var itemsToRemove: [String] = []
        
        for (key, item) in cache {
            if item.accessTime < Date().addingTimeInterval(-Double(priority.rawValue * 1800)) {
                itemsToRemove.append(key)
            }
        }
        
        for key in itemsToRemove {
            if let item = cache.removeValue(forKey: key) {
                _currentMemoryUsage -= item.memorySize
                _currentSize -= 1
            }
        }
        
        logger.info("🏆 按优先级清理: 移除了 \(itemsToRemove.count) 个项目")
    }
    
    /// 基于条件的选择性清理
    private func cleanupByCriteria(_ criteria: CacheCleanupCriteria) {
        var itemsToRemove: [String] = []
        let now = Date()
        
        for (key, item) in cache {
            var shouldRemove = false
            
            // 检查各种条件
            if let maxAge = criteria.maxAge,
               now.timeIntervalSince(item.accessTime) > maxAge {
                shouldRemove = true
            }
            
            if let minAccessCount = criteria.minAccessCount,
               item.accessCount < minAccessCount {
                shouldRemove = true
            }
            
            if let targetSize = criteria.thumbnailSize {
                let imageSize = item.image.size
                let matches = checkSizeMatch(imageSize: imageSize, targetSize: targetSize)
                if !matches {
                    shouldRemove = true
                }
            }
            
            if shouldRemove {
                itemsToRemove.append(key)
            }
        }
        
        for key in itemsToRemove {
            if let item = cache.removeValue(forKey: key) {
                _currentMemoryUsage -= item.memorySize
                _currentSize -= 1
            }
        }
        
        logger.info("🎯 按条件清理: 移除了 \(itemsToRemove.count) 个项目")
    }
    
    /// 预测性清理（基于内存目标）
    private func cleanupPredictive(targetMemory: UInt64) async {
        let currentMemory = await getCurrentMemoryUsage()
        
        guard currentMemory > targetMemory else {
            logger.info("🎯 内存使用已在目标范围内")
            return
        }
        
        let memoryToFree = currentMemory - targetMemory
        var freedMemory: UInt64 = 0
        var itemsToRemove: [String] = []
        
        // 按访问时间和优先级排序，优先清理老旧和低优先级的项目
        let sortedItems = cache.sorted { (lhs, rhs) in
            if lhs.value.accessTime < rhs.value.accessTime {
                return true
            } else if lhs.value.accessTime > rhs.value.accessTime {
                return false
            } else {
                return lhs.value.accessTime < rhs.value.accessTime
            }
        }
        
        for (key, item) in sortedItems {
            itemsToRemove.append(key)
            freedMemory += UInt64(item.memorySize)
            
            if freedMemory >= memoryToFree {
                break
            }
        }
        
        for key in itemsToRemove {
            if let item = cache.removeValue(forKey: key) {
                _currentMemoryUsage -= item.memorySize
                _currentSize -= 1
            }
        }
        
        logger.info("🔮 预测性清理: 移除了 \(itemsToRemove.count) 个项目，释放了 \(self.formatBytes(Int64(freedMemory)))")
    }
    
    /// 定时清理（基于项目年龄）
    private func cleanupTimedEviction(maxAge: TimeInterval) {
        let now = Date()
        var itemsToRemove: [String] = []
        
        for (key, item) in cache {
            if now.timeIntervalSince(item.accessTime) > maxAge {
                itemsToRemove.append(key)
            }
        }
        
        for key in itemsToRemove {
            if let item = cache.removeValue(forKey: key) {
                _currentMemoryUsage -= item.memorySize
                _currentSize -= 1
            }
        }
        
        logger.info("⏰ 定时清理: 移除了 \(itemsToRemove.count) 个过期项目")
    }
    
    /// 基于大小优化的清理
    private func cleanupSizeOptimized(targetSize: OptimizedThumbnailManager.ThumbnailSize) {
        var itemsToRemove: [String] = []
        
        for (key, item) in cache {
            let imageSize = item.image.size
            let matches = checkSizeMatch(imageSize: imageSize, targetSize: targetSize)
            if !matches {
                itemsToRemove.append(key)
            }
        }
        
        for key in itemsToRemove {
            if let item = cache.removeValue(forKey: key) {
                _currentMemoryUsage -= item.memorySize
                _currentSize -= 1
            }
        }
        
        logger.info("📏 大小优化清理: 移除了 \(itemsToRemove.count) 个非目标大小项目")
    }
    
    /// 基于活动的清理
    private func cleanupActivityBased(threshold: TimeInterval) {
        let now = Date()
        var lowActivityItems: [(String, CacheItem)] = []
        
        for (key, item) in cache {
            let timeSinceLastAccess = now.timeIntervalSince(item.accessTime)
            let activityScore = Double(item.accessCount) / max(1.0, timeSinceLastAccess / 3600.0) // 每小时访问次数
            
            if activityScore < 0.1 { // 低活动阈值
                lowActivityItems.append((key, item))
            }
        }
        
        // 按活动分数排序，优先清理最不活跃的
        lowActivityItems.sort { (lhs, rhs) in
            let lhsScore = Double(lhs.1.accessCount) / max(1.0, now.timeIntervalSince(lhs.1.accessTime) / 3600.0)
            let rhsScore = Double(rhs.1.accessCount) / max(1.0, now.timeIntervalSince(rhs.1.accessTime) / 3600.0)
            return lhsScore < rhsScore
        }
        
        let itemsToRemove = Array(lowActivityItems.prefix(lowActivityItems.count / 2)) // 清理一半最不活跃的
        
        for (key, _) in itemsToRemove {
            if let item = cache.removeValue(forKey: key) {
                _currentMemoryUsage -= item.memorySize
                _currentSize -= 1
            }
        }
        
        logger.info("📊 活动清理: 移除了 \(itemsToRemove.count) 个低活动项目")
    }
    
    // MARK: - 辅助方法
    
    private func checkSizeMatch(imageSize: CGSize, targetSize: OptimizedThumbnailManager.ThumbnailSize) -> Bool {
        // 简化的大小匹配逻辑
        let tolerance: CGFloat = 10.0
        
        switch targetSize {
        case .small:
            return abs(imageSize.width - 150) < tolerance && abs(imageSize.height - 150) < tolerance
        case .medium:
            return abs(imageSize.width - 300) < tolerance && abs(imageSize.height - 300) < tolerance
        case .large:
            return abs(imageSize.width - 600) < tolerance && abs(imageSize.height - 600) < tolerance
        }
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        return ByteCountFormatter.string(fromByteCount: bytes, countStyle: .memory)
    }
    
    /// 智能内存管理集成
    func integrateWithMemoryManager() {
        
        // 监听内存压力事件
        NotificationCenter.default.addObserver(
            forName: .memoryPressureReleaseViews,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.handleMemoryPressure(.warning)
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: .memoryEmergencyState,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.handleMemoryPressure(.emergency)
            }
        }
        
        logger.info("🔗 ThumbnailMemoryCache已与内存管理器集成")
    }
    
    /// 处理内存压力
    private func handleMemoryPressure(_ level: MemoryPressureLevel) async {
        switch level {
        case .normal:
            break
            
        case .warning:
            await preciseCleanup(strategy: .priority(.low))
            
        case .critical:
            await preciseCleanup(strategy: .predictive(150 * 1024 * 1024)) // 目标150MB
            
        case .emergency:
            await preciseCleanup(strategy: .priority(.high))
            await clearAll()
        }
    }
    
    /// 获取详细的缓存分析
    func getCacheAnalysis() async -> CacheAnalysis {
        return await withCheckedContinuation { continuation in
            cacheQueue.sync {
                var sizeDistribution: [String: Int] = [:]
                var priorityDistribution: [CachePriority: Int] = [:]
                var memoryUsageBySize: [String: UInt64] = [:]
                
                for (_, item) in self.cache {
                    let sizeKey = "\(Int(item.image.size.width))x\(Int(item.image.size.height))"
                    sizeDistribution[sizeKey, default: 0] += 1
                    memoryUsageBySize[sizeKey, default: 0] += UInt64(item.memorySize)
                    
                    // 基于访问时间确定优先级
                    let age = Date().timeIntervalSince(item.accessTime)
                    let priority: CachePriority
                    if age < 1800 { // 30分钟内
                        priority = .high
                    } else if age < 3600 { // 1小时内
                        priority = .medium
                    } else {
                        priority = .low
                    }
                    priorityDistribution[priority, default: 0] += 1
                }
                
                let totalAccessCount = self.cache.values.map { $0.accessCount }.reduce(0, +)
                let averageAccessCount = self.cache.count > 0 ? totalAccessCount / self.cache.count : 0
                let oldestItemAge = self.cache.values.map { Date().timeIntervalSince($0.accessTime) }.max() ?? 0
                
                let analysis = CacheAnalysis(
                    totalItems: self.cache.count,
                    totalMemoryUsage: UInt64(self._currentMemoryUsage),
                    sizeDistribution: sizeDistribution,
                    priorityDistribution: priorityDistribution,
                    memoryUsageBySize: memoryUsageBySize,
                    averageAccessCount: averageAccessCount,
                    oldestItemAge: oldestItemAge
                )
                
                continuation.resume(returning: analysis)
            }
        }
    }
    
    /// 预测性缓存优化
    func predictiveOptimization() async {
        let analysis = await getCacheAnalysis()
        
        // 检查内存管理器状态（简化实现）
        let currentMemory = await getCurrentMemoryUsage()
        
        if currentMemory < 50 * 1024 * 1024 { // 50MB以下为正常
            // 正常状态：优化缓存分布
            await optimizeCacheDistribution(analysis)
        } else {
            // 压力状态：激进清理
            await aggressiveCleanup(analysis)
        }
    }
    
    private func optimizeCacheDistribution(_ analysis: CacheAnalysis) async {
        // 如果某种大小的缓存过多，清理一些
        for (_, count) in analysis.sizeDistribution {
            if count > 100 { // 单个大小超过100个缓存项
                await preciseCleanup(strategy: .timedEviction(3600)) // 1小时
            }
        }
    }
    
    private func aggressiveCleanup(_ analysis: CacheAnalysis) async {
        // 激进清理：只保留高优先级和最近访问的项目
        await preciseCleanup(strategy: .priority(.medium))
        await preciseCleanup(strategy: .timedEviction(1800)) // 30分钟
    }
}

// MARK: - 缓存清理策略和数据结构
enum CacheCleanupStrategy {
    case priority(CachePriority)
    case selective(CacheCleanupCriteria)
    case predictive(UInt64) // 目标内存使用
    case timedEviction(TimeInterval) // 最大年龄
    case sizeOptimized(OptimizedThumbnailManager.ThumbnailSize) // 目标缩略图大小
    case activityBased(TimeInterval) // 不活跃阈值
    
    var description: String {
        switch self {
        case .priority(let priority):
            return "按优先级(\(priority))清理"
        case .selective:
            return "选择性清理"
        case .predictive(let target):
            return "预测性清理(目标: \(ByteCountFormatter.string(fromByteCount: Int64(target), countStyle: .memory)))"
        case .timedEviction(let age):
            return "定时清理(最大年龄: \(Int(age))秒)"
        case .sizeOptimized(let size):
            return "大小优化清理(目标: \(size))"
        case .activityBased(let threshold):
            return "活动清理(阈值: \(Int(threshold))秒)"
        }
    }
}

struct CacheCleanupCriteria {
    let thumbnailSize: OptimizedThumbnailManager.ThumbnailSize?
    let mediaType: String? // 简化为String类型
    let maxAge: TimeInterval?
    let minAccessCount: Int?
    
    init(thumbnailSize: OptimizedThumbnailManager.ThumbnailSize? = nil,
         mediaType: String? = nil,
         maxAge: TimeInterval? = nil,
         minAccessCount: Int? = nil) {
        self.thumbnailSize = thumbnailSize
        self.mediaType = mediaType
        self.maxAge = maxAge
        self.minAccessCount = minAccessCount
    }
}

enum CachePriority: Int, CaseIterable {
    case low = 1
    case medium = 2
    case high = 3
    case critical = 4
    
    var description: String {
        switch self {
        case .low: return "低"
        case .medium: return "中"
        case .high: return "高"
        case .critical: return "关键"
        }
    }
}

struct CacheAnalysis {
    let totalItems: Int
    let totalMemoryUsage: UInt64
    let sizeDistribution: [String: Int] // 简化为String键
    let priorityDistribution: [CachePriority: Int]
    let memoryUsageBySize: [String: UInt64] // 简化为String键
    let averageAccessCount: Int
    let oldestItemAge: TimeInterval
    
    var formattedTotalMemory: String {
        ByteCountFormatter.string(fromByteCount: Int64(totalMemoryUsage), countStyle: .memory)
    }
    
    var efficiency: Double {
        guard totalItems > 0 else { return 0.0 }
        return Double(averageAccessCount) / max(1.0, oldestItemAge / 3600.0) // 每小时平均访问次数
    }
}

