//
//  ImportPerformanceTester.swift
//  cop
//
//  Created by 阿亮 on 2025/6/19.
//  第三阶段性能优化 - 导入性能测试工具
//

import Foundation
import SwiftUI
import os.log

// MARK: - 性能测试结果
struct PerformanceTestResult {
    let testName: String
    let totalFiles: Int
    let totalSize: Int64
    let importDuration: TimeInterval
    let avgFileProcessingTime: TimeInterval
    let throughputFilesPerSecond: Double
    let throughputMBPerSecond: Double
    let memoryPeakUsage: UInt64
    let memoryAverageUsage: UInt64
    let errorsCount: Int
    let successRate: Double
    let testDate: Date
    
    var formattedTotalSize: String {
        ByteCountFormatter.string(fromByteCount: totalSize, countStyle: .file)
    }
    
    var formattedThroughputMB: String {
        String(format: "%.2f MB/s", throughputMBPerSecond)
    }
    
    var formattedMemoryPeak: String {
        ByteCountFormatter.string(fromByteCount: Int64(memoryPeakUsage), countStyle: .memory)
    }
    
    var formattedMemoryAverage: String {
        ByteCountFormatter.string(fromByteCount: Int64(memoryAverageUsage), countStyle: .memory)
    }
    
    var formattedSuccessRate: String {
        String(format: "%.1f%%", successRate * 100)
    }
}

// MARK: - 性能基准测试工具
@MainActor
class ImportPerformanceTester: ObservableObject {
    static let shared = ImportPerformanceTester()
    
    @Published var isRunning = false
    @Published var currentTest = ""
    @Published var testResults: [PerformanceTestResult] = []
    @Published var testProgress: Double = 0.0
    
    private let logger = Logger(subsystem: "com.cop.app", category: "PerformanceTest")
    private let fileManager = FileManager.default
    
    // 测试配置
    private let testConfigurations = [
        TestConfiguration(name: "小文件集合", fileCount: 100, avgFileSize: 1024 * 1024), // 1MB
        TestConfiguration(name: "中等文件集合", fileCount: 500, avgFileSize: 5 * 1024 * 1024), // 5MB
        TestConfiguration(name: "大文件集合", fileCount: 50, avgFileSize: 50 * 1024 * 1024), // 50MB
        TestConfiguration(name: "混合文件集合", fileCount: 1000, avgFileSize: 10 * 1024 * 1024), // 10MB
        TestConfiguration(name: "压力测试", fileCount: 2000, avgFileSize: 8 * 1024 * 1024) // 8MB
    ]
    
    private init() {
        loadPreviousResults()
    }
    
    // MARK: - 公共接口
    
    /// 运行完整的性能测试套件
    func runPerformanceTestSuite() async {
        guard !isRunning else { return }
        
        isRunning = true
        testResults.removeAll()
        
        defer {
            isRunning = false
            currentTest = ""
            testProgress = 0.0
        }
        
        logger.info("开始运行性能测试套件")
        
        for (index, config) in testConfigurations.enumerated() {
            currentTest = config.name
            testProgress = Double(index) / Double(testConfigurations.count)
            
            do {
                let result = await runSingleTest(config: config)
                testResults.append(result)
                
                // 保存结果
                savePreviousResults()
                
                // 短暂休息以避免系统过载
                try await Task.sleep(nanoseconds: 2_000_000_000) // 2秒
                
            } catch {
                logger.error("测试 \(config.name) 失败: \(error.localizedDescription)")
            }
        }
        
        testProgress = 1.0
        currentTest = "测试完成"
        
        logger.info("性能测试套件完成，共 \(self.testResults.count) 个测试")
        
        // 输出测试报告
        generateTestReport()
    }
    
    /// 运行单个性能测试
    func runSingleTest(config: TestConfiguration) async -> PerformanceTestResult {
        logger.info("开始测试: \(config.name)")
        
        // 1. 创建测试数据
        let testDataURL = try! createTestData(config: config)
        defer {
            // 清理测试数据
            try? fileManager.removeItem(at: testDataURL)
        }
        
        // 2. 运行原版导入服务测试
        let legacyResult = await runLegacyImportTest(sourceURL: testDataURL, config: config)
        
        // 3. 清理导入的文件
        clearImportedFiles()
        
        // 4. 运行并发导入服务测试
        let concurrentResult = await runConcurrentImportTest(sourceURL: testDataURL, config: config)
        
        // 5. 清理导入的文件
        clearImportedFiles()
        
        // 6. 比较结果
        let comparisonResult = compareResults(legacy: legacyResult, concurrent: concurrentResult, config: config)
        
        logger.info("测试 \(config.name) 完成")
        
        return comparisonResult
    }
    
    /// 生成测试报告
    func generateTestReport() {
        let report = TestReport(results: testResults)
        let reportString = report.generateMarkdownReport()
        
        // 保存报告到文件
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let reportURL = documentsPath.appendingPathComponent("performance_test_report.md")
        
        do {
            try reportString.write(to: reportURL, atomically: true, encoding: .utf8)
            logger.info("测试报告已保存到: \(reportURL.path)")
        } catch {
            logger.error("保存测试报告失败: \(error.localizedDescription)")
        }
        
        // 打印到控制台
        print("\n" + String(repeating: "=", count: 80))
        print("性能测试报告")
        print(String(repeating: "=", count: 80))
        print(reportString)
        print(String(repeating: "=", count: 80) + "\n")
    }
    
    // MARK: - 私有方法
    
    private func createTestData(config: TestConfiguration) throws -> URL {
        let testDataURL = fileManager.temporaryDirectory
            .appendingPathComponent("PerformanceTest_\(UUID().uuidString)")
        
        try fileManager.createDirectory(at: testDataURL, withIntermediateDirectories: true)
        
        // 创建不同类型的测试文件
        let imageCount = config.fileCount * 7 / 10 // 70% 图片
        let videoCount = config.fileCount - imageCount // 30% 视频
        
        // 创建图片文件
        for i in 0..<imageCount {
            let imageData = generateTestImageData(size: config.avgFileSize / 2)
            let imageURL = testDataURL.appendingPathComponent("test_image_\(i).jpg")
            try imageData.write(to: imageURL)
        }
        
        // 创建视频文件
        for i in 0..<videoCount {
            let videoData = generateTestVideoData(size: config.avgFileSize * 2)
            let videoURL = testDataURL.appendingPathComponent("test_video_\(i).mp4")
            try videoData.write(to: videoURL)
        }
        
        return testDataURL
    }
    
    private func generateTestImageData(size: Int) -> Data {
        // 生成随机的JPEG数据
        let imageSize = CGSize(width: 800, height: 600)
        let renderer = UIGraphicsImageRenderer(size: imageSize)
        
        let image = renderer.image { context in
            // 随机背景色
            let hue = Double.random(in: 0...1)
            let color = UIColor(hue: hue, saturation: 0.7, brightness: 0.8, alpha: 1.0)
            color.setFill()
            context.fill(CGRect(origin: .zero, size: imageSize))
            
            // 添加一些随机内容
            for _ in 0..<100 {
                let rect = CGRect(
                    x: Double.random(in: 0...Double(imageSize.width)),
                    y: Double.random(in: 0...Double(imageSize.height)),
                    width: Double.random(in: 10...50),
                    height: Double.random(in: 10...50)
                )
                
                let rectColor = UIColor(hue: Double.random(in: 0...1), saturation: 0.8, brightness: 0.6, alpha: 0.7)
                rectColor.setFill()
                context.fill(rect)
            }
        }
        
        let data = image.jpegData(compressionQuality: 0.8) ?? Data()
        
        // 调整到目标大小
        if data.count < size {
            var paddedData = data
            let paddingSize = size - data.count
            let padding = Data(repeating: 0, count: paddingSize)
            paddedData.append(padding)
            return paddedData
        }
        
        return data
    }
    
    private func generateTestVideoData(size: Int) -> Data {
        // 生成模拟的MP4数据（实际上是填充数据）
        // 在真实测试中，这里应该生成实际的视频文件
        return Data(repeating: UInt8.random(in: 0...255), count: size)
    }
    
    private func runLegacyImportTest(sourceURL: URL, config: TestConfiguration) async -> TestResult {
        let startTime = Date()
        let memoryMonitor = MemoryMonitor()
        memoryMonitor.startMonitoring()
        
        do {
            let importedFiles = try await MediaImportService.shared.importMediaFolder(from: sourceURL)
            
            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)
            
            memoryMonitor.stopMonitoring()
            
            return TestResult(
                name: "Legacy_\(config.name)",
                filesProcessed: importedFiles.count,
                totalSize: importedFiles.reduce(0) { $0 + $1.fileSize },
                duration: duration,
                errors: 0,
                peakMemory: memoryMonitor.peakMemoryUsage,
                averageMemory: memoryMonitor.averageMemoryUsage
            )
            
        } catch {
            memoryMonitor.stopMonitoring()
            
            return TestResult(
                name: "Legacy_\(config.name)",
                filesProcessed: 0,
                totalSize: 0,
                duration: 0,
                errors: 1,
                peakMemory: memoryMonitor.peakMemoryUsage,
                averageMemory: memoryMonitor.averageMemoryUsage
            )
        }
    }
    
    private func runConcurrentImportTest(sourceURL: URL, config: TestConfiguration) async -> TestResult {
        let startTime = Date()
        let memoryMonitor = MemoryMonitor()
        memoryMonitor.startMonitoring()
        
        do {
            let importedFiles = try await ConcurrentMediaImportService.shared.importMediaFolder(from: sourceURL)
            
            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)
            
            memoryMonitor.stopMonitoring()
            
            return TestResult(
                name: "Concurrent_\(config.name)",
                filesProcessed: importedFiles.count,
                totalSize: importedFiles.reduce(0) { $0 + $1.fileSize },
                duration: duration,
                errors: ConcurrentMediaImportService.shared.progress.errors.count,
                peakMemory: memoryMonitor.peakMemoryUsage,
                averageMemory: memoryMonitor.averageMemoryUsage
            )
            
        } catch {
            memoryMonitor.stopMonitoring()
            
            return TestResult(
                name: "Concurrent_\(config.name)",
                filesProcessed: 0,
                totalSize: 0,
                duration: 0,
                errors: 1,
                peakMemory: memoryMonitor.peakMemoryUsage,
                averageMemory: memoryMonitor.averageMemoryUsage
            )
        }
    }
    
    private func compareResults(legacy: TestResult, concurrent: TestResult, config: TestConfiguration) -> PerformanceTestResult {
        let _ = legacy.duration > 0 ? legacy.duration / concurrent.duration : 1.0
        
        return PerformanceTestResult(
            testName: config.name,
            totalFiles: config.fileCount,
            totalSize: Int64(config.fileCount) * Int64(config.avgFileSize),
            importDuration: concurrent.duration,
            avgFileProcessingTime: concurrent.duration / Double(max(concurrent.filesProcessed, 1)),
            throughputFilesPerSecond: Double(concurrent.filesProcessed) / max(concurrent.duration, 0.001),
            throughputMBPerSecond: Double(concurrent.totalSize) / (1024 * 1024) / max(concurrent.duration, 0.001),
            memoryPeakUsage: concurrent.peakMemory,
            memoryAverageUsage: concurrent.averageMemory,
            errorsCount: concurrent.errors,
            successRate: Double(concurrent.filesProcessed) / Double(config.fileCount),
            testDate: Date()
        )
    }
    
    private func clearImportedFiles() {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let mediaDirectory = documentsPath.appendingPathComponent("MediaFiles")
        let thumbnailDirectory = documentsPath.appendingPathComponent("Thumbnails")
        
        try? fileManager.removeItem(at: mediaDirectory)
        try? fileManager.removeItem(at: thumbnailDirectory)
        
        // 重新创建目录
        try? fileManager.createDirectory(at: mediaDirectory, withIntermediateDirectories: true)
        try? fileManager.createDirectory(at: thumbnailDirectory, withIntermediateDirectories: true)
    }
    
    private func loadPreviousResults() {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let resultsURL = documentsPath.appendingPathComponent("performance_results.json")
        
        guard let data = try? Data(contentsOf: resultsURL),
              let results = try? JSONDecoder().decode([PerformanceTestResult].self, from: data) else {
            return
        }
        
        testResults = results
    }
    
    private func savePreviousResults() {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let resultsURL = documentsPath.appendingPathComponent("performance_results.json")
        
        guard let data = try? JSONEncoder().encode(testResults) else { return }
        try? data.write(to: resultsURL)
    }
}

// MARK: - 辅助结构

struct TestConfiguration {
    let name: String
    let fileCount: Int
    let avgFileSize: Int
}

struct TestResult {
    let name: String
    let filesProcessed: Int
    let totalSize: Int64
    let duration: TimeInterval
    let errors: Int
    let peakMemory: UInt64
    let averageMemory: UInt64
}

// MARK: - 内存监控器
class MemoryMonitor {
    private var isMonitoring = false
    private var memoryReadings: [UInt64] = []
    private var monitoringTask: Task<Void, Never>?
    
    var peakMemoryUsage: UInt64 {
        memoryReadings.max() ?? 0
    }
    
    var averageMemoryUsage: UInt64 {
        guard !memoryReadings.isEmpty else { return 0 }
        let sum = memoryReadings.reduce(0, +)
        return sum / UInt64(memoryReadings.count)
    }
    
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        memoryReadings.removeAll()
        
        monitoringTask = Task {
            while isMonitoring {
                let usage = getCurrentMemoryUsage()
                memoryReadings.append(usage)
                
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
            }
        }
    }
    
    func stopMonitoring() {
        isMonitoring = false
        monitoringTask?.cancel()
        monitoringTask = nil
    }
    
    private func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return info.resident_size
        } else {
            return 0
        }
    }
}

// MARK: - 测试报告生成器
struct TestReport {
    let results: [PerformanceTestResult]
    
    func generateMarkdownReport() -> String {
        var report = """
        # 导入性能测试报告
        
        生成时间: \(DateFormatter.localizedString(from: Date(), dateStyle: .medium, timeStyle: .medium))
        
        ## 测试概览
        
        - 测试项目数量: \(results.count)
        - 测试设备: iPad mini (A17 Pro)
        - 系统版本: iOS 18.4
        
        ## 详细结果
        
        """
        
        for result in results {
            report += """
            
            ### \(result.testName)
            
            - **文件数量**: \(result.totalFiles)
            - **总大小**: \(result.formattedTotalSize)
            - **导入时间**: \(String(format: "%.2f", result.importDuration)) 秒
            - **吞吐量**: \(String(format: "%.1f", result.throughputFilesPerSecond)) 文件/秒, \(result.formattedThroughputMB)
            - **内存峰值**: \(result.formattedMemoryPeak)
            - **内存平均**: \(result.formattedMemoryAverage)
            - **成功率**: \(result.formattedSuccessRate)
            - **错误数**: \(result.errorsCount)
            
            """
        }
        
        // 添加性能总结
        report += generatePerformanceSummary()
        
        return report
    }
    
    private func generatePerformanceSummary() -> String {
        guard !results.isEmpty else { return "" }
        
        let avgThroughput = results.map { $0.throughputFilesPerSecond }.reduce(0, +) / Double(results.count)
        let avgMemoryUsage = results.map { $0.memoryAverageUsage }.reduce(0, +) / UInt64(results.count)
        let totalSuccessRate = results.map { $0.successRate }.reduce(0, +) / Double(results.count)
        
        return """
        
        ## 性能总结
        
        - **平均吞吐量**: \(String(format: "%.1f", avgThroughput)) 文件/秒
        - **平均内存使用**: \(ByteCountFormatter.string(fromByteCount: Int64(avgMemoryUsage), countStyle: .memory))
        - **总体成功率**: \(String(format: "%.1f%%", totalSuccessRate * 100))
        
        ## 优化建议
        
        \(generateOptimizationRecommendations())
        """
    }
    
    private func generateOptimizationRecommendations() -> String {
        var recommendations: [String] = []
        
        let avgSuccessRate = results.map { $0.successRate }.reduce(0, +) / Double(results.count)
        if avgSuccessRate < 0.95 {
            recommendations.append("- 提高错误处理和重试机制")
        }
        
        let avgMemory = results.map { $0.memoryAverageUsage }.reduce(0, +) / UInt64(results.count)
        if avgMemory > 500 * 1024 * 1024 { // 500MB
            recommendations.append("- 优化内存使用，考虑更积极的缓存清理")
        }
        
        let slowTests = results.filter { $0.throughputFilesPerSecond < 10 }
        if !slowTests.isEmpty {
            recommendations.append("- 针对大文件优化处理速度")
        }
        
        if recommendations.isEmpty {
            recommendations.append("- 当前性能表现良好，继续监控")
        }
        
        return recommendations.joined(separator: "\n")
    }
}

// MARK: - PerformanceTestResult Codable
extension PerformanceTestResult: Codable {} 