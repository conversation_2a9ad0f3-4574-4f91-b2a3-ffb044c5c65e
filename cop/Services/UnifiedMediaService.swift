import Foundation
import SwiftUI
import Combine
import OSLog
import CryptoKit

// MARK: - 并发级别枚举
enum ConcurrencyLevel: String, CaseIterable {
    case sequential = "sequential"  // 顺序导入
    case concurrent = "concurrent"  // 并发导入
    case adaptive = "adaptive"      // 自适应（根据设备性能）
    
    var maxConcurrentOperations: Int {
        switch self {
        case .sequential: return 1
        case .concurrent: return 4
        case .adaptive: return max(2, ProcessInfo.processInfo.processorCount / 2)
        }
    }
    
    var batchSize: Int {
        switch self {
        case .sequential: return 10
        case .concurrent: return 50
        case .adaptive: return 30
        }
    }
}

// MARK: - 统一导入进度
struct UnifiedImportProgress {
    var currentFile: String = ""
    var processedCount: Int = 0
    var totalCount: Int = 0
    var progress: Double = 0.0
    var startTime: Date = Date()
    var concurrencyLevel: ConcurrencyLevel = .adaptive
    var errors: [MediaImportError] = []
    var completedBatches: Int = 0
    var totalBatches: Int = 0
    
    mutating func reset() {
        currentFile = ""
        processedCount = 0
        totalCount = 0
        progress = 0.0
        startTime = Date()
        errors.removeAll()
        completedBatches = 0
        totalBatches = 0
    }
}

// MARK: - 统一媒体服务
@MainActor
class UnifiedMediaService: ObservableObject {
    static let shared = UnifiedMediaService()
    
    // MARK: - Published Properties
    @Published var isImporting = false
    @Published var progress = UnifiedImportProgress()
    @Published var realFolderCount = 0
    
    // MARK: - Private Properties
    private let fileManager = FileManager.default
    private let logger = Logger(subsystem: "com.cop.app", category: "UnifiedMediaService")
    private let backgroundThumbnailService = BackgroundThumbnailService.shared
    private var importTask: Task<Void, Never>?
    private var importOperations: [UUID: Task<MediaFileInfo?, Never>] = [:]
    
    // MARK: - Directory Configuration
    private var mediaDirectory: URL {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        return documentsPath.appendingPathComponent("MediaFiles")
    }
    
    private var thumbnailDirectory: URL {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        return documentsPath.appendingPathComponent("Thumbnails")
    }
    
    private init() {
        createDirectoriesIfNeeded()
    }
    
    // MARK: - Directory Setup
    private func createDirectoriesIfNeeded() {
        try? fileManager.createDirectory(at: mediaDirectory, withIntermediateDirectories: true)
        try? fileManager.createDirectory(at: thumbnailDirectory, withIntermediateDirectories: true)
    }
    
    // MARK: - Main Import Method
    func importMediaFolder(
        from sourceURL: URL, 
        concurrency: ConcurrencyLevel = .adaptive
    ) async throws -> [MediaFileInfo] {
        logger.info("开始统一导入: \(sourceURL.path), 并发级别: \(concurrency.rawValue)")
        
        // 重置状态
        isImporting = true
        progress.reset()
        progress.concurrencyLevel = concurrency
        progress.startTime = Date()
        realFolderCount = 0
        
        defer {
            isImporting = false
            importOperations.removeAll()
            importTask = nil
        }
        
        // 获取安全访问权限
        guard sourceURL.startAccessingSecurityScopedResource() else {
            throw MediaImportError.accessDenied
        }
        defer { sourceURL.stopAccessingSecurityScopedResource() }
        
        // 扫描媒体文件
        let mediaFilesByFolder = try await scanMediaFilesByLeafFolders(in: sourceURL)
        realFolderCount = mediaFilesByFolder.count
        
        let totalFiles = mediaFilesByFolder.values.reduce(0) { $0 + $1.count }
        progress.totalCount = totalFiles
        
        logger.info("扫描到 \(totalFiles) 个媒体文件，分布在 \(self.realFolderCount) 个叶子文件夹中")
        
        // 根据并发级别选择导入策略
        let importedFiles: [MediaFileInfo]
        switch concurrency {
        case .sequential:
            importedFiles = try await performSequentialImport(mediaFilesByFolder)
        case .concurrent, .adaptive:
            importedFiles = try await performConcurrentImport(mediaFilesByFolder, concurrency: concurrency)
        }
        
        // 启动后台缩略图生成
        await startBackgroundThumbnailGeneration(for: importedFiles)
        
        // 保存导入历史
        await saveImportHistory(
            folderName: sourceURL.lastPathComponent,
            fileCount: importedFiles.count,
            totalSize: calculateTotalSize(importedFiles)
        )
        
        // 发送完成通知
        NotificationCenter.default.post(name: .mediaImportCompleted, object: nil)
        
        logger.info("统一导入完成: 成功 \(importedFiles.count) 个文件")
        return importedFiles
    }
    
    // MARK: - Sequential Import
    private func performSequentialImport(_ mediaFilesByFolder: [String: [URL]]) async throws -> [MediaFileInfo] {
        var importedFiles: [MediaFileInfo] = []
        
        for (folderName, mediaFiles) in mediaFilesByFolder {
            for fileURL in mediaFiles {
                try Task.checkCancellation()
                
                do {
                    progress.currentFile = fileURL.lastPathComponent
                    let mediaInfo = try await importSingleFile(fileURL, folderPath: folderName)
                    importedFiles.append(mediaInfo)
                    
                    progress.processedCount += 1
                    progress.progress = Double(progress.processedCount) / Double(progress.totalCount)
                    
                    // 小延迟以允许UI更新
                    try await Task.sleep(nanoseconds: 10_000_000)
                } catch {
                    logger.error("导入文件失败: \(fileURL.lastPathComponent), 错误: \(error.localizedDescription)")
                    progress.errors.append(MediaImportError.unsupportedFileType)
                }
            }
        }
        
        return importedFiles
    }
    
    // MARK: - Concurrent Import
    private func performConcurrentImport(
        _ mediaFilesByFolder: [String: [URL]], 
        concurrency: ConcurrencyLevel
    ) async throws -> [MediaFileInfo] {
        // 创建文件导入任务列表
        var filesToImport: [FileToImport] = []
        for (folderName, fileURLs) in mediaFilesByFolder {
            for fileURL in fileURLs {
                do {
                    let fileToImport = try FileToImport(sourceURL: fileURL, folderPath: folderName)
                    filesToImport.append(fileToImport)
                } catch {
                    logger.warning("跳过文件 \(fileURL.lastPathComponent): \(error.localizedDescription)")
                }
            }
        }
        
        // 创建批次
        let batches = createBatches(from: filesToImport, batchSize: concurrency.batchSize)
        progress.totalBatches = batches.count
        
        var importedFiles: [MediaFileInfo] = []
        
        // 并发处理批次
        for (batchIndex, batch) in batches.enumerated() {
            let batchResults = await withTaskGroup(of: MediaFileInfo?.self) { group in
                for file in batch.files {
                    group.addTask { [weak self] in
                        await self?.processSingleFileWithRetry(file)
                    }
                }
                
                var results: [MediaFileInfo] = []
                for await result in group {
                    if let result = result {
                        results.append(result)
                        await MainActor.run {
                            self.progress.processedCount += 1
                            self.progress.progress = Double(self.progress.processedCount) / Double(self.progress.totalCount)
                        }
                    }
                }
                return results
            }
            
            importedFiles.append(contentsOf: batchResults)
            progress.completedBatches = batchIndex + 1
            
            // 批次间小延迟
            try await Task.sleep(nanoseconds: 50_000_000) // 0.05秒
        }
        
        return importedFiles
    }
    
    // MARK: - Helper Methods
    private func processSingleFileWithRetry(_ file: FileToImport) async -> MediaFileInfo? {
        let maxRetries = 3
        var lastError: Error?
        
        for attempt in 1...maxRetries {
            do {
                return try await importSingleFile(file.sourceURL, folderPath: file.folderPath)
            } catch {
                lastError = error
                logger.warning("文件导入重试 \(attempt)/\(maxRetries): \(file.sourceURL.lastPathComponent)")
                
                if attempt < maxRetries {
                    try? await Task.sleep(nanoseconds: UInt64(attempt * 1_000_000_000)) // 递增延迟
                }
            }
        }
        
        if let error = lastError {
            await MainActor.run {
                progress.errors.append(MediaImportError.unsupportedFileType)
            }
        }
        
        return nil
    }
    
    private func importSingleFile(_ sourceURL: URL, folderPath: String) async throws -> MediaFileInfo {
        guard let mediaType = MediaFormatDetector.detectMediaType(from: sourceURL) else {
            throw MediaImportError.unsupportedFileType
        }
        
        let fileName = sourceURL.lastPathComponent
        
        // 创建目标文件夹
        let targetFolderURL = mediaDirectory.appendingPathComponent(folderPath)
        try fileManager.createDirectory(at: targetFolderURL, withIntermediateDirectories: true)
        
        // 复制文件到应用目录
        let targetFileURL = targetFolderURL.appendingPathComponent(fileName)
        
        // 处理文件名冲突
        var finalTargetURL = targetFileURL
        if fileManager.fileExists(atPath: targetFileURL.path) {
            let timestamp = Int(Date().timeIntervalSince1970)
            let nameWithoutExtension = targetFileURL.deletingPathExtension().lastPathComponent
            let fileExtension = targetFileURL.pathExtension
            let newFileName = "\(nameWithoutExtension)_\(timestamp).\(fileExtension)"
            finalTargetURL = targetFolderURL.appendingPathComponent(newFileName)
        }
        
        try fileManager.copyItem(at: sourceURL, to: finalTargetURL)
        
        // 生成稳定的ID
        let fileID = generateStableID(for: finalTargetURL)
        
        // 获取文件信息
        let resourceValues = try finalTargetURL.resourceValues(forKeys: [
            .fileSizeKey, .creationDateKey, .contentModificationDateKey
        ])
        
        let fileSize = Int64(resourceValues.fileSize ?? 0)
        let creationDate = resourceValues.creationDate ?? Date()
        let modificationDate = resourceValues.contentModificationDate ?? Date()
        
        return MediaFileInfo(
            id: fileID,
            name: fileName,
            type: mediaType,
            fileSize: fileSize,
            creationDate: creationDate,
            modificationDate: modificationDate,
            localURL: finalTargetURL,
            thumbnailURL: nil, // 后台生成
            folderPath: folderPath,
            duration: nil,
            dimensions: nil
        )
    }
    
    private func createBatches(from files: [FileToImport], batchSize: Int) -> [FileBatch] {
        var batches: [FileBatch] = []
        
        for i in stride(from: 0, to: files.count, by: batchSize) {
            let endIndex = min(i + batchSize, files.count)
            let batchFiles = Array(files[i..<endIndex])
            let batch = FileBatch(
                files: batchFiles,
                batchIndex: i / batchSize
            )
            batches.append(batch)
        }
        
        return batches
    }
    
    private func generateStableID(for fileURL: URL) -> UUID {
        let filePath = fileURL.path
        let data = Data(filePath.utf8)
        let hash = SHA256.hash(data: data)
        let hashString = hash.compactMap { String(format: "%02x", $0) }.joined()
        return UUID(uuidString: String(hashString.prefix(36).padding(toLength: 36, withPad: "0", startingAt: 0))) ?? UUID()
    }
    
    private func calculateTotalSize(_ files: [MediaFileInfo]) -> Int64 {
        return files.reduce(0) { $0 + $1.fileSize }
    }
    
    private func startBackgroundThumbnailGeneration(for files: [MediaFileInfo]) async {
        for file in files {
            await backgroundThumbnailService.enqueueThumbnailGeneration(for: file)
        }
    }
    
    private func saveImportHistory(folderName: String, fileCount: Int, totalSize: Int64) async {
        // 保存导入历史的实现
        logger.info("保存导入历史: \(folderName), 文件数: \(fileCount), 总大小: \(totalSize)")
    }
    
    // MARK: - File Scanning Methods
    private func scanMediaFilesByLeafFolders(in directory: URL) async throws -> [String: [URL]] {
        var mediaFilesByFolder: [String: [URL]] = [:]
        var allMediaFiles: [URL] = []
        
        // 递归扫描所有子目录
        try await scanDirectory(directory, into: &allMediaFiles)
        
        // 按照每个媒体文件所在的直接父文件夹分组
        for mediaFile in allMediaFiles {
            let parentFolder = mediaFile.deletingLastPathComponent()
            let folderName = parentFolder.lastPathComponent
            
            if mediaFilesByFolder[folderName] == nil {
                mediaFilesByFolder[folderName] = []
            }
            mediaFilesByFolder[folderName]?.append(mediaFile)
        }
        
        return mediaFilesByFolder
    }
    
    private func scanDirectory(_ directory: URL, into results: inout [URL]) async throws {
        let fileURLs = try fileManager.contentsOfDirectory(
            at: directory,
            includingPropertiesForKeys: [.isRegularFileKey, .isDirectoryKey],
            options: [.skipsHiddenFiles]
        )
        
        for fileURL in fileURLs {
            let resourceValues = try fileURL.resourceValues(forKeys: [.isRegularFileKey, .isDirectoryKey])
            
            if resourceValues.isDirectory == true {
                // 递归扫描子目录
                try await scanDirectory(fileURL, into: &results)
            } else if resourceValues.isRegularFile == true {
                // 检查是否为支持的媒体文件
                if let _ = MediaFormatDetector.detectMediaType(from: fileURL) {
                    results.append(fileURL)
                }
            }
        }
    }
    
    // MARK: - Legacy Compatibility Methods
    func getImportedFolders() async throws -> [FolderInfo] {
        // 为了兼容性，调用现有的MediaImportService方法
        return try await MediaImportService.shared.getImportedFolders()
    }
    
    func getImportedFoldersSync() throws -> [FolderInfo] {
        // 为了兼容性，调用现有的MediaImportService方法
        return try MediaImportService.shared.getImportedFoldersSync()
    }
}

// MARK: - Supporting Types (使用现有的ConcurrentMediaImportService中的定义)
// FileToImport和FileBatch已在ConcurrentMediaImportService.swift中全局定义，直接使用 