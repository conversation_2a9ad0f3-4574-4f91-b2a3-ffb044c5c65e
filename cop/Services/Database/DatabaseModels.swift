import Foundation
import UIKit
import SQLite3

// MARK: - 数据库文件夹模型
struct DatabaseFolder {
    let id: Int?
    let name: String
    let path: String
    let parentId: Int?
    let createdAt: Date
    let updatedAt: Date
    let mediaCount: Int
    let totalSize: Int64
    let fileCount: Int
    let lastModified: Date?
    let thumbnailPath: String?
    
    // 转换为FolderInfo
    func toFolderInfo() -> FolderInfo {
        return FolderInfo(
            name: name,
            path: path,
            mediaCount: mediaCount,
            fileCount: fileCount,
            totalSize: totalSize,
            lastModified: lastModified ?? updatedAt,
            thumbnail: thumbnailPath != nil ? URL(fileURLWithPath: thumbnailPath!) : nil
        )
    }
    
    // 从FolderInfo创建
    static func from(_ folderInfo: FolderInfo, parentId: Int? = nil) -> DatabaseFolder {
        return DatabaseFolder(
            id: nil,
            name: folderInfo.name,
            path: folderInfo.path,
            parentId: parentId,
            createdAt: Date(),
            updatedAt: Date(),
            mediaCount: folderInfo.mediaCount,
            totalSize: folderInfo.totalSize,
            fileCount: folderInfo.fileCount,
            lastModified: folderInfo.lastModified,
            thumbnailPath: folderInfo.thumbnail?.path
        )
    }
}

// MARK: - 数据库媒体文件模型
struct DatabaseMediaFile {
    let id: String
    let folderId: Int
    let name: String
    let filePath: String
    let fileType: String
    let fileSize: Int64
    let createdAt: Date
    let modifiedAt: Date
    let duration: Double?
    let width: Int?
    let height: Int?
    let thumbnailPath: String?
    let folderPath: String
    
    // 转换为MediaFileInfo
    func toMediaFileInfo() -> MediaFileInfo? {
        guard let mediaType = MediaType(rawValue: fileType),
              let uuid = UUID(uuidString: id) else {
            return nil
        }
        
        let dimensions: CGSize? = {
            if let width = width, let height = height {
                return CGSize(width: width, height: height)
            }
            return nil
        }()
        
        return MediaFileInfo(
            id: uuid,
            name: name,
            type: mediaType,
            fileSize: fileSize,
            creationDate: createdAt,
            modificationDate: modifiedAt,
            localURL: URL(fileURLWithPath: filePath),
            thumbnailURL: thumbnailPath != nil ? URL(fileURLWithPath: thumbnailPath!) : nil,
            folderPath: folderPath,
            duration: duration,
            dimensions: dimensions
        )
    }
    
    // 从MediaFileInfo创建
    static func from(_ mediaFileInfo: MediaFileInfo, folderId: Int) -> DatabaseMediaFile {
        return DatabaseMediaFile(
            id: mediaFileInfo.id.uuidString,
            folderId: folderId,
            name: mediaFileInfo.name,
            filePath: mediaFileInfo.localURL.path,
            fileType: mediaFileInfo.type.rawValue,
            fileSize: mediaFileInfo.fileSize,
            createdAt: mediaFileInfo.creationDate,
            modifiedAt: mediaFileInfo.modificationDate,
            duration: mediaFileInfo.duration,
            width: mediaFileInfo.dimensions != nil ? Int(mediaFileInfo.dimensions!.width) : nil,
            height: mediaFileInfo.dimensions != nil ? Int(mediaFileInfo.dimensions!.height) : nil,
            thumbnailPath: mediaFileInfo.thumbnailURL?.path,
            folderPath: mediaFileInfo.folderPath
        )
    }
}

// MARK: - 数据库缓存元数据模型
struct DatabaseCacheMetadata {
    let id: String
    let mediaFileId: String
    let cacheType: String
    let cachePath: String
    let createdAt: Date
    let accessedAt: Date
    let cacheSize: Int64
    
    // 缓存类型枚举
    enum CacheType: String, CaseIterable {
        case thumbnailSmall = "thumbnail_small"
        case thumbnailMedium = "thumbnail_medium"
        case thumbnailLarge = "thumbnail_large"
        case preview = "preview"
        case metadata = "metadata"
        
        var displayName: String {
            switch self {
            case .thumbnailSmall: return "小缩略图"
            case .thumbnailMedium: return "中缩略图"
            case .thumbnailLarge: return "大缩略图"
            case .preview: return "预览"
            case .metadata: return "元数据"
            }
        }
    }
}

// MARK: - 数据库查询构建器
class DatabaseQueryBuilder {
    private var query: String = ""
    private var parameters: [Any] = []
    
    // MARK: - SELECT 查询
    func select(_ columns: String = "*") -> DatabaseQueryBuilder {
        query = "SELECT \(columns)"
        return self
    }
    
    func from(_ table: String) -> DatabaseQueryBuilder {
        query += " FROM \(table)"
        return self
    }
    
    func join(_ table: String, on condition: String) -> DatabaseQueryBuilder {
        query += " JOIN \(table) ON \(condition)"
        return self
    }
    
    func leftJoin(_ table: String, on condition: String) -> DatabaseQueryBuilder {
        query += " LEFT JOIN \(table) ON \(condition)"
        return self
    }
    
    func `where`(_ condition: String, parameters: [Any] = []) -> DatabaseQueryBuilder {
        query += " WHERE \(condition)"
        self.parameters.append(contentsOf: parameters)
        return self
    }
    
    func and(_ condition: String, parameters: [Any] = []) -> DatabaseQueryBuilder {
        query += " AND \(condition)"
        self.parameters.append(contentsOf: parameters)
        return self
    }
    
    func or(_ condition: String, parameters: [Any] = []) -> DatabaseQueryBuilder {
        query += " OR \(condition)"
        self.parameters.append(contentsOf: parameters)
        return self
    }
    
    func orderBy(_ column: String, ascending: Bool = true) -> DatabaseQueryBuilder {
        query += " ORDER BY \(column) \(ascending ? "ASC" : "DESC")"
        return self
    }
    
    func limit(_ count: Int) -> DatabaseQueryBuilder {
        query += " LIMIT \(count)"
        return self
    }
    
    func offset(_ count: Int) -> DatabaseQueryBuilder {
        query += " OFFSET \(count)"
        return self
    }
    
    // MARK: - INSERT 查询
    func insertInto(_ table: String, columns: [String]) -> DatabaseQueryBuilder {
        let placeholders = Array(repeating: "?", count: columns.count).joined(separator: ", ")
        query = "INSERT INTO \(table) (\(columns.joined(separator: ", "))) VALUES (\(placeholders))"
        return self
    }
    
    func values(_ values: [Any]) -> DatabaseQueryBuilder {
        parameters.append(contentsOf: values)
        return self
    }
    
    // MARK: - UPDATE 查询
    func update(_ table: String) -> DatabaseQueryBuilder {
        query = "UPDATE \(table)"
        return self
    }
    
    func set(_ assignments: [String], parameters: [Any]) -> DatabaseQueryBuilder {
        query += " SET \(assignments.joined(separator: ", "))"
        self.parameters.append(contentsOf: parameters)
        return self
    }
    
    // MARK: - DELETE 查询
    func deleteFrom(_ table: String) -> DatabaseQueryBuilder {
        query = "DELETE FROM \(table)"
        return self
    }
    
    // MARK: - 构建结果
    func build() -> (sql: String, parameters: [Any]) {
        return (query, parameters)
    }
}

// MARK: - 数据库行解析器
class DatabaseRowParser {
    private let stmt: OpaquePointer
    
    init(_ stmt: OpaquePointer) {
        self.stmt = stmt
    }
    
    func getString(_ index: Int32) -> String? {
        if let cString = sqlite3_column_text(stmt, index) {
            return String(cString: cString)
        }
        return nil
    }
    
    func getInt(_ index: Int32) -> Int? {
        return Int(sqlite3_column_int64(stmt, index))
    }
    
    func getInt64(_ index: Int32) -> Int64? {
        return sqlite3_column_int64(stmt, index)
    }
    
    func getDouble(_ index: Int32) -> Double? {
        return sqlite3_column_double(stmt, index)
    }
    
    func getDate(_ index: Int32) -> Date? {
        if let dateString = getString(index) {
            let formatter = ISO8601DateFormatter()
            return formatter.date(from: dateString)
        }
        return nil
    }
    
    func getBool(_ index: Int32) -> Bool {
        return sqlite3_column_int(stmt, index) != 0
    }
    
    func isNull(_ index: Int32) -> Bool {
        return sqlite3_column_type(stmt, index) == SQLITE_NULL
    }
}

// MARK: - 数据库映射器
struct DatabaseMappers {
    // 映射文件夹
    static func mapFolder(_ stmt: OpaquePointer) -> DatabaseFolder? {
        let parser = DatabaseRowParser(stmt)
        
        guard let name = parser.getString(1),
              let path = parser.getString(2) else {
            return nil
        }
        
        return DatabaseFolder(
            id: parser.getInt(0),
            name: name,
            path: path,
            parentId: parser.isNull(3) ? nil : parser.getInt(3),
            createdAt: parser.getDate(4) ?? Date(),
            updatedAt: parser.getDate(5) ?? Date(),
            mediaCount: parser.getInt(6) ?? 0,
            totalSize: parser.getInt64(7) ?? 0,
            fileCount: parser.getInt(8) ?? 0,
            lastModified: parser.getDate(9),
            thumbnailPath: parser.getString(10)
        )
    }
    
    // 映射媒体文件
    static func mapMediaFile(_ stmt: OpaquePointer) -> DatabaseMediaFile? {
        let parser = DatabaseRowParser(stmt)
        
        guard let id = parser.getString(0),
              let folderId = parser.getInt(1),
              let name = parser.getString(2),
              let filePath = parser.getString(3),
              let fileType = parser.getString(4),
              let fileSize = parser.getInt64(5),
              let createdAt = parser.getDate(6),
              let modifiedAt = parser.getDate(7),
              let folderPath = parser.getString(12) else {
            return nil
        }
        
        return DatabaseMediaFile(
            id: id,
            folderId: folderId,
            name: name,
            filePath: filePath,
            fileType: fileType,
            fileSize: fileSize,
            createdAt: createdAt,
            modifiedAt: modifiedAt,
            duration: parser.isNull(8) ? nil : parser.getDouble(8),
            width: parser.isNull(9) ? nil : parser.getInt(9),
            height: parser.isNull(10) ? nil : parser.getInt(10),
            thumbnailPath: parser.getString(11),
            folderPath: folderPath
        )
    }
    
    // 映射缓存元数据
    static func mapCacheMetadata(_ stmt: OpaquePointer) -> DatabaseCacheMetadata? {
        let parser = DatabaseRowParser(stmt)
        
        guard let id = parser.getString(0),
              let mediaFileId = parser.getString(1),
              let cacheType = parser.getString(2),
              let cachePath = parser.getString(3),
              let createdAt = parser.getDate(4),
              let accessedAt = parser.getDate(5),
              let cacheSize = parser.getInt64(6) else {
            return nil
        }
        
        return DatabaseCacheMetadata(
            id: id,
            mediaFileId: mediaFileId,
            cacheType: cacheType,
            cachePath: cachePath,
            createdAt: createdAt,
            accessedAt: accessedAt,
            cacheSize: cacheSize
        )
    }
} 