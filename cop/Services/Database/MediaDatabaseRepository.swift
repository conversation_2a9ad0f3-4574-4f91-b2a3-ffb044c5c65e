import Foundation
import UIKit
import SQLite3

// MARK: - 媒体数据库仓库协议
protocol MediaDatabaseRepositoryProtocol {
    // 文件夹操作
    func insertFolder(_ folder: DatabaseFolder) async throws -> Int
    func updateFolder(_ folder: DatabaseFolder) async throws
    func deleteFolder(id: Int) async throws
    func getFolder(id: Int) async throws -> DatabaseFolder?
    func getAllFolders() async throws -> [DatabaseFolder]
    func getFoldersByName(_ name: String) async throws -> [DatabaseFolder]
    
    // 媒体文件操作
    func insertMediaFile(_ mediaFile: DatabaseMediaFile) async throws
    func updateMediaFile(_ mediaFile: DatabaseMediaFile) async throws
    func deleteMediaFile(id: String) async throws
    func getMediaFile(id: String) async throws -> DatabaseMediaFile?
    func getMediaFiles(in folderId: Int, limit: Int?, offset: Int?) async throws -> [DatabaseMediaFile]
    func getAllMediaFiles(limit: Int?, offset: Int?) async throws -> [DatabaseMediaFile]
    func searchMediaFiles(query: String, type: MediaType?, limit: Int?, offset: Int?) async throws -> [DatabaseMediaFile]
    
    // 批量操作
    func insertMediaFiles(_ mediaFiles: [DatabaseMediaFile]) async throws
    func deleteMediaFiles(ids: [String]) async throws
    func updateFolderStatistics(id: Int) async throws
    
    // 统计操作
    func getMediaFileCount(in folderId: Int?) async throws -> Int
    func getTotalFileSize(in folderId: Int?) async throws -> Int64
    func getFolderCount() async throws -> Int
}

// MARK: - 媒体数据库仓库实现
@MainActor
class MediaDatabaseRepository: MediaDatabaseRepositoryProtocol {
    private let dbManager = DatabaseManager.shared
    
    // MARK: - 文件夹操作
    func insertFolder(_ folder: DatabaseFolder) async throws -> Int {
        let sql = """
            INSERT INTO folders (name, path, parent_id, created_at, updated_at, media_count, total_size, file_count, last_modified, thumbnail_path)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        let formatter = ISO8601DateFormatter()
        let parameters: [Any] = [
            folder.name,
            folder.path,
            folder.parentId as Any,
            formatter.string(from: folder.createdAt),
            formatter.string(from: folder.updatedAt),
            folder.mediaCount,
            folder.totalSize,
            folder.fileCount,
            folder.lastModified != nil ? formatter.string(from: folder.lastModified!) : NSNull(),
            folder.thumbnailPath as Any
        ]
        
        try await dbManager.executeSQL(sql, parameters: parameters)
        
        // 获取插入的ID
        let lastInsertRowId = "SELECT last_insert_rowid()"
        let result = try await dbManager.query(lastInsertRowId) { stmt in
            return sqlite3_column_int64(stmt, 0)
        }
        
        return Int(result.first ?? 0)
    }
    
    func updateFolder(_ folder: DatabaseFolder) async throws {
        guard let id = folder.id else {
            throw DatabaseError.bind("文件夹ID不能为空")
        }
        
        let sql = """
            UPDATE folders SET 
                name = ?, path = ?, parent_id = ?, updated_at = ?, 
                media_count = ?, total_size = ?, file_count = ?, 
                last_modified = ?, thumbnail_path = ?
            WHERE id = ?
        """
        
        let formatter = ISO8601DateFormatter()
        let parameters: [Any] = [
            folder.name,
            folder.path,
            folder.parentId as Any,
            formatter.string(from: folder.updatedAt),
            folder.mediaCount,
            folder.totalSize,
            folder.fileCount,
            folder.lastModified != nil ? formatter.string(from: folder.lastModified!) : NSNull(),
            folder.thumbnailPath as Any,
            id
        ]
        
        try await dbManager.executeSQL(sql, parameters: parameters)
    }
    
    func deleteFolder(id: Int) async throws {
        // 先删除相关的媒体文件
        try await dbManager.executeSQL("DELETE FROM media_files WHERE folder_id = ?", parameters: [id])
        
        // 再删除文件夹
        try await dbManager.executeSQL("DELETE FROM folders WHERE id = ?", parameters: [id])
    }
    
    func getFolder(id: Int) async throws -> DatabaseFolder? {
        let sql = "SELECT * FROM folders WHERE id = ?"
        let results = try await dbManager.query(sql, parameters: [id], mapper: DatabaseMappers.mapFolder)
        return results.first
    }
    
    func getAllFolders() async throws -> [DatabaseFolder] {
        let sql = "SELECT * FROM folders ORDER BY updated_at DESC"
        return try await dbManager.query(sql, mapper: DatabaseMappers.mapFolder)
    }
    
    func getFoldersByName(_ name: String) async throws -> [DatabaseFolder] {
        let sql = "SELECT * FROM folders WHERE name LIKE ? ORDER BY name"
        return try await dbManager.query(sql, parameters: ["%\(name)%"], mapper: DatabaseMappers.mapFolder)
    }
    
    // MARK: - 媒体文件操作
    func insertMediaFile(_ mediaFile: DatabaseMediaFile) async throws {
        let sql = """
            INSERT OR REPLACE INTO media_files 
            (id, folder_id, name, file_path, file_type, file_size, created_at, modified_at, duration, width, height, thumbnail_path, folder_path)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        let formatter = ISO8601DateFormatter()
        let parameters: [Any] = [
            mediaFile.id,
            mediaFile.folderId,
            mediaFile.name,
            mediaFile.filePath,
            mediaFile.fileType,
            mediaFile.fileSize,
            formatter.string(from: mediaFile.createdAt),
            formatter.string(from: mediaFile.modifiedAt),
            mediaFile.duration as Any,
            mediaFile.width as Any,
            mediaFile.height as Any,
            mediaFile.thumbnailPath as Any,
            mediaFile.folderPath
        ]
        
        try await dbManager.executeSQL(sql, parameters: parameters)
    }
    
    func updateMediaFile(_ mediaFile: DatabaseMediaFile) async throws {
        let sql = """
            UPDATE media_files SET 
                folder_id = ?, name = ?, file_path = ?, file_type = ?, file_size = ?, 
                created_at = ?, modified_at = ?, duration = ?, width = ?, height = ?, 
                thumbnail_path = ?, folder_path = ?
            WHERE id = ?
        """
        
        let formatter = ISO8601DateFormatter()
        let parameters: [Any] = [
            mediaFile.folderId,
            mediaFile.name,
            mediaFile.filePath,
            mediaFile.fileType,
            mediaFile.fileSize,
            formatter.string(from: mediaFile.createdAt),
            formatter.string(from: mediaFile.modifiedAt),
            mediaFile.duration as Any,
            mediaFile.width as Any,
            mediaFile.height as Any,
            mediaFile.thumbnailPath as Any,
            mediaFile.folderPath,
            mediaFile.id
        ]
        
        try await dbManager.executeSQL(sql, parameters: parameters)
    }
    
    func deleteMediaFile(id: String) async throws {
        try await dbManager.executeSQL("DELETE FROM media_files WHERE id = ?", parameters: [id])
    }
    
    func getMediaFile(id: String) async throws -> DatabaseMediaFile? {
        let sql = "SELECT * FROM media_files WHERE id = ?"
        let results = try await dbManager.query(sql, parameters: [id], mapper: DatabaseMappers.mapMediaFile)
        return results.first
    }
    
    func getMediaFiles(in folderId: Int, limit: Int? = nil, offset: Int? = nil) async throws -> [DatabaseMediaFile] {
        var sql = "SELECT * FROM media_files WHERE folder_id = ? ORDER BY created_at DESC"
        var parameters: [Any] = [folderId]
        
        if let limit = limit {
            sql += " LIMIT ?"
            parameters.append(limit)
            
            if let offset = offset {
                sql += " OFFSET ?"
                parameters.append(offset)
            }
        }
        
        return try await dbManager.query(sql, parameters: parameters, mapper: DatabaseMappers.mapMediaFile)
    }
    
    func getAllMediaFiles(limit: Int? = nil, offset: Int? = nil) async throws -> [DatabaseMediaFile] {
        var sql = "SELECT * FROM media_files ORDER BY created_at DESC"
        var parameters: [Any] = []
        
        if let limit = limit {
            sql += " LIMIT ?"
            parameters.append(limit)
            
            if let offset = offset {
                sql += " OFFSET ?"
                parameters.append(offset)
            }
        }
        
        return try await dbManager.query(sql, parameters: parameters, mapper: DatabaseMappers.mapMediaFile)
    }
    
    func searchMediaFiles(query: String, type: MediaType? = nil, limit: Int? = nil, offset: Int? = nil) async throws -> [DatabaseMediaFile] {
        var sql = "SELECT * FROM media_files WHERE name LIKE ?"
        var parameters: [Any] = ["%\(query)%"]
        
        if let type = type {
            sql += " AND file_type = ?"
            parameters.append(type.rawValue)
        }
        
        sql += " ORDER BY created_at DESC"
        
        if let limit = limit {
            sql += " LIMIT ?"
            parameters.append(limit)
            
            if let offset = offset {
                sql += " OFFSET ?"
                parameters.append(offset)
            }
        }
        
        return try await dbManager.query(sql, parameters: parameters, mapper: DatabaseMappers.mapMediaFile)
    }
    
    // MARK: - 批量操作
    func insertMediaFiles(_ mediaFiles: [DatabaseMediaFile]) async throws {
        try await dbManager.performAsyncTransaction {
            for mediaFile in mediaFiles {
                try await self.insertMediaFile(mediaFile)
            }
        }
    }
    
    func deleteMediaFiles(ids: [String]) async throws {
        guard !ids.isEmpty else { return }
        
        let placeholders = Array(repeating: "?", count: ids.count).joined(separator: ", ")
        let sql = "DELETE FROM media_files WHERE id IN (\(placeholders))"
        
        try await dbManager.executeSQL(sql, parameters: ids)
    }
    
    func updateFolderStatistics(id: Int) async throws {
        // 计算媒体文件数量
        let countSQL = "SELECT COUNT(*) FROM media_files WHERE folder_id = ?"
        let mediaCount = try await dbManager.query(countSQL, parameters: [id]) { stmt in
            return sqlite3_column_int64(stmt, 0)
        }.first ?? 0
        
        // 计算总文件大小
        let sizeSQL = "SELECT COALESCE(SUM(file_size), 0) FROM media_files WHERE folder_id = ?"
        let totalSize = try await dbManager.query(sizeSQL, parameters: [id]) { stmt in
            return sqlite3_column_int64(stmt, 0)
        }.first ?? 0
        
        // 更新文件夹统计信息
        let updateSQL = """
            UPDATE folders SET 
                media_count = ?, 
                total_size = ?, 
                updated_at = ?
            WHERE id = ?
        """
        
        let formatter = ISO8601DateFormatter()
        let parameters: [Any] = [
            Int(mediaCount),
            totalSize,
            formatter.string(from: Date()),
            id
        ]
        
        try await dbManager.executeSQL(updateSQL, parameters: parameters)
    }
    
    // MARK: - 统计操作
    func getMediaFileCount(in folderId: Int? = nil) async throws -> Int {
        let sql: String
        let parameters: [Any]
        
        if let folderId = folderId {
            sql = "SELECT COUNT(*) FROM media_files WHERE folder_id = ?"
            parameters = [folderId]
        } else {
            sql = "SELECT COUNT(*) FROM media_files"
            parameters = []
        }
        
        let result = try await dbManager.query(sql, parameters: parameters) { stmt in
            return sqlite3_column_int64(stmt, 0)
        }.first ?? 0
        
        return Int(result)
    }
    
    func getTotalFileSize(in folderId: Int? = nil) async throws -> Int64 {
        let sql: String
        let parameters: [Any]
        
        if let folderId = folderId {
            sql = "SELECT COALESCE(SUM(file_size), 0) FROM media_files WHERE folder_id = ?"
            parameters = [folderId]
        } else {
            sql = "SELECT COALESCE(SUM(file_size), 0) FROM media_files"
            parameters = []
        }
        
        let result = try await dbManager.query(sql, parameters: parameters) { stmt in
            return sqlite3_column_int64(stmt, 0)
        }.first ?? 0
        
        return result
    }
    
    func getFolderCount() async throws -> Int {
        let sql = "SELECT COUNT(*) FROM folders"
        let result = try await dbManager.query(sql) { stmt in
            return sqlite3_column_int64(stmt, 0)
        }.first ?? 0
        
        return Int(result)
    }
}

// MARK: - 高级查询扩展
extension MediaDatabaseRepository {
    // 分页查询媒体文件
    func getMediaFilesPaginated(
        folderId: Int? = nil,
        searchQuery: String? = nil,
        mediaType: MediaType? = nil,
        sortBy: MediaSortOption = .dateCreated,
        ascending: Bool = false,
        pageSize: Int = 50,
        page: Int = 0
    ) async throws -> (files: [DatabaseMediaFile], totalCount: Int) {
        
        // 构建查询条件
        var whereConditions: [String] = []
        var parameters: [Any] = []
        
        if let folderId = folderId {
            whereConditions.append("folder_id = ?")
            parameters.append(folderId)
        }
        
        if let searchQuery = searchQuery, !searchQuery.isEmpty {
            whereConditions.append("name LIKE ?")
            parameters.append("%\(searchQuery)%")
        }
        
        if let mediaType = mediaType {
            whereConditions.append("file_type = ?")
            parameters.append(mediaType.rawValue)
        }
        
        let whereClause = whereConditions.isEmpty ? "" : "WHERE " + whereConditions.joined(separator: " AND ")
        
        // 排序字段
        let orderColumn: String
        switch sortBy {
        case .name: orderColumn = "name"
        case .dateCreated: orderColumn = "created_at"
        case .dateModified: orderColumn = "modified_at"
        case .fileSize: orderColumn = "file_size"
        case .type: orderColumn = "file_type"
        }
        
        let orderDirection = ascending ? "ASC" : "DESC"
        let orderClause = "ORDER BY \(orderColumn) \(orderDirection)"
        
        // 获取总数
        let countSQL = "SELECT COUNT(*) FROM media_files \(whereClause)"
        let totalCount = try await dbManager.query(countSQL, parameters: parameters) { stmt in
            return Int(sqlite3_column_int64(stmt, 0))
        }.first ?? 0
        
        // 获取分页数据
        let offset = page * pageSize
        let dataSQL = "SELECT * FROM media_files \(whereClause) \(orderClause) LIMIT ? OFFSET ?"
        let dataParameters = parameters + [pageSize, offset]
        
        let files = try await dbManager.query(dataSQL, parameters: dataParameters, mapper: DatabaseMappers.mapMediaFile)
        
        return (files: files, totalCount: totalCount)
    }
    
    // 获取文件夹内媒体文件统计
    func getFolderMediaStatistics(folderId: Int) async throws -> (imageCount: Int, videoCount: Int, totalSize: Int64) {
        let sql = """
            SELECT 
                file_type,
                COUNT(*) as count,
                COALESCE(SUM(file_size), 0) as total_size
            FROM media_files 
            WHERE folder_id = ? 
            GROUP BY file_type
        """
        
        let results = try await dbManager.query(sql, parameters: [folderId]) { stmt in
            let parser = DatabaseRowParser(stmt)
            return (
                type: parser.getString(0) ?? "",
                count: parser.getInt(1) ?? 0,
                size: parser.getInt64(2) ?? 0
            )
        }
        
        var imageCount = 0
        var videoCount = 0
        var totalSize: Int64 = 0
        
        for result in results {
            totalSize += result.size
            
            if result.type == MediaType.image.rawValue {
                imageCount = result.count
            } else if result.type == MediaType.video.rawValue {
                videoCount = result.count
            }
        }
        
        return (imageCount: imageCount, videoCount: videoCount, totalSize: totalSize)
    }
    
    // 获取最近添加的媒体文件
    func getRecentMediaFiles(limit: Int = 20) async throws -> [DatabaseMediaFile] {
        let sql = "SELECT * FROM media_files ORDER BY created_at DESC LIMIT ?"
        return try await dbManager.query(sql, parameters: [limit], mapper: DatabaseMappers.mapMediaFile)
    }
    
    // 获取大文件列表
    func getLargeMediaFiles(minSize: Int64, limit: Int = 20) async throws -> [DatabaseMediaFile] {
        let sql = "SELECT * FROM media_files WHERE file_size >= ? ORDER BY file_size DESC LIMIT ?"
        return try await dbManager.query(sql, parameters: [minSize, limit], mapper: DatabaseMappers.mapMediaFile)
    }
} 