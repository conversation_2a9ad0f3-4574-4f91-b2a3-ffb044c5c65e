import Foundation
import UIKit

// MARK: - 缓存策略枚举
enum CacheStrategy {
    case memoryOnly
    case diskOnly
    case memoryAndDisk
    case database
    case all
}

// MARK: - 缓存项目类型
enum CacheItemType: String, CaseIterable {
    case thumbnail = "thumbnail"
    case metadata = "metadata"
    case preview = "preview"
    
    var directory: String {
        switch self {
        case .thumbnail: return "thumbnails"
        case .metadata: return "metadata"
        case .preview: return "previews"
        }
    }
}

// MARK: - 缓存大小枚举
enum CacheSize: String, CaseIterable {
    case small = "small"
    case medium = "medium"
    case large = "large"
    
    var thumbnailSize: CGSize {
        switch self {
        case .small: return CGSize(width: 150, height: 150)
        case .medium: return CGSize(width: 300, height: 300)
        case .large: return CGSize(width: 600, height: 600)
        }
    }
    
    var directory: String {
        return rawValue
    }
}

// MARK: - 缓存配置
struct CacheConfiguration {
    let maxMemoryUsage: Int64 // 最大内存使用量（字节）
    let maxDiskUsage: Int64 // 最大磁盘使用量（字节）
    let maxItemCount: Int // 最大缓存项目数量
    let expireDuration: TimeInterval // 缓存过期时间
    let cleanupInterval: TimeInterval // 清理间隔
    
    static let `default` = CacheConfiguration(
        maxMemoryUsage: 100 * 1024 * 1024, // 100MB
        maxDiskUsage: 500 * 1024 * 1024,   // 500MB
        maxItemCount: 1000,
        expireDuration: 7 * 24 * 60 * 60,  // 7天
        cleanupInterval: 5 * 60            // 5分钟
    )
}

// MARK: - 媒体元数据缓存管理器
@MainActor
class MediaMetadataCache {
    static let shared = MediaMetadataCache()
    
    private let memoryCache = NSCache<NSString, CacheItem>()
    private let diskCacheURL: URL
    private let repository = MediaDatabaseRepository()
    private let configuration: CacheConfiguration
    private var cleanupTimer: Timer?
    
    // MARK: - 缓存项目
    private class CacheItem {
        let data: Data
        let accessedAt: Date
        let size: Int64
        let itemType: CacheItemType
        
        init(data: Data, itemType: CacheItemType) {
            self.data = data
            self.accessedAt = Date()
            self.size = Int64(data.count)
            self.itemType = itemType
        }
    }
    
    // MARK: - 初始化
    private init(configuration: CacheConfiguration = .default) {
        self.configuration = configuration
        
        // 设置缓存目录
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first!
        self.diskCacheURL = URL(fileURLWithPath: documentsPath).appendingPathComponent("Cache")
        
        setupMemoryCache()
        setupDiskCache()
        startCleanupTimer()
    }
    
    deinit {
        cleanupTimer?.invalidate()
    }
    
    // MARK: - 设置内存缓存
    private func setupMemoryCache() {
        memoryCache.totalCostLimit = Int(configuration.maxMemoryUsage)
        memoryCache.countLimit = configuration.maxItemCount
        
        // 监听内存警告
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.clearMemoryCache()
            }
        }
    }
    
    // MARK: - 设置磁盘缓存
    private func setupDiskCache() {
        // 创建缓存目录结构
        for itemType in CacheItemType.allCases {
            let typeURL = diskCacheURL.appendingPathComponent(itemType.directory)
            
            for size in CacheSize.allCases {
                let sizeURL = typeURL.appendingPathComponent(size.directory)
                try? FileManager.default.createDirectory(
                    at: sizeURL,
                    withIntermediateDirectories: true
                )
            }
        }
    }
    
    // MARK: - 启动清理定时器
    private func startCleanupTimer() {
        cleanupTimer = Timer.scheduledTimer(withTimeInterval: configuration.cleanupInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.performMaintenanceCleanup()
            }
        }
    }
    
    // MARK: - 缓存操作
    func cacheData(
        _ data: Data,
        for mediaFileId: UUID,
        type: CacheItemType,
        size: CacheSize = .medium,
        strategy: CacheStrategy = .all
    ) async throws {
        let cacheKey = buildCacheKey(mediaFileId: mediaFileId, type: type, size: size)
        let item = CacheItem(data: data, itemType: type)
        
        // 内存缓存
        if strategy == .memoryOnly || strategy == .memoryAndDisk || strategy == .all {
            memoryCache.setObject(item, forKey: cacheKey as NSString, cost: Int(item.size))
        }
        
        // 磁盘缓存
        if strategy == .diskOnly || strategy == .memoryAndDisk || strategy == .all {
            let diskURL = buildDiskCacheURL(mediaFileId: mediaFileId, type: type, size: size)
            try data.write(to: diskURL)
        }
        
        // 数据库缓存
        if strategy == .database || strategy == .all {
            let cacheMetadata = DatabaseCacheMetadata(
                id: UUID().uuidString,
                mediaFileId: mediaFileId.uuidString,
                cacheType: "\(type.rawValue)_\(size.rawValue)",
                cachePath: buildDiskCacheURL(mediaFileId: mediaFileId, type: type, size: size).path,
                createdAt: Date(),
                accessedAt: Date(),
                cacheSize: Int64(data.count)
            )
            
            try await insertCacheMetadata(cacheMetadata)
        }
    }
    
    func getCachedData(
        for mediaFileId: UUID,
        type: CacheItemType,
        size: CacheSize = .medium,
        strategy: CacheStrategy = .all
    ) async -> Data? {
        let cacheKey = buildCacheKey(mediaFileId: mediaFileId, type: type, size: size)
        
        // 先检查内存缓存
        if strategy == .memoryOnly || strategy == .memoryAndDisk || strategy == .all {
            if let item = memoryCache.object(forKey: cacheKey as NSString) {
                return item.data
            }
        }
        
        // 检查磁盘缓存
        if strategy == .diskOnly || strategy == .memoryAndDisk || strategy == .all {
            let diskURL = buildDiskCacheURL(mediaFileId: mediaFileId, type: type, size: size)
            if let data = try? Data(contentsOf: diskURL) {
                // 加载到内存缓存
                if strategy != .diskOnly {
                    let item = CacheItem(data: data, itemType: type)
                    memoryCache.setObject(item, forKey: cacheKey as NSString, cost: Int(item.size))
                }
                
                // 更新访问时间
                if strategy == .database || strategy == .all {
                    await updateCacheAccessTime(mediaFileId: mediaFileId, type: type, size: size)
                }
                
                return data
            }
        }
        
        return nil
    }
    
    func removeCachedData(
        for mediaFileId: UUID,
        type: CacheItemType? = nil,
        size: CacheSize? = nil
    ) async throws {
        if let type = type, let size = size {
            // 删除特定缓存
            let cacheKey = buildCacheKey(mediaFileId: mediaFileId, type: type, size: size)
            memoryCache.removeObject(forKey: cacheKey as NSString)
            
            let diskURL = buildDiskCacheURL(mediaFileId: mediaFileId, type: type, size: size)
            try? FileManager.default.removeItem(at: diskURL)
            
            try await deleteCacheMetadata(mediaFileId: mediaFileId, type: type, size: size)
        } else {
            // 删除该媒体文件的所有缓存
            for cacheType in CacheItemType.allCases {
                for cacheSize in CacheSize.allCases {
                    try await removeCachedData(for: mediaFileId, type: cacheType, size: cacheSize)
                }
            }
        }
    }
    
    // MARK: - 缓存图片相关方法
    func cacheImage(
        _ image: UIImage,
        for mediaFileId: UUID,
        size: CacheSize = .medium,
        quality: CGFloat = 0.8
    ) async throws {
        guard let imageData = image.jpegData(compressionQuality: quality) else {
            throw NSError(domain: "MediaMetadataCache", code: -1, userInfo: [NSLocalizedDescriptionKey: "图片转换失败"])
        }
        
        try await cacheData(imageData, for: mediaFileId, type: .thumbnail, size: size)
    }
    
    func getCachedImage(
        for mediaFileId: UUID,
        size: CacheSize = .medium
    ) async -> UIImage? {
        guard let data = await getCachedData(for: mediaFileId, type: .thumbnail, size: size) else {
            return nil
        }
        
        return UIImage(data: data)
    }
    
    // MARK: - 缓存元数据相关方法
    func cacheMetadata<T: Codable>(
        _ metadata: T,
        for mediaFileId: UUID,
        key: String
    ) async throws {
        let encoder = JSONEncoder()
        let data = try encoder.encode(metadata)
        
        try await cacheData(data, for: mediaFileId, type: .metadata, size: .small)
    }
    
    func getCachedMetadata<T: Codable>(
        for mediaFileId: UUID,
        key: String,
        type: T.Type
    ) async -> T? {
        guard let data = await getCachedData(for: mediaFileId, type: .metadata, size: .small) else {
            return nil
        }
        
        let decoder = JSONDecoder()
        return try? decoder.decode(type, from: data)
    }
    
    // MARK: - 维护操作
    func clearMemoryCache() {
        memoryCache.removeAllObjects()
    }
    
    func clearDiskCache() async throws {
        try FileManager.default.removeItem(at: diskCacheURL)
        setupDiskCache()
        
        // 清理数据库中的缓存记录
        try await clearAllCacheMetadata()
    }
    
    func clearAllCache() async throws {
        clearMemoryCache()
        try await clearDiskCache()
    }
    
    private func performMaintenanceCleanup() async {
        await cleanupExpiredItems()
        await cleanupOversizedCache()
    }
    
    private func cleanupExpiredItems() async {
        let expireDate = Date().addingTimeInterval(-configuration.expireDuration)
        
        do {
            // 获取过期的缓存项
            let expiredItems = try await getExpiredCacheItems(before: expireDate)
            
            for item in expiredItems {
                if let mediaFileId = UUID(uuidString: item.mediaFileId) {
                    let parts = item.cacheType.split(separator: "_")
                    if parts.count == 2,
                       let type = CacheItemType(rawValue: String(parts[0])),
                       let size = CacheSize(rawValue: String(parts[1])) {
                        try await removeCachedData(for: mediaFileId, type: type, size: size)
                    }
                }
            }
        } catch {
            print("清理过期缓存失败: \(error)")
        }
    }
    
    private func cleanupOversizedCache() async {
        do {
            let currentSize = await getCurrentDiskCacheSize()
            
            if currentSize > configuration.maxDiskUsage {
                // 删除最旧的缓存项直到大小合适
                let targetSize = configuration.maxDiskUsage * 8 / 10 // 减少到80%
                let reduceSize = currentSize - targetSize
                
                try await reduceOldestCacheItems(by: reduceSize)
            }
        } catch {
            print("清理超大缓存失败: \(error)")
        }
    }
    
    // MARK: - 统计信息
    func getCacheStatistics() async -> (memoryUsage: Int64, diskUsage: Int64, itemCount: Int) {
        // NSCache 没有直接的方法获取当前总成本，我们需要估算
        let memoryUsage = Int64(memoryCache.totalCostLimit)  // 使用总成本限制作为估算
        let diskUsage = await getCurrentDiskCacheSize()
        let itemCount = memoryCache.countLimit
        
        return (memoryUsage: memoryUsage, diskUsage: diskUsage, itemCount: itemCount)
    }
    
    // MARK: - 私有辅助方法
    private func buildCacheKey(mediaFileId: UUID, type: CacheItemType, size: CacheSize) -> String {
        return "\(mediaFileId.uuidString)_\(type.rawValue)_\(size.rawValue)"
    }
    
    private func buildDiskCacheURL(mediaFileId: UUID, type: CacheItemType, size: CacheSize) -> URL {
        return diskCacheURL
            .appendingPathComponent(type.directory)
            .appendingPathComponent(size.directory)
            .appendingPathComponent("\(mediaFileId.uuidString).cache")
    }
    
    private func getCurrentDiskCacheSize() async -> Int64 {
        var totalSize: Int64 = 0
        
        let enumerator = FileManager.default.enumerator(
            at: diskCacheURL,
            includingPropertiesForKeys: [.fileSizeKey],
            options: [.skipsHiddenFiles]
        )
        
        while let fileURL = enumerator?.nextObject() as? URL {
            do {
                let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                if let fileSize = resourceValues.fileSize {
                    totalSize += Int64(fileSize)
                }
            } catch {
                // 忽略读取错误
            }
        }
        
        return totalSize
    }
    
    // MARK: - 数据库操作
    private func insertCacheMetadata(_ metadata: DatabaseCacheMetadata) async throws {
        let sql = """
            INSERT OR REPLACE INTO cache_metadata 
            (id, media_file_id, cache_type, cache_path, created_at, accessed_at, cache_size)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        
        let formatter = ISO8601DateFormatter()
        let parameters: [Any] = [
            metadata.id,
            metadata.mediaFileId,
            metadata.cacheType,
            metadata.cachePath,
            formatter.string(from: metadata.createdAt),
            formatter.string(from: metadata.accessedAt),
            metadata.cacheSize
        ]
        
        try await DatabaseManager.shared.executeSQL(sql, parameters: parameters)
    }
    
    private func updateCacheAccessTime(mediaFileId: UUID, type: CacheItemType, size: CacheSize) async {
        let sql = """
            UPDATE cache_metadata 
            SET accessed_at = ? 
            WHERE media_file_id = ? AND cache_type = ?
        """
        
        let formatter = ISO8601DateFormatter()
        let cacheType = "\(type.rawValue)_\(size.rawValue)"
        let parameters: [Any] = [
            formatter.string(from: Date()),
            mediaFileId.uuidString,
            cacheType
        ]
        
        try? await DatabaseManager.shared.executeSQL(sql, parameters: parameters)
    }
    
    private func deleteCacheMetadata(mediaFileId: UUID, type: CacheItemType, size: CacheSize) async throws {
        let sql = "DELETE FROM cache_metadata WHERE media_file_id = ? AND cache_type = ?"
        let cacheType = "\(type.rawValue)_\(size.rawValue)"
        let parameters: [Any] = [mediaFileId.uuidString, cacheType]
        
        try await DatabaseManager.shared.executeSQL(sql, parameters: parameters)
    }
    
    private func clearAllCacheMetadata() async throws {
        try await DatabaseManager.shared.executeSQL("DELETE FROM cache_metadata")
    }
    
    private func getExpiredCacheItems(before date: Date) async throws -> [DatabaseCacheMetadata] {
        let sql = "SELECT * FROM cache_metadata WHERE accessed_at < ?"
        let formatter = ISO8601DateFormatter()
        
        return try await DatabaseManager.shared.query(
            sql,
            parameters: [formatter.string(from: date)],
            mapper: DatabaseMappers.mapCacheMetadata
        )
    }
    
    private func reduceOldestCacheItems(by targetSize: Int64) async throws {
        let sql = "SELECT * FROM cache_metadata ORDER BY accessed_at ASC"
        let items = try await DatabaseManager.shared.query(sql, mapper: DatabaseMappers.mapCacheMetadata)
        
        var reducedSize: Int64 = 0
        
        for item in items {
            if reducedSize >= targetSize { break }
            
            // 删除文件
            try? FileManager.default.removeItem(atPath: item.cachePath)
            
            // 删除数据库记录
            try await DatabaseManager.shared.executeSQL(
                "DELETE FROM cache_metadata WHERE id = ?",
                parameters: [item.id]
            )
            
            reducedSize += item.cacheSize
        }
    }
} 