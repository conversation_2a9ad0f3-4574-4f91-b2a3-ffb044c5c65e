import Foundation
import SQLite3
import UIKit

// MARK: - 数据库错误类型
enum DatabaseError: Error, LocalizedError {
    case connectionFailed
    case executionFailed(String)
    case bind(String)
    case noData
    case migrationFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .connectionFailed:
            return "数据库连接失败"
        case .executionFailed(let message):
            return "数据库执行失败: \(message)"
        case .bind(let message):
            return "数据绑定失败: \(message)"
        case .noData:
            return "未找到数据"
        case .migrationFailed(let message):
            return "数据库迁移失败: \(message)"
        }
    }
}

// MARK: - 数据库管理器
actor DatabaseManager: ObservableObject {
    static let shared = DatabaseManager()
    
    private var db: OpaquePointer?
    private let currentVersion = 1
    
    // MARK: - 初始化
    private init() {
        Task {
            try await initializeDatabase()
        }
    }
    
    deinit {
        // 在 deinit 中不能调用 actor 隔离的方法，直接同步关闭数据库
        if db != nil {
            sqlite3_close(db)
            db = nil
        }
    }
    
    // MARK: - 数据库初始化
    private func initializeDatabase() async throws {
        let dbPath = getDatabasePath()
        
        // 确保数据库目录存在
        try createDatabaseDirectoryIfNeeded(dbPath: dbPath)
        
        // 打开数据库连接
        if sqlite3_open(dbPath, &db) != SQLITE_OK {
            throw DatabaseError.connectionFailed
        }
        
        // 启用外键约束
        try executeSQL("PRAGMA foreign_keys = ON")
        
        // 设置WAL模式以提高性能
        try executeSQL("PRAGMA journal_mode = WAL")
        
        // 创建表
        try createTables()
        
        // 检查和执行数据库迁移
        try performMigrationIfNeeded()
    }
    
    private func getDatabasePath() -> String {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let dbURL = documentsPath.appendingPathComponent("MediaLibrary.sqlite")
        return dbURL.path
    }
    
    private func createDatabaseDirectoryIfNeeded(dbPath: String) throws {
        let dbDirectory = (dbPath as NSString).deletingLastPathComponent
        try FileManager.default.createDirectory(atPath: dbDirectory, withIntermediateDirectories: true)
    }
    
    // MARK: - 表创建
    private func createTables() throws {
        // 文件夹表
        let createFoldersTable = """
            CREATE TABLE IF NOT EXISTS folders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                path TEXT NOT NULL UNIQUE,
                parent_id INTEGER,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                media_count INTEGER DEFAULT 0,
                total_size INTEGER DEFAULT 0,
                file_count INTEGER DEFAULT 0,
                last_modified TEXT,
                thumbnail_path TEXT,
                FOREIGN KEY (parent_id) REFERENCES folders (id) ON DELETE CASCADE
            )
        """
        
        // 媒体文件表
        let createMediaFilesTable = """
            CREATE TABLE IF NOT EXISTS media_files (
                id TEXT PRIMARY KEY,
                folder_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                file_path TEXT NOT NULL UNIQUE,
                file_type TEXT NOT NULL,
                file_size INTEGER NOT NULL,
                created_at TEXT NOT NULL,
                modified_at TEXT NOT NULL,
                duration REAL,
                width INTEGER,
                height INTEGER,
                thumbnail_path TEXT,
                folder_path TEXT NOT NULL,
                FOREIGN KEY (folder_id) REFERENCES folders (id) ON DELETE CASCADE
            )
        """
        
        // 缓存元数据表
        let createCacheMetadataTable = """
            CREATE TABLE IF NOT EXISTS cache_metadata (
                id TEXT PRIMARY KEY,
                media_file_id TEXT NOT NULL,
                cache_type TEXT NOT NULL,
                cache_path TEXT NOT NULL,
                created_at TEXT NOT NULL,
                accessed_at TEXT NOT NULL,
                cache_size INTEGER NOT NULL,
                FOREIGN KEY (media_file_id) REFERENCES media_files (id) ON DELETE CASCADE
            )
        """
        
        // 数据库版本表
        let createVersionTable = """
            CREATE TABLE IF NOT EXISTS database_version (
                version INTEGER PRIMARY KEY
            )
        """
        
        try executeSQL(createFoldersTable)
        try executeSQL(createMediaFilesTable)
        try executeSQL(createCacheMetadataTable)
        try executeSQL(createVersionTable)
        
        // 创建索引
        try createIndexes()
    }
    
    private func createIndexes() throws {
        let indexes = [
            "CREATE INDEX IF NOT EXISTS idx_folders_name ON folders (name)",
            "CREATE INDEX IF NOT EXISTS idx_folders_path ON folders (path)",
            "CREATE INDEX IF NOT EXISTS idx_media_files_folder_id ON media_files (folder_id)",
            "CREATE INDEX IF NOT EXISTS idx_media_files_name ON media_files (name)",
            "CREATE INDEX IF NOT EXISTS idx_media_files_type ON media_files (file_type)",
            "CREATE INDEX IF NOT EXISTS idx_media_files_created_at ON media_files (created_at)",
            "CREATE INDEX IF NOT EXISTS idx_cache_metadata_media_file_id ON cache_metadata (media_file_id)",
            "CREATE INDEX IF NOT EXISTS idx_cache_metadata_type ON cache_metadata (cache_type)"
        ]
        
        for indexSQL in indexes {
            try executeSQL(indexSQL)
        }
    }
    
    // MARK: - 数据库迁移
    private func performMigrationIfNeeded() throws {
        let currentDBVersion = getDatabaseVersion()
        
        if currentDBVersion < currentVersion {
            try migrateDatabase(from: currentDBVersion, to: currentVersion)
            try setDatabaseVersion(currentVersion)
        }
    }
    
    private func getDatabaseVersion() -> Int {
        do {
            let result = try query("SELECT version FROM database_version LIMIT 1") { stmt in
                return sqlite3_column_int(stmt, 0)
            }
            return Int(result.first ?? 0)
        } catch {
            return 0
        }
    }
    
    private func setDatabaseVersion(_ version: Int) throws {
        try executeSQL("DELETE FROM database_version")
        try executeSQL("INSERT INTO database_version (version) VALUES (?)", parameters: [version])
    }
    
    private func migrateDatabase(from oldVersion: Int, to newVersion: Int) throws {
        // 在这里处理数据库迁移逻辑
        print("数据库从版本 \(oldVersion) 迁移到版本 \(newVersion)")
        
        // 例如：添加新列、创建新表等
        // if oldVersion < 2 {
        //     try executeSQL("ALTER TABLE media_files ADD COLUMN new_column TEXT")
        // }
    }
    
    // MARK: - SQL 执行
    func executeSQL(_ sql: String, parameters: [Any] = []) throws {
        try performExecuteSQL(sql, parameters: parameters)
    }
    
    private func performExecuteSQL(_ sql: String, parameters: [Any]) throws {
        var stmt: OpaquePointer?
        
        guard sqlite3_prepare_v2(db, sql, -1, &stmt, nil) == SQLITE_OK else {
            let errorMessage = String(cString: sqlite3_errmsg(db))
            throw DatabaseError.executionFailed("准备语句失败: \(errorMessage)")
        }
        
        defer { sqlite3_finalize(stmt) }
        
        // 绑定参数
        try bindParameters(stmt: stmt, parameters: parameters)
        
        guard sqlite3_step(stmt) == SQLITE_DONE else {
            let errorMessage = String(cString: sqlite3_errmsg(db))
            throw DatabaseError.executionFailed("执行语句失败: \(errorMessage)")
        }
    }
    
    // MARK: - 查询方法
    func query<T>(
        _ sql: String,
        parameters: [Any] = [],
        mapper: @escaping (OpaquePointer) -> T?
    ) throws -> [T] {
        return try performQuery(sql, parameters: parameters, mapper: mapper)
    }
    
    private func performQuery<T>(
        _ sql: String,
        parameters: [Any],
        mapper: @escaping (OpaquePointer) -> T?
    ) throws -> [T] {
        var stmt: OpaquePointer?
        var results: [T] = []
        
        guard sqlite3_prepare_v2(db, sql, -1, &stmt, nil) == SQLITE_OK else {
            let errorMessage = String(cString: sqlite3_errmsg(db))
            throw DatabaseError.executionFailed("准备查询失败: \(errorMessage)")
        }
        
        defer { sqlite3_finalize(stmt) }
        
        // 绑定参数
        try bindParameters(stmt: stmt, parameters: parameters)
        
        // 执行查询
        while sqlite3_step(stmt) == SQLITE_ROW {
            if let result = mapper(stmt!) {
                results.append(result)
            }
        }
        
        return results
    }
    
    // MARK: - 参数绑定
    private func bindParameters(stmt: OpaquePointer?, parameters: [Any]) throws {
        for (index, parameter) in parameters.enumerated() {
            let bindIndex = Int32(index + 1)
            
            switch parameter {
            case let value as String:
                if sqlite3_bind_text(stmt, bindIndex, value, -1, nil) != SQLITE_OK {
                    throw DatabaseError.bind("绑定文本参数失败")
                }
            case let value as Int:
                if sqlite3_bind_int64(stmt, bindIndex, Int64(value)) != SQLITE_OK {
                    throw DatabaseError.bind("绑定整数参数失败")
                }
            case let value as Int64:
                if sqlite3_bind_int64(stmt, bindIndex, value) != SQLITE_OK {
                    throw DatabaseError.bind("绑定长整数参数失败")
                }
            case let value as Double:
                if sqlite3_bind_double(stmt, bindIndex, value) != SQLITE_OK {
                    throw DatabaseError.bind("绑定浮点数参数失败")
                }
            case is NSNull:
                if sqlite3_bind_null(stmt, bindIndex) != SQLITE_OK {
                    throw DatabaseError.bind("绑定NULL参数失败")
                }
            default:
                if let stringValue = "\(parameter)" as String? {
                    if sqlite3_bind_text(stmt, bindIndex, stringValue, -1, nil) != SQLITE_OK {
                        throw DatabaseError.bind("绑定默认参数失败")
                    }
                } else {
                    throw DatabaseError.bind("不支持的参数类型")
                }
            }
        }
    }
    
    // MARK: - 事务处理
    func performTransaction<T>(_ block: () throws -> T) throws -> T {
        try executeSQL("BEGIN TRANSACTION")
        
        do {
            let result = try block()
            try executeSQL("COMMIT")
            return result
        } catch {
            try executeSQL("ROLLBACK")
            throw error
        }
    }
    
    // 异步事务处理
    func performAsyncTransaction<T>(_ block: () async throws -> T) async throws -> T {
        try await executeSQL("BEGIN TRANSACTION")
        
        do {
            let result = try await block()
            try await executeSQL("COMMIT")
            return result
        } catch {
            try await executeSQL("ROLLBACK")
            throw error
        }
    }
    
    // MARK: - 数据库关闭
    private func closeDatabase() {
        if db != nil {
            sqlite3_close(db)
            db = nil
        }
    }
    
    // MARK: - 数据库维护
    func vacuum() throws {
        try executeSQL("VACUUM")
    }
    
    func analyze() throws {
        try executeSQL("ANALYZE")
    }
    
    func getDatabaseSize() -> Int64 {
        let dbPath = getDatabasePath()
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: dbPath)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
} 