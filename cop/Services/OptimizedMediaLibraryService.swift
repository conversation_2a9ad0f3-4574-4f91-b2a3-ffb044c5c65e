import Foundation
import UIKit
import Combine

// MARK: - 优化的媒体库服务
@MainActor
class OptimizedMediaLibraryService: ObservableObject {
    static let shared = OptimizedMediaLibraryService()
    
    // MARK: - 依赖
    private let repository = MediaDatabaseRepository()
    private let cache = MediaMetadataCache.shared
    private let mediaImportService = MediaImportService.shared
    
    // MARK: - 发布属性
    @Published var isInitialized = false
    @Published var isLoading = false
    @Published var error: Error?
    
    // MARK: - 私有属性
    private var cancellables = Set<AnyCancellable>()
    private let operationQueue = OperationQueue()
    
    // MARK: - 初始化
    private init() {
        setupOperationQueue()
        setupNotifications()
        
        Task {
            await initialize()
        }
    }
    
    private func setupOperationQueue() {
        operationQueue.maxConcurrentOperationCount = 4
        operationQueue.qualityOfService = .utility
    }
    
    private func setupNotifications() {
        // 监听内存警告
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.handleMemoryPressure()
                }
            }
            .store(in: &cancellables)
        
        // 监听应用进入后台
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.handleAppDidEnterBackground()
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - 初始化数据库
    private func initialize() async {
        isLoading = true
        
        do {
            // 初始化数据库和缓存系统
            try await migrateExistingDataIfNeeded()
            isInitialized = true
            error = nil
        } catch {
            self.error = error
            print("媒体库服务初始化失败: \(error)")
        }
        
        isLoading = false
    }
    
    // MARK: - 数据迁移
    private func migrateExistingDataIfNeeded() async throws {
        // 检查是否需要从旧的文件系统数据迁移到数据库
        let folderCount = try await repository.getFolderCount()
        
        if folderCount == 0 {
            // 数据库为空，需要迁移现有数据
            try await migrateFromFileSystem()
        }
    }
    
    private func migrateFromFileSystem() async throws {
        // 获取现有的导入文件夹
        let existingFolders = try await mediaImportService.getImportedFolders()
        
        for folderInfo in existingFolders {
            // 插入文件夹到数据库
            let dbFolder = DatabaseFolder.from(folderInfo)
            let folderId = try await repository.insertFolder(dbFolder)
            
            // 扫描并插入媒体文件
            let mediaFiles = await scanMediaFilesInFolder(folderInfo, folderId: folderId)
            if !mediaFiles.isEmpty {
                try await repository.insertMediaFiles(mediaFiles)
            }
            
            // 更新文件夹统计
            try await repository.updateFolderStatistics(id: folderId)
        }
    }
    
    private func scanMediaFilesInFolder(_ folderInfo: FolderInfo, folderId: Int) async -> [DatabaseMediaFile] {
        let folderURL = URL(fileURLWithPath: folderInfo.path)
        var mediaFiles: [DatabaseMediaFile] = []
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(
                at: folderURL,
                includingPropertiesForKeys: [.isRegularFileKey, .fileSizeKey, .creationDateKey, .contentModificationDateKey],
                options: [.skipsHiddenFiles]
            )
            
            for fileURL in fileURLs {
                if let mediaFileInfo = createMediaFileInfo(from: fileURL, folderPath: folderInfo.name),
                   mediaFileInfo.type == .image || mediaFileInfo.type == .video {
                    let dbMediaFile = DatabaseMediaFile.from(mediaFileInfo, folderId: folderId)
                    mediaFiles.append(dbMediaFile)
                }
            }
        } catch {
            print("扫描文件夹失败: \(error)")
        }
        
        return mediaFiles
    }
    
    private func createMediaFileInfo(from fileURL: URL, folderPath: String) -> MediaFileInfo? {
        // 使用现有的 MediaFormatDetector 逻辑
        guard let mediaType = MediaFormatDetector.detectMediaType(from: fileURL) else {
            return nil
        }
        
        do {
            let resourceValues = try fileURL.resourceValues(forKeys: [
                .fileSizeKey, .creationDateKey, .contentModificationDateKey
            ])
            
            return MediaFileInfo(
                id: UUID(),
                name: fileURL.lastPathComponent,
                type: mediaType,
                fileSize: Int64(resourceValues.fileSize ?? 0),
                creationDate: resourceValues.creationDate ?? Date(),
                modificationDate: resourceValues.contentModificationDate ?? Date(),
                localURL: fileURL,
                thumbnailURL: nil, // 稍后生成
                folderPath: folderPath,
                duration: nil, // 稍后提取
                dimensions: nil // 稍后提取
            )
        } catch {
            return nil
        }
    }
    
    // MARK: - 文件夹操作
    func getAllFolders() async throws -> [FolderInfo] {
        let dbFolders = try await repository.getAllFolders()
        return dbFolders.map { $0.toFolderInfo() }
    }
    
    func getFoldersByName(_ name: String) async throws -> [FolderInfo] {
        let dbFolders = try await repository.getFoldersByName(name)
        return dbFolders.map { $0.toFolderInfo() }
    }
    
    func addFolder(_ folderInfo: FolderInfo) async throws -> Int {
        let dbFolder = DatabaseFolder.from(folderInfo)
        return try await repository.insertFolder(dbFolder)
    }
    
    func deleteFolder(name: String) async throws {
        let folders = try await repository.getFoldersByName(name)
        for folder in folders {
            if let folderId = folder.id {
                try await repository.deleteFolder(id: folderId)
            }
        }
    }
    
    // MARK: - 媒体文件操作
    func getMediaFiles(
        in folderName: String? = nil,
        searchQuery: String? = nil,
        mediaType: MediaType? = nil,
        sortBy: MediaSortOption = .dateCreated,
        ascending: Bool = false,
        pageSize: Int = 50,
        page: Int = 0
    ) async throws -> (files: [MediaFileInfo], totalCount: Int) {
        
        var folderId: Int? = nil
        
        if let folderName = folderName {
            let folders = try await repository.getFoldersByName(folderName)
            folderId = folders.first?.id
        }
        
        let result = try await repository.getMediaFilesPaginated(
            folderId: folderId,
            searchQuery: searchQuery,
            mediaType: mediaType,
            sortBy: sortBy,
            ascending: ascending,
            pageSize: pageSize,
            page: page
        )
        
        let mediaFiles = result.files.compactMap { $0.toMediaFileInfo() }
        
        // 预加载缩略图
        await preloadThumbnails(for: mediaFiles)
        
        return (files: mediaFiles, totalCount: result.totalCount)
    }
    
    func getAllMediaFiles(
        limit: Int? = nil,
        offset: Int? = nil
    ) async throws -> [MediaFileInfo] {
        let dbMediaFiles = try await repository.getAllMediaFiles(limit: limit, offset: offset)
        let mediaFiles = dbMediaFiles.compactMap { $0.toMediaFileInfo() }
        
        // 预加载缩略图
        await preloadThumbnails(for: mediaFiles)
        
        return mediaFiles
    }
    
    func searchMediaFiles(
        query: String,
        type: MediaType? = nil,
        limit: Int? = nil,
        offset: Int? = nil
    ) async throws -> [MediaFileInfo] {
        let dbMediaFiles = try await repository.searchMediaFiles(
            query: query,
            type: type,
            limit: limit,
            offset: offset
        )
        return dbMediaFiles.compactMap { $0.toMediaFileInfo() }
    }
    
    // MARK: - 导入操作
    func importMediaFolder(from sourceURL: URL) async throws -> ImportResult {
        // 使用现有的导入服务进行实际导入
        let importedFiles = try await mediaImportService.importMediaFolder(from: sourceURL)
        
        // 添加到数据库
        if !importedFiles.isEmpty {
            // 创建或获取文件夹
            let folderName = sourceURL.lastPathComponent
            let folderInfo = FolderInfo(
                name: folderName,
                path: sourceURL.path,
                mediaCount: importedFiles.count,
                fileCount: importedFiles.count,
                totalSize: importedFiles.reduce(0) { $0 + $1.fileSize },
                lastModified: Date(),
                thumbnail: nil
            )
            
            let folderId = try await addFolder(folderInfo)
            
            // 添加媒体文件到数据库
            let dbMediaFiles = importedFiles.map { DatabaseMediaFile.from($0, folderId: folderId) }
            try await repository.insertMediaFiles(dbMediaFiles)
            
            // 更新文件夹统计
            try await repository.updateFolderStatistics(id: folderId)
            
            // 后台生成缩略图
            Task.detached(priority: .background) {
                await self.generateThumbnailsInBackground(for: importedFiles)
            }
        }
        
        return ImportResult(
            successCount: importedFiles.count,
            failureCount: 0,
            importedFiles: importedFiles,
            errors: []
        )
    }
    
    // MARK: - 缓存和优化
    private func preloadThumbnails(for mediaFiles: [MediaFileInfo]) async {
        // 限制并发预加载数量
        let semaphore = AsyncSemaphore(value: 5)
        
        await withTaskGroup(of: Void.self) { group in
            for mediaFile in mediaFiles.prefix(20) { // 只预加载前20个
                group.addTask {
                    await semaphore.wait()
                    
                    // 尝试从缓存获取，如果不存在则生成
                    if await self.cache.getCachedImage(for: mediaFile.id, size: .medium) == nil {
                        await self.generateThumbnail(for: mediaFile)
                    }
                    
                    await semaphore.signal()
                }
            }
        }
    }
    
    private func generateThumbnail(for mediaFile: MediaFileInfo) async {
        do {
            var thumbnail: UIImage?
            
            if mediaFile.type == .image {
                thumbnail = UIImage(contentsOfFile: mediaFile.localURL.path)
            } else if mediaFile.type == .video {
                // 使用 AVAssetImageGenerator 生成视频缩略图
                thumbnail = await generateVideoThumbnail(from: mediaFile.localURL)
            }
            
            if let thumbnail = thumbnail {
                // 调整大小并缓存
                let resizedThumbnail = await resizeImage(thumbnail, to: CGSize(width: 300, height: 300))
                try await cache.cacheImage(resizedThumbnail, for: mediaFile.id, size: .medium)
            }
        } catch {
            print("生成缩略图失败: \(error)")
        }
    }
    
    private func generateVideoThumbnail(from url: URL) async -> UIImage? {
        // 实现视频缩略图生成逻辑
        // 这里可以使用 AVAssetImageGenerator
        return nil
    }
    
    private func resizeImage(_ image: UIImage, to size: CGSize) async -> UIImage {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .utility).async {
                UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
                image.draw(in: CGRect(origin: .zero, size: size))
                let resizedImage = UIGraphicsGetImageFromCurrentImageContext() ?? image
                UIGraphicsEndImageContext()
                continuation.resume(returning: resizedImage)
            }
        }
    }
    
    private func generateThumbnailsInBackground(for mediaFiles: [MediaFileInfo]) async {
        for mediaFile in mediaFiles {
            await generateThumbnail(for: mediaFile)
        }
    }
    
    // MARK: - 统计信息
    func getLibraryStatistics() async throws -> LibraryStatistics {
        let folderCount = try await repository.getFolderCount()
        let totalMediaCount = try await repository.getMediaFileCount()
        let totalSize = try await repository.getTotalFileSize()
        
        return LibraryStatistics(
            folderCount: folderCount,
            totalMediaCount: totalMediaCount,
            totalSize: totalSize,
            imageCount: 0, // 可以进一步细化
            videoCount: 0  // 可以进一步细化
        )
    }
    
    func getFolderStatistics(_ folderName: String) async throws -> FolderStatistics? {
        let folders = try await repository.getFoldersByName(folderName)
        guard let folder = folders.first, let folderId = folder.id else {
            return nil
        }
        
        let stats = try await repository.getFolderMediaStatistics(folderId: folderId)
        
        return FolderStatistics(
            name: folder.name,
            mediaCount: folder.mediaCount,
            imageCount: stats.imageCount,
            videoCount: stats.videoCount,
            totalSize: stats.totalSize
        )
    }
    
    // MARK: - 内存管理
    private func handleMemoryPressure() async {
        // 清理内存缓存
        cache.clearMemoryCache()
        
        // 暂停非关键操作
        operationQueue.cancelAllOperations()
    }
    
    private func handleAppDidEnterBackground() async {
        // 清理临时缓存
        cache.clearMemoryCache()
        
        // 暂停操作队列
        operationQueue.isSuspended = true
    }
}

// MARK: - 支持类型
struct ImportResult {
    let successCount: Int
    let failureCount: Int
    let importedFiles: [MediaFileInfo]
    let errors: [Error]
}

struct LibraryStatistics {
    let folderCount: Int
    let totalMediaCount: Int
    let totalSize: Int64
    let imageCount: Int
    let videoCount: Int
    
    var formattedTotalSize: String {
        ByteCountFormatter.string(fromByteCount: totalSize, countStyle: .file)
    }
}

struct FolderStatistics {
    let name: String
    let mediaCount: Int
    let imageCount: Int
    let videoCount: Int
    let totalSize: Int64
    
    var formattedTotalSize: String {
        ByteCountFormatter.string(fromByteCount: totalSize, countStyle: .file)
    }
}

// MARK: - 异步信号量
actor AsyncSemaphore {
    private var value: Int
    private var waiters: [CheckedContinuation<Void, Never>] = []
    
    init(value: Int) {
        self.value = value
    }
    
    func wait() async {
        if value > 0 {
            value -= 1
            return
        }
        
        await withCheckedContinuation { continuation in
            waiters.append(continuation)
        }
    }
    
    func signal() async {
        if let waiter = waiters.first {
            waiters.removeFirst()
            waiter.resume()
        } else {
            value += 1
        }
    }
} 