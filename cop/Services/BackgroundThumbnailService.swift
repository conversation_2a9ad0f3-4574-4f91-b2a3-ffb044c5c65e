//
//  BackgroundThumbnailService.swift
//  cop
//
//  Created by 阿亮 on 2025/6/19.
//  第三阶段性能优化 - 后台缩略图生成服务
//

import Foundation
import SwiftUI
import UIKit
import AVFoundation
import UniformTypeIdentifiers
import os.log

// MARK: - 缩略图尺寸枚举
enum ThumbnailSize: String, CaseIterable {
    case small = "small"     // 150x150 - 列表视图
    case medium = "medium"   // 300x300 - 网格视图
    case large = "large"     // 600x600 - 详情预览
    
    var size: CGSize {
        switch self {
        case .small: return CGSize(width: 150, height: 150)
        case .medium: return CGSize(width: 300, height: 300)
        case .large: return CGSize(width: 600, height: 600)
        }
    }
    
    var directory: String {
        return rawValue
    }
    
    var priority: TaskPriority {
        switch self {
        case .small: return .high
        case .medium: return .medium
        case .large: return .background
        }
    }
}

// MARK: - 缩略图任务
struct ThumbnailTask: Identifiable {
    let id = UUID()
    let mediaFile: MediaFileInfo
    let size: ThumbnailSize
    var status: TaskStatus = .pending
    var retryCount: Int = 0
    let maxRetries: Int = 3
    
    var canRetry: Bool {
        return retryCount < maxRetries
    }
}

enum TaskStatus {
    case pending
    case processing
    case completed
    case failed
    case cancelled
}

// MARK: - 后台缩略图生成服务
actor BackgroundThumbnailService {
    static let shared = BackgroundThumbnailService()
    
    // MARK: - Configuration
    private let maxConcurrentTasks = 3
    private let taskQueue = DispatchQueue(label: "thumbnail.background", qos: .background)
    private let logger = Logger(subsystem: "com.cop.app", category: "BackgroundThumbnail")
    
    // MARK: - State
    private var pendingTasks: [ThumbnailTask] = []
    private var activeTasks: [UUID: Task<Void, Never>] = [:]
    private var completedTasks: Set<String> = [] // 用文件ID+尺寸作为key
    
    // MARK: - Cache Management
    private let fileManager = FileManager.default
    private let maxCacheSize: Int64 = 1024 * 1024 * 1024 // 1GB
    private var currentCacheSize: Int64 = 0
    
    private var thumbnailDirectory: URL {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        return documentsPath.appendingPathComponent("Thumbnails")
    }
    
    nonisolated private init() {
        Task {
            await setupDirectories()
            await calculateCurrentCacheSize()
            await startCacheCleanupTimer()
        }
    }
    
    // MARK: - Public Interface
    
    /// 为媒体文件生成高质量缩略图
    func generateHighQualityThumbnails(for files: [MediaFileInfo]) async {
        logger.info("开始为 \(files.count) 个文件生成高质量缩略图")
        
        var newTasks: [ThumbnailTask] = []
        
        for file in files {
            for size in ThumbnailSize.allCases {
                let taskKey = "\(file.id.uuidString)_\(size.rawValue)"
                
                // 检查是否已完成
                if !completedTasks.contains(taskKey) && !fileExists(for: file.id, size: size) {
                    let task = ThumbnailTask(mediaFile: file, size: size)
                    newTasks.append(task)
                }
            }
        }
        
        // 按优先级排序
        newTasks.sort { $0.size.priority > $1.size.priority }
        
        // 添加到队列
        pendingTasks.append(contentsOf: newTasks)
        
        logger.info("添加了 \(newTasks.count) 个缩略图任务到队列")
        
        // 开始处理任务
        await processPendingTasks()
    }
    
    /// 获取指定文件和尺寸的缩略图URL
    func getThumbnailURL(for fileID: UUID, size: ThumbnailSize) -> URL? {
        let thumbnailURL = getThumbnailFileURL(for: fileID, size: size)
        return fileManager.fileExists(atPath: thumbnailURL.path) ? thumbnailURL : nil
    }
    
    /// 为单个媒体文件排队生成缩略图（兼容性方法）
    func enqueueThumbnailGeneration(for mediaFile: MediaFileInfo) async {
        await generateHighQualityThumbnails(for: [mediaFile])
    }
    
    /// 取消所有待处理的任务
    func cancelAllTasks() async {
        logger.info("取消所有缩略图任务")
        
        // 取消活动任务
        for (_, task) in activeTasks {
            task.cancel()
        }
        activeTasks.removeAll()
        
        // 清空待处理任务
        pendingTasks.removeAll()
    }
    
    /// 清理缓存
    func cleanupCache(targetSizeBytes: Int64? = nil) async {
        let targetSize = targetSizeBytes ?? (maxCacheSize * 8 / 10) // 默认清理到80%
        
        guard currentCacheSize > targetSize else { return }
        
        logger.info("开始清理缓存: 当前 \(self.formatBytes(self.currentCacheSize)), 目标 \(self.formatBytes(targetSize))")
        
        await cleanupOldFiles(targetSize: targetSize)
    }
    
    // MARK: - Private Methods
    
    private func setupDirectories() {
        for size in ThumbnailSize.allCases {
            let sizeDirectory = thumbnailDirectory.appendingPathComponent(size.directory)
            try? fileManager.createDirectory(at: sizeDirectory, withIntermediateDirectories: true)
        }
    }
    
    private func calculateCurrentCacheSize() {
        currentCacheSize = 0
        
        do {
            let contents = try fileManager.contentsOfDirectory(
                at: thumbnailDirectory,
                includingPropertiesForKeys: [.fileSizeKey],
                options: [.skipsHiddenFiles]
            )
            
            for url in contents {
                if url.hasDirectoryPath {
                    // 递归计算子目录
                    let subContents = try fileManager.contentsOfDirectory(
                        at: url,
                        includingPropertiesForKeys: [.fileSizeKey],
                        options: [.skipsHiddenFiles]
                    )
                    
                    for subUrl in subContents {
                        let resourceValues = try subUrl.resourceValues(forKeys: [.fileSizeKey])
                        currentCacheSize += Int64(resourceValues.fileSize ?? 0)
                    }
                } else {
                    let resourceValues = try url.resourceValues(forKeys: [.fileSizeKey])
                    currentCacheSize += Int64(resourceValues.fileSize ?? 0)
                }
            }
        } catch {
            logger.error("计算缓存大小失败: \(error.localizedDescription)")
        }
        
        logger.info("当前缓存大小: \(self.formatBytes(self.currentCacheSize))")
    }
    
    private func startCacheCleanupTimer() {
        Task {
            while true {
                try? await Task.sleep(nanoseconds: 300_000_000_000) // 5分钟
                
                if currentCacheSize > maxCacheSize {
                    await cleanupCache()
                }
            }
        }
    }
    
    private func processPendingTasks() async {
        while !pendingTasks.isEmpty && activeTasks.count < maxConcurrentTasks {
            guard let task = pendingTasks.first else { break }
            pendingTasks.removeFirst()
            
            let taskHandle = Task {
                await self.processTask(task)
            }
            
            activeTasks[task.id] = taskHandle
        }
    }
    
    private func processTask(_ task: ThumbnailTask) async {
        logger.debug("开始处理缩略图任务: \(task.mediaFile.name) - \(task.size.rawValue)")
        
        do {
            try await generateThumbnail(for: task.mediaFile, size: task.size)
            
            let taskKey = "\(task.mediaFile.id.uuidString)_\(task.size.rawValue)"
            completedTasks.insert(taskKey)
            
            logger.debug("缩略图生成成功: \(task.mediaFile.name) - \(task.size.rawValue)")
            
        } catch {
            logger.warning("缩略图生成失败: \(task.mediaFile.name) - \(task.size.rawValue), 错误: \(error.localizedDescription)")
            
            // 重试逻辑
            if task.canRetry {
                var retryTask = task
                retryTask.retryCount += 1
                retryTask.status = .pending
                
                // 延迟后重新添加到队列
                Task {
                    try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒延迟
                    await self.addRetryTask(retryTask)
                }
            }
        }
        
        // 清理活动任务
        activeTasks.removeValue(forKey: task.id)
        
        // 继续处理下一个任务
        await processPendingTasks()
    }
    
    private func addRetryTask(_ task: ThumbnailTask) async {
        pendingTasks.append(task)
        await processPendingTasks()
    }
    
    private func generateThumbnail(for mediaFile: MediaFileInfo, size: ThumbnailSize) async throws {
        let thumbnailURL = getThumbnailFileURL(for: mediaFile.id, size: size)
        
        // 检查文件是否已存在
        if fileManager.fileExists(atPath: thumbnailURL.path) {
            return
        }
        
        switch mediaFile.type {
        case .image:
            try await generateImageThumbnail(
                from: mediaFile.localURL,
                to: thumbnailURL,
                size: size.size
            )
        case .video:
            try await generateVideoThumbnail(
                from: mediaFile.localURL,
                to: thumbnailURL,
                size: size.size
            )
        }
        
        // 更新缓存大小
        do {
            let resourceValues = try thumbnailURL.resourceValues(forKeys: [.fileSizeKey])
            currentCacheSize += Int64(resourceValues.fileSize ?? 0)
        } catch {
            logger.warning("无法获取缩略图文件大小: \(error.localizedDescription)")
        }
    }
    
    private func generateImageThumbnail(from sourceURL: URL, to targetURL: URL, size: CGSize) async throws {
        guard let imageSource = CGImageSourceCreateWithURL(sourceURL as CFURL, nil) else {
            throw BackgroundThumbnailError.imageSourceCreationFailed
        }
        
        let options: [CFString: Any] = [
            kCGImageSourceCreateThumbnailFromImageIfAbsent: true,
            kCGImageSourceCreateThumbnailWithTransform: true,
            kCGImageSourceThumbnailMaxPixelSize: max(size.width, size.height)
        ]
        
        guard let thumbnail = CGImageSourceCreateThumbnailAtIndex(imageSource, 0, options as CFDictionary) else {
            throw BackgroundThumbnailError.thumbnailCreationFailed
        }
        
        let uiImage = UIImage(cgImage: thumbnail)
        guard let imageData = uiImage.jpegData(compressionQuality: 0.85) else {
            throw BackgroundThumbnailError.dataConversionFailed
        }
        
        try imageData.write(to: targetURL)
    }
    
    private func generateVideoThumbnail(from sourceURL: URL, to targetURL: URL, size: CGSize) async throws {
        let asset = AVURLAsset(url: sourceURL)
        
        // 检查资源是否可用
        let isPlayable = try await asset.load(.isPlayable)
        guard isPlayable else {
            try generateDefaultVideoThumbnail(to: targetURL, size: size)
            return
        }
        
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        imageGenerator.maximumSize = size
        
        // 智能选择缩略图时间点
        let duration = try await asset.load(.duration)
        let durationSeconds = CMTimeGetSeconds(duration)
        
        let thumbnailTime: CMTime
        if durationSeconds >= 10.0 {
            // 长视频：在10%处生成缩略图
            thumbnailTime = CMTimeMake(value: Int64(durationSeconds * 0.1), timescale: 1)
        } else if durationSeconds >= 3.0 {
            // 中等视频：在中点生成缩略图
            thumbnailTime = CMTimeMake(value: Int64(durationSeconds / 2.0), timescale: 1)
        } else {
            // 短视频：在1秒处生成缩略图
            thumbnailTime = CMTimeMake(value: 1, timescale: 1)
        }
        
        do {
            let result = try await imageGenerator.image(at: thumbnailTime)
            let cgImage = result.image
            let uiImage = UIImage(cgImage: cgImage)
            
            guard let imageData = uiImage.jpegData(compressionQuality: 0.85) else {
                throw BackgroundThumbnailError.dataConversionFailed
            }
            
            try imageData.write(to: targetURL)
            
        } catch {
            // 如果失败，生成默认缩略图
            try generateDefaultVideoThumbnail(to: targetURL, size: size)
        }
    }
    
    private func generateDefaultVideoThumbnail(to targetURL: URL, size: CGSize) throws {
        let renderer = UIGraphicsImageRenderer(size: size)
        let image = renderer.image { context in
            // 设置渐变背景
            let colors = [UIColor.systemBlue.cgColor, UIColor.systemIndigo.cgColor]
            let gradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(), colors: colors as CFArray, locations: nil)!
            
            context.cgContext.drawLinearGradient(
                gradient,
                start: CGPoint(x: 0, y: 0),
                end: CGPoint(x: size.width, y: size.height),
                options: []
            )
            
            // 绘制播放图标
            let iconSize: CGFloat = min(size.width, size.height) * 0.3
            let iconRect = CGRect(
                x: (size.width - iconSize) / 2,
                y: (size.height - iconSize) / 2,
                width: iconSize,
                height: iconSize
            )
            
            // 绘制三角形播放按钮
            UIColor.white.setFill()
            let playPath = UIBezierPath()
            playPath.move(to: CGPoint(x: iconRect.minX + iconSize * 0.3, y: iconRect.minY + iconSize * 0.2))
            playPath.addLine(to: CGPoint(x: iconRect.minX + iconSize * 0.8, y: iconRect.midY))
            playPath.addLine(to: CGPoint(x: iconRect.minX + iconSize * 0.3, y: iconRect.minY + iconSize * 0.8))
            playPath.close()
            playPath.fill()
        }
        
        guard let imageData = image.jpegData(compressionQuality: 0.85) else {
            throw BackgroundThumbnailError.dataConversionFailed
        }
        
        try imageData.write(to: targetURL)
    }
    
    private func cleanupOldFiles(targetSize: Int64) async {
        do {
            var filesToDelete: [(URL, Date)] = []
            
            // 收集所有缩略图文件及其修改时间
            for size in ThumbnailSize.allCases {
                let sizeDirectory = thumbnailDirectory.appendingPathComponent(size.directory)
                
                let contents = try fileManager.contentsOfDirectory(
                    at: sizeDirectory,
                    includingPropertiesForKeys: [.contentModificationDateKey, .fileSizeKey],
                    options: [.skipsHiddenFiles]
                )
                
                for url in contents {
                    let resourceValues = try url.resourceValues(forKeys: [.contentModificationDateKey])
                    let modificationDate = resourceValues.contentModificationDate ?? Date.distantPast
                    filesToDelete.append((url, modificationDate))
                }
            }
            
            // 按修改时间排序（最旧的在前）
            filesToDelete.sort { $0.1 < $1.1 }
            
            // 删除文件直到达到目标大小
            var deletedSize: Int64 = 0
            for (url, _) in filesToDelete {
                guard currentCacheSize - deletedSize > targetSize else { break }
                
                do {
                    let resourceValues = try url.resourceValues(forKeys: [.fileSizeKey])
                    let fileSize = Int64(resourceValues.fileSize ?? 0)
                    
                    try fileManager.removeItem(at: url)
                    deletedSize += fileSize
                    
                    logger.debug("删除缩略图文件: \(url.lastPathComponent)")
                    
                } catch {
                    logger.warning("删除文件失败: \(url.lastPathComponent), 错误: \(error.localizedDescription)")
                }
            }
            
            currentCacheSize -= deletedSize
            logger.info("缓存清理完成: 删除 \(self.formatBytes(deletedSize)), 当前大小 \(self.formatBytes(self.currentCacheSize))")
            
        } catch {
            logger.error("缓存清理失败: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Helper Methods
    
    private func getThumbnailFileURL(for fileID: UUID, size: ThumbnailSize) -> URL {
        let sizeDirectory = thumbnailDirectory.appendingPathComponent(size.directory)
        return sizeDirectory.appendingPathComponent("\(fileID.uuidString).jpg")
    }
    
    private func fileExists(for fileID: UUID, size: ThumbnailSize) -> Bool {
        let thumbnailURL = getThumbnailFileURL(for: fileID, size: size)
        return fileManager.fileExists(atPath: thumbnailURL.path)
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useAll]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}

// MARK: - 后台缩略图错误类型
enum BackgroundThumbnailError: LocalizedError {
    case imageSourceCreationFailed
    case thumbnailCreationFailed
    case dataConversionFailed
    case fileWriteFailed
    
    var errorDescription: String? {
        switch self {
        case .imageSourceCreationFailed:
            return "无法创建图像源"
        case .thumbnailCreationFailed:
            return "无法创建缩略图"
        case .dataConversionFailed:
            return "数据转换失败"
        case .fileWriteFailed:
            return "文件写入失败"
        }
    }
} 