import Foundation
import UIKit
import OSLog
import Combine
import CommonCrypto

// MARK: - 智能资源优化器（第二阶段性能优化）
final class ResourceOptimizer: @unchecked Sendable {
    
    // MARK: - 单例
    static let shared = ResourceOptimizer()
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.browser", category: "ResourceOptimizer")
    private let optimizationQueue = DispatchQueue(label: "resource.optimization", qos: .utility)
    private let backgroundQueue = DispatchQueue(label: "resource.background", qos: .background)
    private var optimizationTimer: Timer?
    private var isOptimizing = false
    
    // MARK: - 资源配置
    private struct ResourceConfig {
        static let optimizationInterval: TimeInterval = 300        // 5分钟优化一次
        static let backgroundInterval: TimeInterval = 1800        // 30分钟后台优化
        static let maxTempFileAge: TimeInterval = 3600           // 临时文件最大保留1小时
        static let maxCacheSize: UInt64 = 500 * 1024 * 1024     // 最大缓存500MB
        static let targetCacheSize: UInt64 = 200 * 1024 * 1024  // 目标缓存200MB
    }
    
    // MARK: - 资源统计
    private struct ResourceStatistics {
        var totalFilesProcessed: Int = 0
        var totalBytesReclaimed: UInt64 = 0
        var optimizationCount: Int = 0
        var lastOptimizationTime: Date? = nil
        var averageOptimizationTime: TimeInterval = 0
        var memoryUsage: UInt64 = 0
        var cacheSize: UInt64 = 0
        var databaseSize: UInt64 = 0
        var lastOptimized: Date? = nil
        var performanceScore: Double = 0
    }
    
    private var statistics = ResourceStatistics()
    
    // MARK: - 初始化
    private init() {
        setupOptimizationScheduler()
        registerForAppStateChanges()
        logger.info("🔧 资源优化器已启动")
    }
    
    // MARK: - 公共接口
    
    /// 开始智能资源优化
    func startOptimization() {
        guard !isOptimizing else { return }
        
        optimizationQueue.async { [weak self] in
            self?.performResourceOptimization()
        }
    }
    
    /// 停止资源优化
    func stopOptimization() {
        optimizationTimer?.invalidate()
        optimizationTimer = nil
        isOptimizing = false
        logger.info("⏹️ 资源优化已停止")
    }
    
    /// 立即执行资源清理
    func immediateCleanup() async -> ResourceCleanupResult {
        return await withCheckedContinuation { continuation in
            optimizationQueue.async { [weak self] in
                let result = self?.performImmediateCleanup() ?? ResourceCleanupResult()
                continuation.resume(returning: result)
            }
        }
    }
    
    /// 后台优化
    func backgroundOptimization() async {
        await withCheckedContinuation { continuation in
            backgroundQueue.async { [weak self] in
                self?.performBackgroundOptimization()
                continuation.resume()
            }
        }
    }
    
    /// 获取资源统计信息
    private func getStatistics() -> ResourceStatistics {
        let memoryUsage = getCurrentMemoryUsage()
        let cacheSize = getSystemCacheSize()
        let databaseSize = UInt64(100 * 1024 * 1024) // 估算100MB
        
        return ResourceStatistics(
            memoryUsage: memoryUsage,
            cacheSize: cacheSize,
            databaseSize: databaseSize,
            lastOptimized: statistics.lastOptimizationTime,
            performanceScore: calculateBasicPerformanceScore(
                memory: memoryUsage,
                cache: cacheSize,
                database: databaseSize
            )
        )
    }
    
    private func calculateBasicPerformanceScore(memory: UInt64, cache: UInt64, database: UInt64) -> Double {
        // 简单的性能评分算法
        let totalUsage = memory + cache + database
        let maxUsage = UInt64(1024 * 1024 * 1024) // 1GB
        return max(0.0, 100.0 - (Double(totalUsage) / Double(maxUsage)) * 100.0)
    }
    
    /// 优化磁盘空间
    func optimizeDiskSpace() async -> UInt64 {
        return await withCheckedContinuation { continuation in
            backgroundQueue.async { [weak self] in
                let reclaimedBytes = self?.performBasicDiskSpaceOptimization() ?? 0
                continuation.resume(returning: reclaimedBytes)
            }
        }
    }
    
    /// 清理临时文件
    func cleanupTemporaryFiles() async -> UInt64 {
        return await withCheckedContinuation { continuation in
            optimizationQueue.async { [weak self] in
                let reclaimedBytes = self?.performTemporaryFileCleanup() ?? 0
                continuation.resume(returning: reclaimedBytes)
            }
        }
    }
    
    /// 优化缓存大小
    func optimizeCacheSize() async -> UInt64 {
        return await withCheckedContinuation { continuation in
            optimizationQueue.async { [weak self] in
                let reclaimedBytes = self?.performCacheOptimization() ?? 0
                continuation.resume(returning: reclaimedBytes)
            }
        }
    }
    
    // MARK: - 私有方法
    
    private func setupOptimizationScheduler() {
        optimizationTimer = Timer.scheduledTimer(withTimeInterval: ResourceConfig.optimizationInterval, repeats: true) { [weak self] _ in
            self?.startOptimization()
        }
    }
    
    private func registerForAppStateChanges() {
        NotificationCenter.default.addObserver(
            forName: UIApplication.didEnterBackgroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task {
                await self?.backgroundOptimization()
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: UIApplication.willTerminateNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.stopOptimization()
        }
    }
    
    private func performResourceOptimization() {
        guard !isOptimizing else { return }
        
        let startTime = Date()
        isOptimizing = true
        
        defer {
            isOptimizing = false
            updateStatistics(optimizationTime: Date().timeIntervalSince(startTime))
        }
        
        logger.info("🔄 开始资源优化")
        
        var totalReclaimed: UInt64 = 0
        
        // 1. 清理临时文件
        totalReclaimed += performTemporaryFileCleanup()
        
        // 2. 优化缓存大小
        totalReclaimed += performCacheOptimization()
        
        // 3. 清理重复文件
        totalReclaimed += performDuplicateFileCleanup()
        
        // 4. 压缩旧日志
        totalReclaimed += performLogCompression()
        
        // 5. 优化数据库
        Task {
            await performDatabaseOptimization()
        }
        
        statistics.totalBytesReclaimed += totalReclaimed
        
                    logger.info("✅ 资源优化完成，回收空间: \(self.formatBytes(totalReclaimed))")
    }
    
    private func performImmediateCleanup() -> ResourceCleanupResult {
        let startTime = Date()
        var result = ResourceCleanupResult()
        
        logger.info("⚡ 开始立即清理")
        
        // 清理临时文件
        result.tempFilesReclaimed = performTemporaryFileCleanup()
        
        // 清理系统缓存
        result.cacheReclaimed = performSystemCacheCleanup()
        
        // 清理内存
        result.memoryReclaimed = performMemoryCleanup()
        
        result.totalReclaimed = result.tempFilesReclaimed + result.cacheReclaimed + result.memoryReclaimed
        result.processingTime = Date().timeIntervalSince(startTime)
        
        logger.info("⚡ 立即清理完成，总回收: \(self.formatBytes(result.totalReclaimed))")
        
        return result
    }
    
    private func performBackgroundOptimization() {
        logger.info("🌙 开始后台优化")
        
        // 后台数据库维护
        Task {
            await performDatabaseMaintenance()
        }
        
        // 后台缓存整理
        performCacheDefragmentation()
        
        // 后台日志归档
        performLogArchiving()
        
        // 后台统计更新
        updateBackgroundStatistics()
        
        logger.info("🌙 后台优化完成")
    }
    
    private func performTemporaryFileCleanup() -> UInt64 {
        var reclaimedBytes: UInt64 = 0
        let cutoffDate = Date().addingTimeInterval(-ResourceConfig.maxTempFileAge)
        
        let directories = [
            FileManager.default.temporaryDirectory,
            getCacheDirectory(),
            getThumbnailCacheDirectory()
        ].compactMap { $0 }
        
        for directory in directories {
            reclaimedBytes += cleanupDirectory(directory, olderThan: cutoffDate)
        }
        
        logger.info("🗑️ 临时文件清理完成，回收: \(self.formatBytes(reclaimedBytes))")
        return reclaimedBytes
    }
    
    private func performCacheOptimization() -> UInt64 {
        var reclaimedBytes: UInt64 = 0
        let currentCacheSize = getCurrentCacheSize()
        
        if currentCacheSize > ResourceConfig.maxCacheSize {
            let targetReduction = currentCacheSize - ResourceConfig.targetCacheSize
            reclaimedBytes = reduceCacheSize(by: targetReduction)
        }
        
        logger.info("💾 缓存优化完成，回收: \(self.formatBytes(reclaimedBytes))")
        return reclaimedBytes
    }
    
    private func performDuplicateFileCleanup() -> UInt64 {
        var reclaimedBytes: UInt64 = 0
        
        // 扫描并删除重复的缓存文件
        let cacheDirectory = getCacheDirectory()
        if let cacheDir = cacheDirectory {
            reclaimedBytes += findAndRemoveDuplicates(in: cacheDir)
        }
        
        logger.info("🔍 重复文件清理完成，回收: \(self.formatBytes(reclaimedBytes))")
        return reclaimedBytes
    }
    
    private func performLogCompression() -> UInt64 {
        var reclaimedBytes: UInt64 = 0
        
        if let logsDirectory = getLogsDirectory() {
            reclaimedBytes += compressOldLogs(in: logsDirectory)
        }
        
        logger.info("📝 日志压缩完成，回收: \(self.formatBytes(reclaimedBytes))")
        return reclaimedBytes
    }
    
    private func performDatabaseOptimization() async {
        logger.info("🗄️ 开始数据库优化")
        
        // 执行数据库真空操作
        do {
            try await DatabaseManager.shared.vacuum()
            
            // 重建索引 - 移除不存在的方法
            // DatabaseManager.shared.reindex()
            
            // 分析查询性能
            try await DatabaseManager.shared.analyze()
        } catch {
            logger.error("数据库优化失败: \(error.localizedDescription)")
        }
        
        logger.info("🗄️ 数据库优化完成")
    }
    
    private func performSystemCacheCleanup() -> UInt64 {
        let beforeSize = getSystemCacheSize()
        
        // 清理URL缓存
        URLCache.shared.removeAllCachedResponses()
        
        // 清理图片缓存
        clearImageCaches()
        
        let afterSize = getSystemCacheSize()
        return beforeSize > afterSize ? beforeSize - afterSize : 0
    }
    
    private func performMemoryCleanup() -> UInt64 {
        let beforeMemory = getCurrentMemoryUsage()
        
        // 强制垃圾回收
        autoreleasepool {
            _ = Array(0..<1000).map { "\($0)" }
        }
        
        let afterMemory = getCurrentMemoryUsage()
        return beforeMemory > afterMemory ? beforeMemory - afterMemory : 0
    }
    
    private func performDatabaseMaintenance() async {
        // 定期数据库维护
        // DatabaseManager.shared.performMaintenance() // 移除不存在的方法
        await performDatabaseOptimization() // 使用现有的数据库优化方法
    }
    
    private func performCacheDefragmentation() {
        // 缓存碎片整理
        defragmentCacheFiles()
    }
    
    private func performLogArchiving() {
        // 日志归档
        if let logsDirectory = getLogsDirectory() {
            archiveOldLogs(in: logsDirectory)
        }
    }
    
    private func updateBackgroundStatistics() {
        // 更新后台统计信息
        statistics.lastOptimizationTime = Date()
    }
    
    // MARK: - 辅助方法
    
    private func cleanupDirectory(_ directory: URL, olderThan cutoffDate: Date) -> UInt64 {
        var reclaimedBytes: UInt64 = 0
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(
                at: directory,
                includingPropertiesForKeys: [.contentModificationDateKey, .fileSizeKey],
                options: .skipsHiddenFiles
            )
            
            for fileURL in fileURLs {
                let resourceValues = try fileURL.resourceValues(forKeys: [.contentModificationDateKey, .fileSizeKey])
                
                if let modificationDate = resourceValues.contentModificationDate,
                   modificationDate < cutoffDate {
                    
                    let fileSize = UInt64(resourceValues.fileSize ?? 0)
                    
                    try FileManager.default.removeItem(at: fileURL)
                    reclaimedBytes += fileSize
                    statistics.totalFilesProcessed += 1
                }
            }
        } catch {
            logger.error("清理目录失败: \(error.localizedDescription)")
        }
        
        return reclaimedBytes
    }
    
    private func getCurrentCacheSize() -> UInt64 {
        var totalSize: UInt64 = 0
        
        if let cacheDirectory = getCacheDirectory() {
            totalSize += getDirectorySize(cacheDirectory)
        }
        
        if let thumbnailDirectory = getThumbnailCacheDirectory() {
            totalSize += getDirectorySize(thumbnailDirectory)
        }
        
        return totalSize
    }
    
    private func reduceCacheSize(by targetReduction: UInt64) -> UInt64 {
        var actualReduction: UInt64 = 0
        
        // 按照最后访问时间删除缓存文件
        if let cacheDirectory = getCacheDirectory() {
            actualReduction += reduceCacheSizeInDirectory(cacheDirectory, by: targetReduction - actualReduction)
        }
        
        if actualReduction < targetReduction,
           let thumbnailDirectory = getThumbnailCacheDirectory() {
            actualReduction += reduceCacheSizeInDirectory(thumbnailDirectory, by: targetReduction - actualReduction)
        }
        
        return actualReduction
    }
    
    private func reduceCacheSizeInDirectory(_ directory: URL, by targetReduction: UInt64) -> UInt64 {
        var actualReduction: UInt64 = 0
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(
                at: directory,
                includingPropertiesForKeys: [.contentAccessDateKey, .fileSizeKey],
                options: .skipsHiddenFiles
            )
            
            // 按访问时间排序，最久未访问的优先删除
            let sortedFiles = fileURLs.sorted { url1, url2 in
                let date1 = try? url1.resourceValues(forKeys: [.contentAccessDateKey]).contentAccessDate ?? Date.distantPast
                let date2 = try? url2.resourceValues(forKeys: [.contentAccessDateKey]).contentAccessDate ?? Date.distantPast
                return (date1 ?? Date.distantPast) < (date2 ?? Date.distantPast)
            }
            
            for fileURL in sortedFiles {
                guard actualReduction < targetReduction else { break }
                
                let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                let fileSize = UInt64(resourceValues.fileSize ?? 0)
                
                try FileManager.default.removeItem(at: fileURL)
                actualReduction += fileSize
            }
        } catch {
            logger.error("减少缓存大小失败: \(error.localizedDescription)")
        }
        
        return actualReduction
    }
    
    private func findAndRemoveDuplicates(in directory: URL) -> UInt64 {
        var reclaimedBytes: UInt64 = 0
        var fileHashes: [String: URL] = [:]
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(
                at: directory,
                includingPropertiesForKeys: [.fileSizeKey],
                options: .skipsHiddenFiles
            )
            
            for fileURL in fileURLs {
                if let hash = calculateFileHash(fileURL) {
                    if fileHashes[hash] != nil {
                        // 找到重复文件，删除较新的
                        let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                        let fileSize = UInt64(resourceValues.fileSize ?? 0)
                        
                        try FileManager.default.removeItem(at: fileURL)
                        reclaimedBytes += fileSize
                    } else {
                        fileHashes[hash] = fileURL
                    }
                }
            }
        } catch {
            logger.error("查找重复文件失败: \(error.localizedDescription)")
        }
        
        return reclaimedBytes
    }
    
    private func compressOldLogs(in directory: URL) -> UInt64 {
        var reclaimedBytes: UInt64 = 0
        let cutoffDate = Date().addingTimeInterval(-7 * 24 * 3600) // 7天前的日志
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(
                at: directory,
                includingPropertiesForKeys: [.contentModificationDateKey, .fileSizeKey],
                options: .skipsHiddenFiles
            )
            
            for fileURL in fileURLs {
                let resourceValues = try fileURL.resourceValues(forKeys: [.contentModificationDateKey, .fileSizeKey])
                
                if let modificationDate = resourceValues.contentModificationDate,
                   modificationDate < cutoffDate,
                   !fileURL.pathExtension.lowercased().contains("gz") {
                    
                    let originalSize = UInt64(resourceValues.fileSize ?? 0)
                    
                    if compressFile(fileURL) {
                        let compressedSize = getFileSize(fileURL.appendingPathExtension("gz"))
                        reclaimedBytes += originalSize - compressedSize
                    }
                }
            }
        } catch {
            logger.error("压缩日志失败: \(error.localizedDescription)")
        }
        
        return reclaimedBytes
    }
    
    private func archiveOldLogs(in directory: URL) {
        let archiveDate = Date().addingTimeInterval(-30 * 24 * 3600) // 30天前
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(
                at: directory,
                includingPropertiesForKeys: [.contentModificationDateKey],
                options: .skipsHiddenFiles
            )
            
            let archiveDirectory = directory.appendingPathComponent("archive")
            try FileManager.default.createDirectory(at: archiveDirectory, withIntermediateDirectories: true)
            
            for fileURL in fileURLs {
                let resourceValues = try fileURL.resourceValues(forKeys: [.contentModificationDateKey])
                
                if let modificationDate = resourceValues.contentModificationDate,
                   modificationDate < archiveDate {
                    
                    let archiveURL = archiveDirectory.appendingPathComponent(fileURL.lastPathComponent)
                    try FileManager.default.moveItem(at: fileURL, to: archiveURL)
                }
            }
        } catch {
            logger.error("归档日志失败: \(error.localizedDescription)")
        }
    }
    
    private func defragmentCacheFiles() {
        // 缓存碎片整理实现
        // 这里可以实现更复杂的碎片整理逻辑
        logger.info("🔧 缓存碎片整理完成")
    }
    
    private func clearImageCaches() {
        // 清理图片缓存
        if let imageCache = getCacheDirectory()?.appendingPathComponent("Images") {
            try? FileManager.default.removeItem(at: imageCache)
        }
    }
    
    // MARK: - 工具方法
    
    private func getCacheDirectory() -> URL? {
        return FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first
    }
    
    private func getThumbnailCacheDirectory() -> URL? {
        return getCacheDirectory()?.appendingPathComponent("Thumbnails")
    }
    
    private func getLogsDirectory() -> URL? {
        return FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first?.appendingPathComponent("Logs")
    }
    
    private func getDirectorySize(_ directory: URL) -> UInt64 {
        var totalSize: UInt64 = 0
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(
                at: directory,
                includingPropertiesForKeys: [.fileSizeKey],
                options: .skipsHiddenFiles
            )
            
            for fileURL in fileURLs {
                let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                totalSize += UInt64(resourceValues.fileSize ?? 0)
            }
        } catch {
            // 忽略错误，返回0
        }
        
        return totalSize
    }
    
    private func getFileSize(_ fileURL: URL) -> UInt64 {
        do {
            let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
            return UInt64(resourceValues.fileSize ?? 0)
        } catch {
            return 0
        }
    }
    
    private func calculateFileHash(_ fileURL: URL) -> String? {
        do {
            let data = try Data(contentsOf: fileURL)
            return data.sha256
        } catch {
            return nil
        }
    }
    
    private func compressFile(_ fileURL: URL) -> Bool {
        // 简化的文件压缩实现
        // 实际项目中可以使用更高效的压缩算法
        return false
    }
    
    private func getSystemCacheSize() -> UInt64 {
        return UInt64(URLCache.shared.currentDiskUsage + URLCache.shared.currentMemoryUsage)
    }
    
    private func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        return result == KERN_SUCCESS ? info.resident_size : 0
    }
    
    private func updateStatistics(optimizationTime: TimeInterval) {
        statistics.optimizationCount += 1
        statistics.lastOptimizationTime = Date()
        
        // 计算平均优化时间
        let totalTime = statistics.averageOptimizationTime * Double(statistics.optimizationCount - 1) + optimizationTime
        statistics.averageOptimizationTime = totalTime / Double(statistics.optimizationCount)
    }
    
    private func formatBytes(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .decimal
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    private func performBasicDiskSpaceOptimization() -> UInt64 {
        // 基本的磁盘空间优化
        return cleanupOldLogFiles()
    }
    
    private func cleanupOldLogFiles() -> UInt64 {
        guard let logsDirectory = getLogsDirectory() else { return 0 }
        
        let cutoffDate = Date().addingTimeInterval(-7 * 24 * 3600) // 7天前
        return cleanupDirectory(logsDirectory, olderThan: cutoffDate)
    }
    
    deinit {
        optimizationTimer?.invalidate()
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - 资源清理结果
struct ResourceCleanupResult {
    var tempFilesReclaimed: UInt64 = 0
    var cacheReclaimed: UInt64 = 0
    var memoryReclaimed: UInt64 = 0
    var totalReclaimed: UInt64 = 0
    var processingTime: TimeInterval = 0
    
    var formattedTotalReclaimed: String {
        ByteCountFormatter().string(fromByteCount: Int64(totalReclaimed))
    }
}

// MARK: - Data 扩展
extension Data {
    var sha256: String {
        let digest = self.withUnsafeBytes { bytes in
            var hash = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
            CC_SHA256(bytes.bindMemory(to: UInt8.self).baseAddress, CC_LONG(self.count), &hash)
            return hash
        }
        return digest.map { String(format: "%02x", $0) }.joined()
    }
}

// MARK: - DatabaseManager 扩展支持
// 移除重复的方法定义，这些方法已在DatabaseManager.swift中定义 