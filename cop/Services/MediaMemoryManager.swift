import Foundation
import UIKit
import OSLog
import Combine

// MARK: - 媒体专用内存管理器（第二阶段性能优化）
@MainActor
final class MediaMemoryManager: ObservableObject {
    
    // MARK: - 单例
    static let shared = MediaMemoryManager()
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.browser", category: "MediaMemoryManager")
    private let memoryQueue = DispatchQueue(label: "memory.management", qos: .utility)
    private var memoryTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 发布属性
    @Published var currentMemoryUsage: UInt64 = 0
    @Published var memoryPressureLevel: MemoryPressureLevel = .normal
    @Published var isOptimizing: Bool = false
    @Published var memoryStatistics: MemoryStatistics = MemoryStatistics()
    
    // MARK: - 内存阈值配置
    private struct MemoryThresholds {
        static let warningThreshold: UInt64 = 200 * 1024 * 1024      // 200MB
        static let criticalThreshold: UInt64 = 350 * 1024 * 1024     // 350MB
        static let emergencyThreshold: UInt64 = 450 * 1024 * 1024    // 450MB
        static let optimalTarget: UInt64 = 150 * 1024 * 1024         // 150MB 目标
    }
    
    // MARK: - 缓存管理器引用
    private weak var thumbnailMemoryCache: ThumbnailMemoryCache?
    private weak var thumbnailDiskCache: ThumbnailDiskCache?
    private weak var unifiedMemoryManager: UnifiedMemoryManager?
    
    // MARK: - 内存压力预测算法优化（第二阶段完善）
    struct MemoryPredictionModel {
        // 历史数据点
        private var memoryHistory: [MemoryDataPoint] = []
        private let maxHistorySize = 100
        
        // 预测参数
        private struct PredictionParams {
            static let shortTermWeight: Double = 0.4  // 短期趋势权重
            static let mediumTermWeight: Double = 0.35 // 中期趋势权重
            static let longTermWeight: Double = 0.25  // 长期趋势权重
            static let seasonalityFactor: Double = 0.1 // 季节性因子
        }
        
        mutating func addDataPoint(_ usage: UInt64) {
            let dataPoint = MemoryDataPoint(timestamp: Date(), usage: usage)
            memoryHistory.append(dataPoint)
            
            // 保持历史数据在合理范围内
            if memoryHistory.count > maxHistorySize {
                memoryHistory.removeFirst(memoryHistory.count - maxHistorySize)
            }
        }
        
        func predictUsageIn(seconds: TimeInterval) -> UInt64 {
            guard memoryHistory.count >= 3 else {
                return memoryHistory.last?.usage ?? 0
            }
            
            // 短期趋势（最近5个数据点）
            let shortTermTrend = calculateTrend(dataPoints: Array(memoryHistory.suffix(5)))
            
            // 中期趋势（最近20个数据点）
            let mediumTermTrend = calculateTrend(dataPoints: Array(memoryHistory.suffix(20)))
            
            // 长期趋势（全部数据点）
            let longTermTrend = calculateTrend(dataPoints: memoryHistory)
            
            // 加权预测
            let currentUsage = Double(memoryHistory.last?.usage ?? 0)
            let predictedChange = (
                shortTermTrend * PredictionParams.shortTermWeight +
                mediumTermTrend * PredictionParams.mediumTermWeight +
                longTermTrend * PredictionParams.longTermWeight
            ) * seconds
            
            // 应用季节性调整（考虑应用使用模式）
            let seasonalAdjustment = calculateSeasonalAdjustment()
            
            let predictedUsage = currentUsage + predictedChange + seasonalAdjustment
            return UInt64(max(0, predictedUsage))
        }
        
        private func calculateTrend(dataPoints: [MemoryDataPoint]) -> Double {
            guard dataPoints.count >= 2 else { return 0 }
            
            let n = Double(dataPoints.count)
            let sumX = (0..<dataPoints.count).reduce(0, +)
            let sumY = dataPoints.map { Double($0.usage) }.reduce(0, +)
            let sumXY = zip(0..<dataPoints.count, dataPoints).map { Double($0) * Double($1.usage) }.reduce(0, +)
            let sumXX = (0..<dataPoints.count).map { Double($0 * $0) }.reduce(0, +)
            
            let slope = (n * sumXY - Double(sumX) * sumY) / (n * sumXX - Double(sumX * sumX))
            return slope.isFinite ? slope : 0
        }
        
        private func calculateSeasonalAdjustment() -> Double {
            // 基于时间的季节性调整（简化版本）
            let hour = Calendar.current.component(.hour, from: Date())
            
            // 用户活跃时间的内存使用通常更高
            switch hour {
            case 9...11, 14...16, 19...21: // 高活跃时段
                return 20 * 1024 * 1024 // +20MB
            case 0...6: // 低活跃时段
                return -10 * 1024 * 1024 // -10MB
            default:
                return 0
            }
        }
        
        func getPressureProbability(threshold: UInt64, timeWindow: TimeInterval) -> Double {
            let predictedUsage = predictUsageIn(seconds: timeWindow)
            
            if predictedUsage <= threshold {
                return 0.0
            }
            
            // 计算超出阈值的程度，转换为概率
            let excess = Double(predictedUsage - threshold)
            let maxExcess = Double(threshold) * 0.5 // 阈值的50%作为最大超出值
            
            return min(1.0, excess / maxExcess)
        }
    }
    
    struct MemoryDataPoint {
        let timestamp: Date
        let usage: UInt64
    }
    
    // MARK: - 初始化
    private init() {
        setupMemoryMonitoring()
        registerForMemoryWarnings()
        setupStatisticsTracking()
        logger.info("🧠 媒体内存管理器已启动")
    }
    
    // MARK: - 公共接口
    
    /// 注册缓存管理器
    func registerCacheManagers(
        thumbnailMemory: ThumbnailMemoryCache,
        thumbnailDisk: ThumbnailDiskCache,
        unified: UnifiedMemoryManager
    ) {
        self.thumbnailMemoryCache = thumbnailMemory
        self.thumbnailDiskCache = thumbnailDisk
        self.unifiedMemoryManager = unified
        logger.info("📝 已注册缓存管理器")
    }
    
    /// 优化媒体查看的内存配置
    func optimizeForMediaViewing() {
        logger.info("🎯 开始优化媒体查看内存配置")
        
        // 调整系统缓存策略
        configureSystemCaches()
        
        // 配置图片缓存
        configureThumbnailCaches()
        
        // 启动智能内存监控
        startIntelligentMonitoring()
        
        logger.info("✅ 媒体查看内存优化完成")
    }
    
    /// 预测性内存清理
    func predictiveMemoryCleanup() async {
        guard !isOptimizing else { return }
        
        await MainActor.run {
            isOptimizing = true
        }
        
        defer {
            Task { @MainActor in
                isOptimizing = false
            }
        }
        
        logger.info("🔮 开始预测性内存清理")
        
        let projectedUsage = await calculateProjectedMemoryUsage()
        
        if projectedUsage > MemoryThresholds.warningThreshold {
            await performIntelligentCleanup(targetReduction: projectedUsage - MemoryThresholds.optimalTarget)
        }
        
        logger.info("✨ 预测性内存清理完成")
    }
    
    /// 智能资源释放
    func smartResourceRelease() async {
        logger.info("🎛️ 开始智能资源释放")
        
        // 释放不活跃的缩略图
        await releaseInactiveThumbnails()
        
        // 清理临时文件
        await cleanupTemporaryFiles()
        
        // 压缩内存缓存
        await compressMemoryCaches()
        
        // 触发垃圾回收
        await triggerIntelligentGarbageCollection()
        
        logger.info("🧹 智能资源释放完成")
    }
    
    /// 处理内存压力
    func handleMemoryPressure() async {
        let currentUsage = getCurrentMemoryUsage()
        let pressureLevel = calculateMemoryPressureLevel(currentUsage)
        
        await MainActor.run {
            self.memoryPressureLevel = pressureLevel
        }
        
        logger.warning("⚠️ 内存压力检测: \(pressureLevel.description), 当前使用: \(self.formatBytes(currentUsage))")
        
        switch pressureLevel {
        case .normal:
            // 正常状态，无需特殊处理
            break
            
        case .warning:
            await performMildCleanup()
            
        case .critical:
            await performAggressiveCleanup()
            
        case .emergency:
            await performEmergencyCleanup()
        }
    }
    
    /// 获取内存统计信息
    func getMemoryStatistics() async -> MemoryStatistics {
        let usage = getCurrentMemoryUsage()
        let cacheUsage = await getCacheMemoryUsage()
        let thumbnailUsage = await getThumbnailMemoryUsage()
        
        let statistics = MemoryStatistics(
            totalUsage: usage,
            cacheUsage: cacheUsage,
            thumbnailUsage: thumbnailUsage,
            pressureLevel: memoryPressureLevel,
            optimizationCount: memoryStatistics.optimizationCount,
            lastOptimizationTime: memoryStatistics.lastOptimizationTime
        )
        
        await MainActor.run {
            self.memoryStatistics = statistics
        }
        
        return statistics
    }
    
    // MARK: - 私有方法
    
    private func setupMemoryMonitoring() {
        // 每5秒监控内存使用
        memoryTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.updateMemoryStatistics()
            }
        }
    }
    
    private func registerForMemoryWarnings() {
        NotificationCenter.default
            .publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                Task {
                    await self?.handleSystemMemoryWarning()
                }
            }
            .store(in: &cancellables)
    }
    
    private func setupStatisticsTracking() {
        // 每分钟更新统计信息
        Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { [weak self] _ in
            Task {
                _ = await self?.getMemoryStatistics()
            }
        }
    }
    
    private func configureSystemCaches() {
        // 调整URL缓存
        let urlCache = URLCache(
            memoryCapacity: 30 * 1024 * 1024,    // 30MB 内存
            diskCapacity: 150 * 1024 * 1024,     // 150MB 磁盘
            diskPath: "MediaURLCache"
        )
        URLCache.shared = urlCache
        
        // 配置图片解码缓存
        configureImageDecodingCache()
    }
    
    private func configureThumbnailCaches() {
        // 配置缩略图内存缓存
        thumbnailMemoryCache?.configureForOptimalPerformance()
        
        // 配置缩略图磁盘缓存
        Task {
            await thumbnailDiskCache?.optimizeForPerformance()
        }
    }
    
    private func startIntelligentMonitoring() {
        // 启动预测性监控
        Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task {
                await self?.predictiveMemoryCleanup()
            }
        }
    }
    
    private func configureImageDecodingCache() {
        // 设置图片解码选项
        if #available(iOS 15.0, *) {
            // 使用新的图片解码API
            let options = [
                kCGImageSourceShouldCache: false,
                kCGImageSourceShouldCacheImmediately: false,
                kCGImageSourceCreateThumbnailFromImageAlways: true
            ]
            // 存储配置供后续使用
            UserDefaults.standard.set(options, forKey: "ImageDecodingOptions")
        }
    }
    
    private func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        guard result == KERN_SUCCESS else {
            logger.error("❌ 获取内存使用失败")
            return 0
        }
        
        return info.resident_size
    }
    
    private func calculateMemoryPressureLevel(_ usage: UInt64) -> MemoryPressureLevel {
        switch usage {
        case 0..<MemoryThresholds.warningThreshold:
            return .normal
        case MemoryThresholds.warningThreshold..<MemoryThresholds.criticalThreshold:
            return .warning
        case MemoryThresholds.criticalThreshold..<MemoryThresholds.emergencyThreshold:
            return .critical
        default:
            return .emergency
        }
    }
    
    private func calculateProjectedMemoryUsage() async -> UInt64 {
        let currentUsage = getCurrentMemoryUsage()
        
        // 简化的预测逻辑（移除复杂的预测模型）
        // 基于当前使用趋势的简单预测
        let shortTermPrediction = currentUsage + UInt64(Double(currentUsage) * 0.1) // 预测10%增长
        
        // 考虑当前活动的影响
        let activityAdjustment = await calculateActivityBasedAdjustment()
        
        // 考虑缓存增长的影响
        let cacheGrowthPrediction = await predictCacheGrowth()
        
        let totalPrediction = shortTermPrediction + activityAdjustment + cacheGrowthPrediction
        
        logger.info("📊 内存使用预测: 当前 \(self.formatBytes(currentUsage)), 预测 \(self.formatBytes(totalPrediction))")
        
        return totalPrediction
    }
    
    private func calculateActivityBasedAdjustment() async -> UInt64 {
        var adjustment: UInt64 = 0
        
        // 检查是否正在导入文件
        if ConcurrentMediaImportService.shared.isImporting {
            adjustment += 50 * 1024 * 1024 // +50MB for import operations
        }
        
        // 检查是否有活跃的缩略图生成（简化实现）
        let thumbnailManager = OptimizedThumbnailManager.shared
        let activeTasks = thumbnailManager.statistics.activeTaskCount // 使用已存在的属性
        if activeTasks > 0 {
            adjustment += UInt64(activeTasks) * 10 * 1024 * 1024 // +10MB per task
        }
        
        // 检查浏览器活动
        let browserManager = BrowserManager.shared
        if browserManager.currentMetrics.activeWebViews > 0 {
            adjustment += UInt64(browserManager.currentMetrics.activeWebViews) * 30 * 1024 * 1024 // +30MB per WebView
        }
        
        return adjustment
    }
    
    private func predictCacheGrowth() async -> UInt64 {
        let currentCacheUsage = await getCacheMemoryUsage()
        
        // 基于历史缓存增长模式预测
        let cacheGrowthRate: Double = 0.02 // 2% per prediction cycle
        let predictedGrowth = Double(currentCacheUsage) * cacheGrowthRate
        
        return UInt64(predictedGrowth)
    }
    
    private func performIntelligentCleanup(targetReduction: UInt64) async {
        logger.info("🧠 执行智能清理，目标释放: \(self.formatBytes(targetReduction))")
        
        var releasedMemory: UInt64 = 0
        
        // 优先级清理策略
        if releasedMemory < targetReduction {
            releasedMemory += await clearOldThumbnails()
        }
        
        if releasedMemory < targetReduction {
            releasedMemory += await clearInactiveCaches()
        }
        
        if releasedMemory < targetReduction {
            releasedMemory += await performGarbageCollection()
        }
        
        logger.info("✅ 智能清理完成，释放内存: \(self.formatBytes(releasedMemory))")
    }
    
    private func performMildCleanup() async {
        logger.info("🧹 执行温和清理")
        
        _ = await clearOldThumbnails()
        await cleanupTemporaryFiles()
        
        await updateOptimizationStatistics()
    }
    
    private func performAggressiveCleanup() async {
        logger.warning("🔥 执行激进清理")
        
        _ = await clearOldThumbnails()
        _ = await clearInactiveCaches()
        await cleanupTemporaryFiles()
        _ = await performGarbageCollection()
        
        await updateOptimizationStatistics()
    }
    
    private func performEmergencyCleanup() async {
        logger.error("🚨 执行紧急清理")
        
        await clearAllCaches()
        await releaseInactiveViews()
        _ = await performGarbageCollection()
        await notifyEmergencyState()
        
        await updateOptimizationStatistics()
    }
    
    private func releaseInactiveThumbnails() async {
        // await thumbnailMemoryCache?.clearInactiveItems() // 方法不存在，注释掉
        await thumbnailDiskCache?.clearOldItems(olderThan: 300) // 5分钟
    }
    
    private func cleanupTemporaryFiles() async {
        let tempDirectory = FileManager.default.temporaryDirectory
        let cacheDirectory = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first
        
        // 清理临时目录
        await cleanupDirectory(tempDirectory, olderThan: 3600) // 1小时
        
        // 清理缓存目录中的临时文件
        if let cacheDir = cacheDirectory {
            await cleanupDirectory(cacheDir, olderThan: 24 * 3600) // 24小时
        }
    }
    
    private func cleanupDirectory(_ directory: URL, olderThan seconds: TimeInterval) async {
        let cutoffDate = Date().addingTimeInterval(-seconds)
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(
                at: directory,
                includingPropertiesForKeys: [.contentModificationDateKey],
                options: .skipsHiddenFiles
            )
            
            for fileURL in fileURLs {
                if let modificationDate = try? fileURL.resourceValues(forKeys: [.contentModificationDateKey]).contentModificationDate,
                   modificationDate < cutoffDate {
                    try? FileManager.default.removeItem(at: fileURL)
                }
            }
        } catch {
            logger.error("清理目录失败: \(error.localizedDescription)")
        }
    }
    
    private func compressMemoryCaches() async {
        // 压缩内存中的缓存
        await thumbnailMemoryCache?.compress()
        
        // 通知统一内存管理器进行压缩（方法不存在，注释掉）
        // await unifiedMemoryManager?.compressMemoryUsage()
    }
    
    private func triggerIntelligentGarbageCollection() async {
        // 智能垃圾回收
        _ = await performGarbageCollection()
        
        // 等待垃圾回收完成
        try? await Task.sleep(nanoseconds: 100_000_000) // 100ms
    }
    
    private func clearOldThumbnails() async -> UInt64 {
        let beforeSize = await getThumbnailMemoryUsage()
        await thumbnailMemoryCache?.clearOldItems(olderThan: 300) // 5分钟
        let afterSize = await getThumbnailMemoryUsage()
        return beforeSize - afterSize
    }
    
    private func clearInactiveCaches() async -> UInt64 {
        let beforeSize = getCurrentMemoryUsage()
        
        URLCache.shared.removeAllCachedResponses()
        await thumbnailMemoryCache?.clearInactiveItems()
        
        let afterSize = getCurrentMemoryUsage()
        return beforeSize > afterSize ? beforeSize - afterSize : 0
    }
    
    private func performGarbageCollection() async -> UInt64 {
        let beforeSize = getCurrentMemoryUsage()
        
        // 强制垃圾回收
        autoreleasepool {
            // 创建临时对象强制内存回收
            _ = Array(0..<1000).map { "\($0)" }
        }
        
        let afterSize = getCurrentMemoryUsage()
        return beforeSize > afterSize ? beforeSize - afterSize : 0
    }
    
    private func clearAllCaches() async {
        await thumbnailMemoryCache?.clearAll()
        URLCache.shared.removeAllCachedResponses()
        
        // 清理图片缓存
        if let cacheDirectory = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first {
            try? FileManager.default.removeItem(at: cacheDirectory.appendingPathComponent("ImageCache"))
        }
    }
    
    private func releaseInactiveViews() async {
        // 通知视图层释放不活跃的视图
        await MainActor.run {
            NotificationCenter.default.post(name: .memoryPressureReleaseViews, object: nil)
        }
    }
    
    private func notifyEmergencyState() async {
        await MainActor.run {
            NotificationCenter.default.post(name: .memoryEmergencyState, object: nil)
        }
    }
    
    private func handleSystemMemoryWarning() async {
        logger.warning("⚠️ 收到系统内存警告")
        await performAggressiveCleanup()
    }
    
    private func updateMemoryStatistics() async {
        let usage = getCurrentMemoryUsage()
        let pressureLevel = calculateMemoryPressureLevel(usage)
        
        currentMemoryUsage = usage
        memoryPressureLevel = pressureLevel
    }
    
    private func getCacheMemoryUsage() async -> UInt64 {
        let urlCacheSize = UInt64(URLCache.shared.currentMemoryUsage)
        let thumbnailCacheSize = await getThumbnailMemoryUsage()
        return urlCacheSize + thumbnailCacheSize
    }
    
    private func getThumbnailMemoryUsage() async -> UInt64 {
        return await thumbnailMemoryCache?.getCurrentMemoryUsage() ?? 0
    }
    
    private func updateOptimizationStatistics() async {
        await MainActor.run {
            memoryStatistics.optimizationCount += 1
            memoryStatistics.lastOptimizationTime = Date()
        }
    }
    
    private func formatBytes(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .decimal
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    deinit {
        memoryTimer?.invalidate()
        cancellables.removeAll()
    }
}

// MARK: - 内存压力级别（已迁移到 MemoryUtils.swift）

// MARK: - 内存统计信息
struct MemoryStatistics {
    var totalUsage: UInt64 = 0
    var cacheUsage: UInt64 = 0
    var thumbnailUsage: UInt64 = 0
    var pressureLevel: MemoryPressureLevel = .normal
    var optimizationCount: Int = 0
    var lastOptimizationTime: Date? = nil
    
    var formattedTotalUsage: String {
        ByteCountFormatter().string(fromByteCount: Int64(totalUsage))
    }
    
    var formattedCacheUsage: String {
        ByteCountFormatter().string(fromByteCount: Int64(cacheUsage))
    }
    
    var formattedThumbnailUsage: String {
        ByteCountFormatter().string(fromByteCount: Int64(thumbnailUsage))
    }
}

// MARK: - 通知定义（第二阶段完善）
extension Notification.Name {
    static let memoryPressureReleaseViews = Notification.Name("memoryPressureReleaseViews")
    static let memoryEmergencyState = Notification.Name("memoryEmergencyState")
    static let memoryOptimizationComplete = Notification.Name("memoryOptimizationComplete")
}

// MARK: - WebView管理器集成（第二阶段完善）
extension MediaMemoryManager {
    
    /// 注册WebView管理器
    func registerWebViewManager(_ manager: BrowserManager) {
        // 保存弱引用到WebView管理器
        objc_setAssociatedObject(self, &AssociatedKeys.webViewManager, manager, .OBJC_ASSOCIATION_ASSIGN)
        
        // 启动WebView内存监控
        startWebViewMemoryMonitoring()
        
        logger.info("📱 已注册WebView管理器")
    }
    
    /// 获取WebView管理器
    private var webViewManager: BrowserManager? {
        return objc_getAssociatedObject(self, &AssociatedKeys.webViewManager) as? BrowserManager
    }
    
    /// 启动WebView内存监控
    private func startWebViewMemoryMonitoring() {
        // 每30秒检查WebView内存使用
        Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.monitorWebViewMemory()
            }
        }
    }
    
    /// 监控WebView内存使用
    private func monitorWebViewMemory() async {
        guard let webViewManager = webViewManager else { return }
        
        let webViewMemoryUsage = await webViewManager.getDetailedMemoryUsage()
        
        // 如果WebView内存使用过高，触发预测性管理
        if webViewMemoryUsage.totalMemory > 100 * 1024 * 1024 { // 100MB
            logger.warning("🌐 WebView内存使用过高: \(webViewMemoryUsage.formattedTotalMemory)")
            await webViewManager.predictiveMemoryManagement()
        }
        
        // 更新统计信息
        await updateWebViewMemoryStatistics(webViewMemoryUsage)
    }
    
    /// 更新WebView内存统计
    private func updateWebViewMemoryStatistics(_ usage: WebViewMemoryUsage) async {
        // 将WebView内存使用纳入整体内存统计
        let totalUsage = currentMemoryUsage + usage.totalMemory
        
        await MainActor.run {
            self.currentMemoryUsage = totalUsage
            
            // 更新内存统计
            self.memoryStatistics = MemoryStatistics(
                totalUsage: totalUsage,
                cacheUsage: memoryStatistics.cacheUsage,
                thumbnailUsage: memoryStatistics.thumbnailUsage,
                pressureLevel: memoryPressureLevel,
                optimizationCount: memoryStatistics.optimizationCount,
                lastOptimizationTime: memoryStatistics.lastOptimizationTime
            )
        }
    }
    
    /// 协调式内存清理
    func coordinatedMemoryCleanup() async {
        logger.info("🤝 开始协调式内存清理")
        
        // 1. 分析当前内存使用分布
        let memoryDistribution = await analyzeMemoryDistribution()
        
        // 2. 制定清理策略
        let cleanupPlan = createCleanupPlan(distribution: memoryDistribution)
        
        // 3. 执行协调清理
        await executeCoordinatedCleanup(plan: cleanupPlan)
        
        logger.info("✅ 协调式内存清理完成")
    }
    
    /// 分析内存使用分布
    private func analyzeMemoryDistribution() async -> MemoryDistribution {
        let cacheUsage = await getCacheMemoryUsage()
        let thumbnailUsage = await getThumbnailMemoryUsage()
        
        var webViewUsage: UInt64 = 0
        if let webViewManager = webViewManager {
            let webViewMemory = await webViewManager.getDetailedMemoryUsage()
            webViewUsage = webViewMemory.totalMemory
        }
        
        let systemUsage = currentMemoryUsage - cacheUsage - thumbnailUsage - webViewUsage
        
        return MemoryDistribution(
            cacheUsage: cacheUsage,
            thumbnailUsage: thumbnailUsage,
            webViewUsage: webViewUsage,
            systemUsage: systemUsage,
            totalUsage: currentMemoryUsage
        )
    }
    
    /// 创建清理计划
    private func createCleanupPlan(distribution: MemoryDistribution) -> CleanupPlan {
        var plan = CleanupPlan()
        
        // 基于内存分布确定清理优先级
        let totalMemory = distribution.totalUsage
        let threshold = MemoryThresholds.warningThreshold
        
        if totalMemory > threshold {
            let excess = totalMemory - threshold
            let excessRatio = Double(excess) / Double(totalMemory)
            
            // 按使用比例分配清理目标
            plan.cacheCleanupTarget = UInt64(Double(distribution.cacheUsage) * excessRatio * 0.4)
            plan.thumbnailCleanupTarget = UInt64(Double(distribution.thumbnailUsage) * excessRatio * 0.3)
            plan.webViewCleanupTarget = UInt64(Double(distribution.webViewUsage) * excessRatio * 0.3)
            
            // 确定清理策略
            if excessRatio > 0.5 {
                plan.aggressiveMode = true
            }
        }
        
        return plan
    }
    
    /// 执行协调清理
    private func executeCoordinatedCleanup(plan: CleanupPlan) async {
        // 并行执行各组件的清理
        await withTaskGroup(of: Void.self) { group in
            // 清理缓存
            if plan.cacheCleanupTarget > 0 {
                group.addTask { [weak self] in
                    await self?.cleanupCacheMemory(target: plan.cacheCleanupTarget)
                }
            }
            
            // 清理缩略图
            if plan.thumbnailCleanupTarget > 0 {
                group.addTask { [weak self] in
                    await self?.cleanupThumbnailMemory(target: plan.thumbnailCleanupTarget)
                }
            }
            
            // 清理WebView
            if plan.webViewCleanupTarget > 0 {
                group.addTask { [weak self] in
                    await self?.cleanupWebViewMemory(target: plan.webViewCleanupTarget)
                }
            }
        }
        
        // 发送清理完成通知
        NotificationCenter.default.post(name: .memoryOptimizationComplete, object: self)
    }
    
    /// 清理缓存内存
    private func cleanupCacheMemory(target: UInt64) async {
        guard let thumbnailMemoryCache = thumbnailMemoryCache else { return }
        
        await thumbnailMemoryCache.preciseCleanup(
            strategy: .predictive(await getCacheMemoryUsage() - target)
        )
    }
    
    /// 清理缩略图内存
    private func cleanupThumbnailMemory(target: UInt64) async {
        guard let thumbnailDiskCache = thumbnailDiskCache else { return }
        
        // 简化的缓存清理（方法不存在，使用简化实现）
        await thumbnailDiskCache.clearOldItems(olderThan: 300) // 5分钟
    }
    
    /// 清理WebView内存
    private func cleanupWebViewMemory(target: UInt64) async {
        guard let webViewManager = webViewManager else { return }
        
        await webViewManager.optimizeMemory()
        
        // 如果需要激进清理
        let currentUsage = await webViewManager.getDetailedMemoryUsage()
        if currentUsage.totalMemory > target {
            await webViewManager.suspendInactiveWebViews()
        }
    }
}

// MARK: - 内存分布和清理计划数据结构
struct MemoryDistribution {
    let cacheUsage: UInt64
    let thumbnailUsage: UInt64
    let webViewUsage: UInt64
    let systemUsage: UInt64
    let totalUsage: UInt64
    
    var cachePercentage: Double {
        return Double(cacheUsage) / Double(totalUsage)
    }
    
    var thumbnailPercentage: Double {
        return Double(thumbnailUsage) / Double(totalUsage)
    }
    
    var webViewPercentage: Double {
        return Double(webViewUsage) / Double(totalUsage)
    }
    
    var systemPercentage: Double {
        return Double(systemUsage) / Double(totalUsage)
    }
}

struct CleanupPlan {
    var cacheCleanupTarget: UInt64 = 0
    var thumbnailCleanupTarget: UInt64 = 0
    var webViewCleanupTarget: UInt64 = 0
    var aggressiveMode: Bool = false
    
    var totalCleanupTarget: UInt64 {
        return cacheCleanupTarget + thumbnailCleanupTarget + webViewCleanupTarget
    }
}

// MARK: - 增强的内存统计结构（第二阶段完善）
struct EnhancedMemoryStatistics {
    let totalUsage: UInt64
    let cacheUsage: UInt64
    let thumbnailUsage: UInt64
    let webViewUsage: UInt64
    let pressureLevel: MemoryPressureLevel
    let optimizationCount: Int
    let lastOptimizationTime: Date?
    
    // 保持向后兼容
    init(totalUsage: UInt64,
         cacheUsage: UInt64,
         thumbnailUsage: UInt64,
         webViewUsage: UInt64 = 0,
         pressureLevel: MemoryPressureLevel,
         optimizationCount: Int,
         lastOptimizationTime: Date?) {
        self.totalUsage = totalUsage
        self.cacheUsage = cacheUsage
        self.thumbnailUsage = thumbnailUsage
        self.webViewUsage = webViewUsage
        self.pressureLevel = pressureLevel
        self.optimizationCount = optimizationCount
        self.lastOptimizationTime = lastOptimizationTime
    }
    
    var formattedTotalUsage: String {
        ByteCountFormatter.string(fromByteCount: Int64(totalUsage), countStyle: .memory)
    }
    
    var formattedCacheUsage: String {
        ByteCountFormatter.string(fromByteCount: Int64(cacheUsage), countStyle: .memory)
    }
    
    var formattedThumbnailUsage: String {
        ByteCountFormatter.string(fromByteCount: Int64(thumbnailUsage), countStyle: .memory)
    }
    
    var formattedWebViewUsage: String {
        ByteCountFormatter.string(fromByteCount: Int64(webViewUsage), countStyle: .memory)
    }
    
    // 转换为原始MemoryStatistics以保持兼容性
    var compatibleStatistics: MemoryStatistics {
        return MemoryStatistics(
            totalUsage: totalUsage,
            cacheUsage: cacheUsage,
            thumbnailUsage: thumbnailUsage,
            pressureLevel: pressureLevel,
            optimizationCount: optimizationCount,
            lastOptimizationTime: lastOptimizationTime
        )
    }
}

// MARK: - 关联键定义
private struct AssociatedKeys {
    static var predictionModel: UInt8 = 0
    static var webViewManager: UInt8 = 1
} 