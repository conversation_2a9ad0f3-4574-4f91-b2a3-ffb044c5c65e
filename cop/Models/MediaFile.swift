//
//  MediaFile.swift
//  cop
//
//  Created by 阿亮 on 2025/5/31.
//

import Foundation
import CoreData
import UIKit

// MARK: - 媒体文件类型枚举
enum MediaType: String, CaseIterable {
    case image = "image"
    case video = "video"
    
    var displayName: String {
        switch self {
        case .image: return "图片"
        case .video: return "视频"
        }
    }
}

// MARK: - 媒体文件信息结构体
struct MediaFileInfo: Hashable, Equatable {
    let id: UUID
    let name: String
    let type: MediaType
    let fileSize: Int64
    let creationDate: Date
    let modificationDate: Date
    let localURL: URL
    let thumbnailURL: URL?
    let folderPath: String
    let duration: TimeInterval? // 仅用于视频
    let dimensions: CGSize? // 图片/视频尺寸
    
    // MARK: - Hashable 实现
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    // MARK: - Equatable 实现
    static func == (lhs: MediaFileInfo, rhs: MediaFileInfo) -> Bool {
        return lhs.id == rhs.id
    }
    
    // MARK: - 虚拟化支持
    var estimatedSize: CGSize {
        return CGSize(width: 150, height: 150)
    }
    
    // 格式化文件大小
    var formattedFileSize: String {
        ByteCountFormatter.string(fromByteCount: fileSize, countStyle: .file)
    }
    
    // 格式化持续时间
    var formattedDuration: String? {
        guard let duration = duration else { return nil }
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute, .second]
        formatter.unitsStyle = .positional
        formatter.zeroFormattingBehavior = .pad
        return formatter.string(from: duration)
    }
    
    // 格式化创建日期
    var formattedDateCreated: String? {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: creationDate)
    }
    
    // 格式化修改日期
    var formattedDateModified: String? {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: modificationDate)
    }
    
    // 相对时间格式
    var relativeTimeString: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: creationDate, relativeTo: Date())
    }
}

// MARK: - 文件夹信息结构体
struct FolderInfo: Hashable, Equatable {
    let id: UUID = UUID() // 为虚拟化支持添加唯一标识符
    let name: String
    let path: String
    let mediaCount: Int
    let fileCount: Int // 总文件数量（包括非媒体文件）
    let totalSize: Int64
    let lastModified: Date
    let thumbnail: URL? // 文件夹封面（第一个媒体的缩略图）
    
    var formattedTotalSize: String {
        ByteCountFormatter.string(fromByteCount: totalSize, countStyle: .file)
    }
    
    // MARK: - Hashable 实现
    func hash(into hasher: inout Hasher) {
        hasher.combine(path) // 使用路径作为唯一性标识
    }
    
    // MARK: - Equatable 实现
    static func == (lhs: FolderInfo, rhs: FolderInfo) -> Bool {
        return lhs.path == rhs.path
    }
    
    // MARK: - 虚拟化支持
    var estimatedSize: CGSize {
        return CGSize(width: 200, height: 240) // 文件夹卡片的估算尺寸
    }
}

// MARK: - 媒体文件排序选项
enum MediaSortOption: String, CaseIterable {
    case name = "name"
    case dateCreated = "dateCreated"
    case dateModified = "dateModified"
    case fileSize = "fileSize"
    case type = "type"
    
    var displayName: String {
        switch self {
        case .name: return "文件名"
        case .dateCreated: return "创建时间"
        case .dateModified: return "修改时间"
        case .fileSize: return "文件大小"
        case .type: return "文件类型"
        }
    }
}

// MARK: - 排序方向
enum SortDirection: String, CaseIterable {
    case ascending = "ascending"
    case descending = "descending"
    
    var displayName: String {
        switch self {
        case .ascending: return "升序"
        case .descending: return "降序"
        }
    }
}

// MARK: - 缩略图质量枚举
enum ThumbnailQuality: String, CaseIterable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    
    var displayName: String {
        switch self {
        case .low: return "低质量"
        case .medium: return "中等质量"
        case .high: return "高质量"
        }
    }
    
    var size: CGSize {
        switch self {
        case .low: return CGSize(width: 100, height: 100)
        case .medium: return CGSize(width: 200, height: 200)
        case .high: return CGSize(width: 400, height: 400)
        }
    }
    
    var jpegQuality: Double {
        switch self {
        case .low: return 0.6
        case .medium: return 0.8
        case .high: return 0.9
        }
    }
} 