import SwiftUI
import UIKit

// MARK: - 虚拟化配置
struct VirtualizationConfig {
    let itemSize: CGSize
    let spacing: CGFloat
    let bufferSize: Int // 缓冲区大小（超出可见范围的项目数量）
    let preloadDistance: CGFloat // 预加载距离
    
    static let `default` = VirtualizationConfig(
        itemSize: CGSize(width: 150, height: 150),
        spacing: 8,
        bufferSize: 10,
        preloadDistance: 200
    )
}

// MARK: - 虚拟化项目协议
protocol VirtualizableItem: Identifiable, Hashable {
    var id: UUID { get }
    var estimatedSize: CGSize { get }
}

// MARK: - 媒体文件虚拟化适配
extension MediaFileInfo: VirtualizableItem {
    // estimatedSize 属性在 MediaFile.swift 中已定义
}

// MARK: - 文件夹虚拟化适配
extension FolderInfo: VirtualizableItem {
    // estimatedSize 属性在 MediaFile.swift 中已定义
}

// MARK: - 滚动偏移偏好键
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGPoint = .zero
    
    static func reduce(value: inout CGPoint, nextValue: () -> CGPoint) {
        value = nextValue()
    }
}

// MARK: - 虚拟化媒体网格 - 真正的虚拟化实现
struct VirtualizedMediaGrid<Item: VirtualizableItem, Content: View>: View {
    let items: [Item]
    let config: VirtualizationConfig
    let content: (Item) -> Content
    
    @State private var visibleRange: Range<Int> = 0..<0
    @State private var scrollOffset: CGPoint = .zero
    @State private var containerSize: CGSize = .zero
    @State private var preloadedItems: Set<UUID> = []
    
    private let itemCache = ItemCache<Item>()
    
    init(
        items: [Item],
        config: VirtualizationConfig = .default,
        @ViewBuilder content: @escaping (Item) -> Content
    ) {
        self.items = items
        self.config = config
        self.content = content
    }
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                ZStack(alignment: .topLeading) {
                    // 总高度占位符
                    Rectangle()
                        .fill(Color.clear)
                        .frame(width: 1, height: totalContentHeight)
                    
                    // 可见项目绝对定位布局
                    ForEach(visibleItemsWithPositions, id: \.item.id) { itemPosition in
                        content(itemPosition.item)
                            .frame(
                                width: config.itemSize.width,
                                height: config.itemSize.height
                            )
                            .position(
                                x: itemPosition.position.x + config.itemSize.width / 2,
                                y: itemPosition.position.y + config.itemSize.height / 2
                            )
                            .onAppear {
                                preloadNearbyItems(for: itemPosition.item)
                            }
                    }
                }
            }
            .coordinateSpace(name: "scroll")
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { offset in
                scrollOffset = offset
                updateVisibleRange(for: geometry)
            }
            .background(
                GeometryReader { proxy in
                    Color.clear.preference(
                        key: ScrollOffsetPreferenceKey.self,
                        value: proxy.frame(in: .named("scroll")).origin
                    )
                }
            )
            .onAppear {
                containerSize = geometry.size
                updateVisibleRange(for: geometry)
            }
            .onChange(of: geometry.size) { _, newSize in
                containerSize = newSize
                updateVisibleRange(for: geometry)
            }
            .onChange(of: items.count) { _, _ in
                updateVisibleRange(for: geometry)
            }
        }
    }
    
    // MARK: - 计算属性
    private var columnsPerRow: Int {
        max(1, Int((containerSize.width + config.spacing) / (config.itemSize.width + config.spacing)))
    }
    
    private var rowsTotal: Int {
        guard !items.isEmpty else { return 0 }
        return Int(ceil(Double(items.count) / Double(columnsPerRow)))
    }
    
    private var itemHeight: CGFloat {
        config.itemSize.height + config.spacing
    }
    
    private var totalContentHeight: CGFloat {
        CGFloat(rowsTotal) * itemHeight
    }
    
    private var visibleItems: ArraySlice<Item> {
        guard visibleRange.lowerBound < items.count,
              visibleRange.upperBound <= items.count else {
            return ArraySlice<Item>()
        }
        return items[visibleRange]
    }
    
    // MARK: - 项目位置信息
    private struct ItemPosition {
        let item: Item
        let position: CGPoint
    }
    
    private var visibleItemsWithPositions: [ItemPosition] {
        guard !visibleItems.isEmpty else { return [] }
        
        var positions: [ItemPosition] = []
        let leftPadding = config.spacing
        
        for (index, item) in visibleItems.enumerated() {
            let absoluteIndex = visibleRange.lowerBound + index
            let row = absoluteIndex / columnsPerRow
            let column = absoluteIndex % columnsPerRow
            
            let x = leftPadding + CGFloat(column) * (config.itemSize.width + config.spacing)
            let y = CGFloat(row) * itemHeight
            
            positions.append(ItemPosition(
                item: item,
                position: CGPoint(x: x, y: y)
            ))
        }
        
        return positions
    }
    
    // MARK: - 可见范围更新
    private func updateVisibleRange(for geometry: GeometryProxy) {
        let viewportHeight = geometry.size.height
        let scrollY = -scrollOffset.y
        
        // 计算可见行范围
        let startRow = max(0, Int((scrollY - config.preloadDistance) / itemHeight))
        let endRow = min(rowsTotal, Int(ceil((scrollY + viewportHeight + config.preloadDistance) / itemHeight)))
        
        // 转换为项目索引范围
        let startIndex = max(0, startRow * columnsPerRow - config.bufferSize)
        let endIndex = min(items.count, endRow * columnsPerRow + config.bufferSize)
        
        let newRange = startIndex..<endIndex
        
        if newRange != visibleRange {
            visibleRange = newRange
            
            // 清理不再可见的项目缓存
            cleanupInvisibleItems()
        }
    }
    
    // MARK: - 预加载管理
    private func preloadNearbyItems(for item: Item) {
        guard let itemIndex = items.firstIndex(where: { $0.id == item.id }) else { return }
        
        Task { @MainActor in
            let preloadStart = max(0, itemIndex - config.bufferSize)
            let preloadEnd = min(items.count, itemIndex + config.bufferSize)
            
            for index in preloadStart..<preloadEnd {
                let item = items[index]
                
                if !preloadedItems.contains(item.id) {
                    await preloadItem(item)
                    preloadedItems.insert(item.id)
                }
            }
            
            // 清理远距离的预加载项
            cleanupDistantPreloadedItems(currentIndex: itemIndex)
        }
    }
    
    private func preloadItem(_ item: Item) async {
        // 实现具体的预加载逻辑
        if let mediaFile = item as? MediaFileInfo {
            // 预加载缩略图
            _ = await OptimizedThumbnailManager.shared.getThumbnail(
                for: mediaFile.id,
                size: .medium
            )
        }
    }
    
    private func cleanupInvisibleItems() {
        // 从缓存中移除不再可见的项目
        let visibleIds = Set(visibleItems.map { $0.id })
        itemCache.cleanup(keepingOnly: visibleIds)
    }
    
    private func cleanupDistantPreloadedItems(currentIndex: Int) {
        let maxDistance = config.bufferSize * 3 // 保持3倍缓冲区大小的预加载项
        
        preloadedItems = preloadedItems.filter { itemId in
            guard let itemIndex = items.firstIndex(where: { $0.id == itemId }) else {
                return false
            }
            return abs(itemIndex - currentIndex) <= maxDistance
        }
    }
}

// MARK: - 项目缓存
private class ItemCache<Item: VirtualizableItem> {
    private var cache: [String: Any] = [:]
    private let maxCacheSize = 200
    
    func store<T>(_ value: T, for itemId: UUID, key: String) {
        let cacheKey = "\(itemId.uuidString)_\(key)"
        cache[cacheKey] = value
        
        // 限制缓存大小
        if cache.count > maxCacheSize {
            let keysToRemove = Array(cache.keys.prefix(cache.count - maxCacheSize))
            for key in keysToRemove {
                cache.removeValue(forKey: key)
            }
        }
    }
    
    func retrieve<T>(_ type: T.Type, for itemId: UUID, key: String) -> T? {
        let cacheKey = "\(itemId.uuidString)_\(key)"
        return cache[cacheKey] as? T
    }
    
    func cleanup(keepingOnly visibleIds: Set<UUID>) {
        // 移除不在可见范围内的缓存项
        let keepKeys = Set(visibleIds.map { "\($0.uuidString)" })
        cache = cache.filter { key, _ in
            keepKeys.contains(where: { key.hasPrefix($0) })
        }
        
        // 如果缓存过大，强制清理
        if cache.count > maxCacheSize * 2 {
            cache.removeAll()
        }
    }
}

// MARK: - 性能优化的媒体卡片组件
struct OptimizedMediaCard: View {
    let mediaFile: MediaFileInfo
    let size: CGSize
    
    @State private var thumbnailImage: UIImage?
    @State private var isLoading = false
    
    var body: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color(.systemGray6))
            .overlay(
                Group {
                    if let thumbnailImage = thumbnailImage {
                        Image(uiImage: thumbnailImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .clipped()
                    } else if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: mediaFile.type == .video ? "video.fill" : "photo.fill")
                            .font(.system(size: size.width * 0.3))
                            .foregroundColor(.secondary)
                    }
                }
            )
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .overlay(
                // 视频时长标签
                Group {
                    if mediaFile.type == .video,
                       let duration = mediaFile.formattedDuration {
                        HStack {
                            Spacer()
                            VStack {
                                Spacer()
                                Text(duration)
                                    .font(.caption2)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.black.opacity(0.7))
                                    .clipShape(RoundedRectangle(cornerRadius: 4))
                                    .padding(.trailing, 6)
                                    .padding(.bottom, 6)
                            }
                        }
                    }
                },
                alignment: .bottomTrailing
            )
            .onAppear {
                loadThumbnail()
            }
            .frame(width: size.width, height: size.height)
    }
    
    private func loadThumbnail() {
        guard thumbnailImage == nil && !isLoading else { return }
        
        isLoading = true
        
        Task { @MainActor in
            // 使用优化的缩略图管理器
            if let image = await OptimizedThumbnailManager.shared.getThumbnail(
                for: mediaFile.id,
                size: .medium
            ) {
                thumbnailImage = image
            }
            
            isLoading = false
        }
    }
}

// MARK: - 虚拟化媒体网格的便利视图
struct MediaLibraryVirtualizedGrid: View {
    let mediaFiles: [MediaFileInfo]
    let onItemTapped: (MediaFileInfo) -> Void
    
    var body: some View {
        VirtualizedMediaGrid(
            items: mediaFiles,
            config: VirtualizationConfig(
                itemSize: CGSize(width: 150, height: 150),
                spacing: 8,
                bufferSize: 20,
                preloadDistance: 300
            )
        ) { mediaFile in
            OptimizedMediaCard(
                mediaFile: mediaFile,
                size: CGSize(width: 150, height: 150)
            )
            .onTapGesture {
                onItemTapped(mediaFile)
            }
        }
    }
}

// MARK: - 高性能虚拟化列表
struct VirtualizedMediaList<Item: VirtualizableItem, Content: View>: View {
    let items: [Item]
    let itemHeight: CGFloat
    let content: (Item) -> Content
    
    @State private var visibleRange: Range<Int> = 0..<0
    @State private var scrollOffset: CGPoint = .zero
    
    private let bufferSize = 10
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                ZStack(alignment: .topLeading) {
                    // 总高度占位符
                    Rectangle()
                        .fill(Color.clear)
                        .frame(width: 1, height: CGFloat(items.count) * itemHeight)
                    
                    // 可见项目
                    ForEach(Array(visibleItems.enumerated()), id: \.element.id) { index, item in
                        content(item)
                            .frame(height: itemHeight)
                            .position(
                                x: geometry.size.width / 2,
                                y: CGFloat(visibleRange.lowerBound + index) * itemHeight + itemHeight / 2
                            )
                    }
                }
            }
            .coordinateSpace(name: "scroll")
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { offset in
                scrollOffset = offset
                updateVisibleRange(for: geometry)
            }
            .background(
                GeometryReader { proxy in
                    Color.clear.preference(
                        key: ScrollOffsetPreferenceKey.self,
                        value: proxy.frame(in: .named("scroll")).origin
                    )
                }
            )
            .onAppear {
                updateVisibleRange(for: geometry)
            }
        }
    }
    
    private var visibleItems: ArraySlice<Item> {
        guard visibleRange.lowerBound < items.count,
              visibleRange.upperBound <= items.count else {
            return ArraySlice<Item>()
        }
        return items[visibleRange]
    }
    
    private func updateVisibleRange(for geometry: GeometryProxy) {
        let viewportHeight = geometry.size.height
        let scrollY = -scrollOffset.y
        
        let startIndex = max(0, Int(scrollY / itemHeight) - bufferSize)
        let endIndex = min(items.count, Int((scrollY + viewportHeight) / itemHeight) + bufferSize)
        
        visibleRange = startIndex..<endIndex
    }
}

// MARK: - 预览
struct VirtualizedMediaGrid_Previews: PreviewProvider {
    static var previews: some View {
        MediaLibraryVirtualizedGrid(
            mediaFiles: [],
            onItemTapped: { _ in }
        )
        .preferredColorScheme(.light)
    }
}

// MARK: - 静态网格布局（用于少量固定项目）
struct StaticGrid<Content: View>: View {
    let columns: Int
    let spacing: CGFloat
    let content: () -> Content
    
    init(columns: Int, spacing: CGFloat = 12, @ViewBuilder content: @escaping () -> Content) {
        self.columns = columns
        self.spacing = spacing
        self.content = content
    }
    
    var body: some View {
        let items = extractItems(from: content())
        
        VStack(spacing: spacing) {
            ForEach(0..<numberOfRows(for: items.count), id: \.self) { row in
                HStack(spacing: spacing) {
                    ForEach(0..<columns, id: \.self) { column in
                        let index = row * columns + column
                        if index < items.count {
                            items[index]
                        } else {
                            Spacer()
                                .frame(maxWidth: .infinity)
                        }
                    }
                }
            }
        }
    }
    
    private func numberOfRows(for itemCount: Int) -> Int {
        return (itemCount + columns - 1) / columns
    }
    
    private func extractItems(from content: Content) -> [AnyView] {
        // 这是一个简化实现，在实际使用中，SwiftUI会自动处理子视图
        // 这里返回空数组作为占位符
        return []
    }
}

// MARK: - 简化的卡片网格布局（通用）
struct SimpleCardGrid<Data: RandomAccessCollection, Content: View>: View where Data.Element: Identifiable {
    let data: Data
    let columns: Int
    let spacing: CGFloat
    let content: (Data.Element) -> Content
    
    init(
        data: Data,
        columns: Int,
        spacing: CGFloat = 12,
        @ViewBuilder content: @escaping (Data.Element) -> Content
    ) {
        self.data = data
        self.columns = columns
        self.spacing = spacing
        self.content = content
    }
    
    var body: some View {
        let items = Array(data)
        let numberOfRows = (items.count + columns - 1) / columns
        
        VStack(spacing: spacing) {
            ForEach(0..<numberOfRows, id: \.self) { row in
                HStack(spacing: spacing) {
                    ForEach(0..<columns, id: \.self) { column in
                        let index = row * columns + column
                        if index < items.count {
                            content(items[index])
                                .frame(maxWidth: .infinity)
                        } else {
                            Color.clear
                                .frame(maxWidth: .infinity)
                        }
                    }
                }
            }
        }
    }
}