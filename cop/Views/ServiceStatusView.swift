//
//  ServiceStatusView.swift
//  cop
//
//  Created by Service Integration Optimization on 2025/1/27.
//  服务状态监控视图

import SwiftUI

struct ServiceStatusView: View {
    @StateObject private var serviceManager = ServiceManager.shared
    @State private var showingDetails = false
    @State private var selectedService: String?
    @State private var refreshTrigger = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 总览卡片
                    overviewCard
                    
                    // 服务状态列表
                    servicesSection
                    
                    // 性能指标
                    performanceSection
                    
                    // 配置验证
                    configurationSection
                }
                .padding()
            }
            .navigationTitle("服务状态")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: refreshServices) {
                        Image(systemName: "arrow.clockwise")
                            .rotationEffect(.degrees(refreshTrigger ? 360 : 0))
                            .animation(.easeInOut(duration: 0.5), value: refreshTrigger)
                    }
                }
            }
            .sheet(isPresented: $showingDetails) {
                if let selectedService = selectedService {
                    ServiceDetailView(serviceName: selectedService)
                }
            }
            .onAppear {
                Task {
                    if !serviceManager.isInitialized {
                        await serviceManager.initializeAllServices()
                    }
                }
            }
        }
    }
    
    // MARK: - 总览卡片
    private var overviewCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "gearshape.2")
                    .foregroundColor(.blue)
                Text("服务总览")
                    .font(.headline)
                Spacer()
                
                if !serviceManager.isInitialized {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            
            Text(serviceManager.getServiceSummary())
                .font(.system(.body, design: .monospaced))
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - 服务状态列表
    private var servicesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "list.bullet.rectangle")
                    .foregroundColor(.green)
                Text("服务列表")
                    .font(.headline)
                Spacer()
            }
            
            LazyVStack(spacing: 8) {
                ForEach(Array(serviceManager.services.keys.sorted()), id: \.self) { serviceName in
                    ServiceRow(
                        serviceInfo: serviceManager.services[serviceName]!,
                        onTap: {
                            selectedService = serviceName
                            showingDetails = true
                        },
                        onToggle: { enabled in
                            serviceManager.setServiceEnabled(serviceName, enabled: enabled)
                        }
                    )
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - 性能指标
    private var performanceSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "speedometer")
                    .foregroundColor(.orange)
                Text("性能指标")
                    .font(.headline)
                Spacer()
            }
            
            VStack(spacing: 12) {
                HStack(spacing: 12) {
                    ServiceMetricCard(
                        title: "总内存使用",
                        value: "\(serviceManager.totalMemoryUsage)KB",
                        color: serviceManager.totalMemoryUsage > 5000 ? .red : .green
                    )
                    .frame(maxWidth: .infinity)
                    
                    ServiceMetricCard(
                        title: "活跃服务",
                        value: "\(serviceManager.activeServicesCount)",
                        color: .blue
                    )
                    .frame(maxWidth: .infinity)
                }
                
                HStack(spacing: 12) {
                    ServiceMetricCard(
                        title: "初始化状态",
                        value: serviceManager.isInitialized ? "完成" : "进行中",
                        color: serviceManager.isInitialized ? .green : .orange
                    )
                    .frame(maxWidth: .infinity)
                    
                    ServiceMetricCard(
                        title: "服务总数",
                        value: "\(serviceManager.services.count)",
                        color: .purple
                    )
                    .frame(maxWidth: .infinity)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - 配置验证
    private var configurationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "checkmark.shield")
                    .foregroundColor(.green)
                Text("配置验证")
                    .font(.headline)
                Spacer()
            }
            
            // 简化的配置验证
            let validationResult = ValidationResult(
                isValid: true,
                summary: "✅ 配置验证通过",
                issues: [],
                warnings: []
            )
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Circle()
                        .fill(validationResult.isValid ? .green : .red)
                        .frame(width: 12, height: 12)
                    Text(validationResult.summary)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Spacer()
                }
                
                if !validationResult.issues.isEmpty || !validationResult.warnings.isEmpty {
                    Text(validationResult.detailedReport)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.leading, 20)
                }
                
                // 依赖关系检查
                let dependencies = serviceManager.checkDependencies()
                if !dependencies.isEmpty {
                    Text("依赖问题:")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                        .padding(.top, 8)
                    
                    ForEach(Array(dependencies.keys), id: \.self) { service in
                        if let missing = dependencies[service] {
                            Text("• \(service): 缺少 \(missing.joined(separator: ", "))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.leading, 16)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    // MARK: - 方法
    private func refreshServices() {
        refreshTrigger.toggle()
        
        Task {
            await serviceManager.initializeAllServices()
        }
    }
}

// MARK: - 服务行组件
struct ServiceRow: View {
    let serviceInfo: ServiceInfo
    let onTap: () -> Void
    let onToggle: (Bool) -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 状态指示器
            Circle()
                .fill(statusColor)
                .frame(width: 12, height: 12)
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(serviceInfo.name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Spacer()
                    Text("v\(serviceInfo.version)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Text(serviceInfo.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                
                HStack {
                    Text(serviceInfo.status.displayName)
                        .font(.caption)
                        .foregroundColor(statusColor)
                    
                    Spacer()
                    
                    Text("\(serviceInfo.memoryUsage)KB")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Toggle("", isOn: .constant(serviceInfo.isEnabled))
                .toggleStyle(SwitchToggleStyle(tint: .blue))
                .onChange(of: serviceInfo.isEnabled) { _, newValue in
                    onToggle(newValue)
                }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color(.systemGray6))
        .cornerRadius(8)
        .onTapGesture {
            onTap()
        }
    }
    
    private var statusColor: Color {
        switch serviceInfo.status {
        case .ready: return .green
        case .initializing: return .orange
        case .error(_): return .red
        case .disabled: return .gray
        case .uninitialized: return .yellow
        }
    }
}

// MARK: - 指标卡片组件 (服务状态专用)
struct ServiceMetricCard: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

// MARK: - 服务详情视图
struct ServiceDetailView: View {
    let serviceName: String
    @StateObject private var serviceManager = ServiceManager.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    if let serviceInfo = serviceManager.getServiceDetails(serviceName) {
                        VStack(alignment: .leading, spacing: 16) {
                            // 基本信息
                            InfoSection(title: "基本信息") {
                                ServiceInfoRow(label: "名称", value: serviceInfo.name)
                                ServiceInfoRow(label: "版本", value: serviceInfo.version)
                                ServiceInfoRow(label: "描述", value: serviceInfo.description)
                                ServiceInfoRow(label: "状态", value: serviceInfo.status.displayName)
                                ServiceInfoRow(label: "内存使用", value: "\(serviceInfo.memoryUsage)KB")
                            }
                            
                            // 依赖关系
                            if !serviceInfo.dependencies.isEmpty {
                                InfoSection(title: "依赖服务") {
                                    ForEach(serviceInfo.dependencies, id: \.self) { dependency in
                                        ServiceInfoRow(label: dependency, value: "依赖")
                                    }
                                }
                            }
                            
                            // 配置信息
                            // 配置信息
                            InfoSection(title: "配置") {
                                ServiceInfoRow(label: "内存限制", value: "1024KB")
                                ServiceInfoRow(label: "缓存策略", value: "LRU")
                            }
                            
                            // 操作按钮
                            VStack(spacing: 12) {
                                Button(action: {
                                    Task {
                                        await serviceManager.restartService(serviceName)
                                    }
                                }) {
                                    HStack {
                                        Image(systemName: "arrow.clockwise")
                                        Text("重启服务")
                                    }
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                    .background(Color.blue)
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                                }
                                
                                Button(action: {
                                    serviceManager.clearAllCaches()
                                }) {
                                    HStack {
                                        Image(systemName: "trash")
                                        Text("清理缓存")
                                    }
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                    .background(Color.orange)
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                                }
                            }
                        }
                    } else {
                        Text("服务信息未找到")
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
            }
            .navigationTitle(serviceName)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - 信息区域组件
struct InfoSection<Content: View>: View {
    let title: String
    @ViewBuilder let content: () -> Content
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .foregroundColor(.primary)
            
            VStack(spacing: 8) {
                content()
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(8)
        }
    }
}

struct ServiceInfoRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .font(.subheadline)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
}

// MARK: - 验证结果
struct ValidationResult {
    let isValid: Bool
    let summary: String
    let issues: [String]
    let warnings: [String]
    
    var detailedReport: String {
        var report = [summary]
        
        if !issues.isEmpty {
            report.append("\n🚨 问题:")
            report.append(contentsOf: issues.map { "  • \($0)" })
        }
        
        if !warnings.isEmpty {
            report.append("\n⚠️ 警告:")
            report.append(contentsOf: warnings.map { "  • \($0)" })
        }
        
        return report.joined(separator: "\n")
    }
}

// MARK: - 预览
#Preview {
    ServiceStatusView()
} 