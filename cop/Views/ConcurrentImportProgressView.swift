//
//  ConcurrentImportProgressView.swift
//  cop
//
//  Created by 阿亮 on 2025/6/19.
//  第三阶段性能优化 - 并发导入进度视图
//

import SwiftUI

struct ConcurrentImportProgressView: View {
    @ObservedObject var importService = ConcurrentMediaImportService.shared
    @Environment(\.dismiss) private var dismiss
    @State private var showErrorDetails = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 整体进度区域
                overallProgressSection
                
                // 批次进度区域
                if !importService.currentBatches.isEmpty {
                    batchProgressSection
                }
                
                // 控制按钮区域
                controlButtonsSection
                
                // 错误信息区域
                if !importService.progress.errors.isEmpty {
                    errorSection
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("并发导入进度")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("完成") {
                        dismiss()
                    }
                    .disabled(importService.isImporting)
                }
            }
        }
    }
    
    // MARK: - 整体进度区域
    private var overallProgressSection: some View {
        VStack(spacing: 16) {
            // 主要进度条
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("总体进度")
                        .font(.headline)
                    
                    Spacer()
                    
                    Text("\(importService.progress.processedFiles)/\(importService.progress.totalFiles)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                ProgressView(value: importService.progress.overallProgress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                    .scaleEffect(y: 2)
                
                HStack {
                    Text("\(Int(importService.progress.overallProgress * 100))%")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    if let eta = importService.progress.estimatedTimeRemaining {
                        Text("剩余时间: \(formatTimeInterval(eta))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
            
            // 统计信息
            HStack(spacing: 20) {
                StatisticView(
                    title: "成功",
                    value: "\(importService.progress.successfulFiles)",
                    color: .green
                )
                
                StatisticView(
                    title: "失败",
                    value: "\(importService.progress.failedFiles)",
                    color: .red
                )
                
                StatisticView(
                    title: "速度",
                    value: String(format: "%.1f/秒", importService.progress.avgProcessingSpeed),
                    color: .blue
                )
            }
            
            // 当前文件信息
            if !importService.progress.currentFile.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("当前处理文件")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(importService.progress.currentFile)
                        .font(.footnote)
                        .lineLimit(2)
                        .truncationMode(.middle)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - 批次进度区域
    private var batchProgressSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("批次进度 (\(importService.progress.currentBatch)/\(importService.progress.totalBatches))")
                .font(.headline)
                .padding(.horizontal)
            
            ScrollView {
                LazyVStack(spacing: 8) {
                    ForEach(importService.currentBatches, id: \.id) { batch in
                        BatchProgressCard(batch: batch)
                    }
                }
                .padding(.horizontal)
            }
            .frame(maxHeight: 200)
        }
    }
    
    // MARK: - 控制按钮区域
    private var controlButtonsSection: some View {
        HStack(spacing: 16) {
            if importService.canPause {
                Button(action: {
                    importService.pauseImport()
                }) {
                    Label("暂停", systemImage: "pause.fill")
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)
                .tint(.orange)
            }
            
            if importService.canResume {
                Button(action: {
                    importService.resumeImport()
                }) {
                    Label("恢复", systemImage: "play.fill")
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)
                .tint(.green)
            }
            
            if importService.isImporting {
                Button(action: {
                    importService.cancelImport()
                }) {
                    Label("取消", systemImage: "stop.fill")
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)
                .tint(.red)
            }
        }
        .padding(.horizontal)
    }
    
    // MARK: - 错误信息区域
    private var errorSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("错误信息 (\(importService.progress.errors.count))")
                    .font(.headline)
                    .foregroundColor(.red)
                
                Spacer()
                
                Button(showErrorDetails ? "收起" : "展开") {
                    withAnimation {
                        showErrorDetails.toggle()
                    }
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            .padding(.horizontal)
            
            if showErrorDetails {
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 4) {
                        ForEach(importService.progress.errors) { error in
                            ErrorRowView(error: error)
                        }
                    }
                    .padding(.horizontal)
                }
                .frame(maxHeight: 150)
                .background(Color(.systemGray6))
                .cornerRadius(8)
                .padding(.horizontal)
            }
        }
    }
    
    // MARK: - 工具方法
    private func formatTimeInterval(_ interval: TimeInterval) -> String {
        let hours = Int(interval) / 3600
        let minutes = Int(interval) % 3600 / 60
        let seconds = Int(interval) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%d:%02d", minutes, seconds)
        }
    }
}

// MARK: - 统计视图
struct StatisticView: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

// MARK: - 批次进度卡片
struct BatchProgressCard: View {
    let batch: FileBatch
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("批次 \(batch.batchIndex + 1)")
                    .font(.headline)
                
                Spacer()
                
                StatusBadge(status: batch.status)
            }
            
            ProgressView(value: batch.progress)
                .progressViewStyle(LinearProgressViewStyle(tint: statusColor))
            
            HStack {
                Text("\(batch.processedCount + batch.failedCount)/\(batch.files.count) 文件")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                if batch.failedCount > 0 {
                    Text("\(batch.failedCount) 失败")
                        .font(.caption)
                        .foregroundColor(.red)
                }
                
                if let duration = batch.endTime?.timeIntervalSince(batch.startTime ?? Date()) {
                    Text("耗时: \(String(format: "%.1fs", duration))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
    
    private var statusColor: Color {
        switch batch.status {
        case .pending:
            return .gray
        case .processing:
            return .blue
        case .completed:
            return .green
        case .failed:
            return .red
        case .paused:
            return .orange
        }
    }
}

// MARK: - 状态徽章
struct StatusBadge: View {
    let status: BatchStatus
    
    var body: some View {
        Text(statusText)
            .font(.caption2)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 2)
            .background(statusColor.opacity(0.2))
            .foregroundColor(statusColor)
            .cornerRadius(4)
    }
    
    private var statusText: String {
        switch status {
        case .pending:
            return "等待中"
        case .processing:
            return "处理中"
        case .completed:
            return "已完成"
        case .failed:
            return "失败"
        case .paused:
            return "已暂停"
        }
    }
    
    private var statusColor: Color {
        switch status {
        case .pending:
            return .gray
        case .processing:
            return .blue
        case .completed:
            return .green
        case .failed:
            return .red
        case .paused:
            return .orange
        }
    }
}

// MARK: - 错误行视图
struct ErrorRowView: View {
    let error: ImportError
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(error.fileName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Spacer()
                
                Text(formatTime(error.timestamp))
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isExpanded.toggle()
                    }
                }) {
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            if isExpanded {
                VStack(alignment: .leading, spacing: 2) {
                    Text("文件夹: \(error.folderPath)")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    Text("错误: \(error.error.localizedDescription)")
                        .font(.caption2)
                        .foregroundColor(.red)
                }
                .padding(.top, 2)
            }
        }
        .padding(.vertical, 4)
        .padding(.horizontal, 8)
        .background(Color(.systemBackground))
        .cornerRadius(4)
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - 预览
struct ConcurrentImportProgressView_Previews: PreviewProvider {
    static var previews: some View {
        ConcurrentImportProgressView()
    }
} 