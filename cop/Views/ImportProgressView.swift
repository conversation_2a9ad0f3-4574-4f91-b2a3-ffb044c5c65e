//
//  ImportProgressView.swift
//  cop
//
//  Created by 阿亮 on 2025/6/1.
//

import SwiftUI
import Combine

struct ImportProgressView: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    @ObservedObject var importService = MediaImportService.shared
    @State private var showingDocumentPicker = false
    @State private var importHistory: [ImportHistoryItem] = []
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 导入操作区域（始终显示）
                ImportActionSection(
                    showingDocumentPicker: $showingDocumentPicker,
                    isImporting: importService.isImporting
                )
                
                // 当前导入进度区域（紧跟在导入按钮下方）
                if importService.isImporting {
                    CurrentImportSection(importService: importService)
                        .transition(.move(edge: .top).combined(with: .opacity))
                }
                
                // 导入历史
                ImportHistorySection(importHistory: importHistory, viewModel: viewModel)
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 16)
        }
        .animation(.easeInOut(duration: 0.3), value: importService.isImporting)
        .onAppear {
            loadImportHistory()
        }
        .onReceive(importService.$isImporting) { isImporting in
            if !isImporting {
                // 导入完成后延迟一点刷新历史，确保数据已保存
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    loadImportHistory()
                }
            }
        }
        .sheet(isPresented: $showingDocumentPicker) {
            DocumentPickerView { url in
                Task {
                    await viewModel.importMediaFolder(from: url)
                    // 导入完成后立即刷新历史
                    loadImportHistory()
                }
            }
        }
    }
    
    private func loadImportHistory() {
        // 从UserDefaults加载导入历史
        if let data = UserDefaults.standard.data(forKey: "ImportHistory"),
           let history = try? JSONDecoder().decode([ImportHistoryItem].self, from: data) {
            importHistory = history.sorted { $0.importDate > $1.importDate }
        }
    }
}

// MARK: - 当前导入进度区域
struct CurrentImportSection: View {
    @ObservedObject var importService: MediaImportService
    @State private var showingCancelAlert = false
    
    var body: some View {
        VStack(spacing: 16) {
            // 进度卡片
            VStack(spacing: 16) {
                // 卡片标题
                HStack {
                    Image(systemName: "arrow.down.circle.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                    Text("正在导入")
                        .font(.title2)
                        .fontWeight(.semibold)
                    Spacer()
                    
                    // 取消按钮
                    Button("取消") {
                        showingCancelAlert = true
                    }
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
                }
                
                // 总体进度
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("导入进度")
                            .font(.headline)
                        Spacer()
                        Text("\(importService.importProgress.processedCount) / \(importService.importProgress.totalCount)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    // 当前文件信息在进度条上方
                    if !importService.importProgress.currentFile.isEmpty {
                        Text("正在处理: \(importService.importProgress.currentFile)")
                            .font(.caption)
                            .foregroundColor(.blue)
                            .lineLimit(1)
                    }
                    
                    ProgressView(value: importService.importProgress.progress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                        .scaleEffect(y: 2)
                    
                    HStack {
                        Text(String(format: "%.1f%%", importService.importProgress.progress * 100))
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        // 预计剩余时间
                        if importService.importProgress.progress > 0 {
                            Text("预计剩余: \(estimatedTimeRemaining())")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Divider()
                
                // 详细信息网格（修正文件夹数计数）
                VStack(spacing: 12) {
                    HStack(spacing: 12) {
                        InfoItemView(
                            title: "总文件夹数",
                            value: "\(getRealFolderCount()) 个",
                            icon: "folder.fill",
                            color: .blue
                        )
                        .frame(maxWidth: .infinity)
                        
                        InfoItemView(
                            title: "总文件数",
                            value: "\(importService.importProgress.totalCount) 个",
                            icon: "doc.fill",
                            color: .green
                        )
                        .frame(maxWidth: .infinity)
                    }
                    
                    HStack(spacing: 12) {
                        InfoItemView(
                            title: "处理速度",
                            value: "\(getProcessingSpeed()) 文件/秒",
                            icon: "speedometer",
                            color: .orange
                        )
                        .frame(maxWidth: .infinity)
                        
                        // 占位符保持布局平衡
                        Color.clear
                            .frame(maxWidth: .infinity)
                    }
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(UIColor.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color(UIColor.systemGray5), lineWidth: 1)
                    )
            )
        }
        .alert("取消导入", isPresented: $showingCancelAlert) {
            Button("继续导入", role: .cancel) { }
            Button("确认取消", role: .destructive) {
                cancelImport()
            }
        } message: {
            Text("确定要取消当前的导入操作吗？已导入的文件将保留。")
        }
    }
    
    private func getRealFolderCount() -> Int {
        // 从MediaImportService获取真实的叶子文件夹数
        return importService.realFolderCount
    }
    
    private func cancelImport() {
        // 实现取消导入功能
        importService.cancelImport()
    }
    
    private func estimatedTimeRemaining() -> String {
        if importService.importProgress.progress > 0.1 {
            let remainingProgress = 1.0 - importService.importProgress.progress
            let elapsedTime = Date().timeIntervalSince(importService.importProgress.startTime ?? Date())
            let estimatedTotal = elapsedTime / importService.importProgress.progress
            let remaining = estimatedTotal * remainingProgress
            
            if remaining < 60 {
                return "\(Int(remaining))秒"
            } else {
                return "\(Int(remaining / 60))分钟"
            }
        }
        return "计算中..."
    }
    
    private func getProcessingSpeed() -> String {
        if let startTime = importService.importProgress.startTime,
           importService.importProgress.processedCount > 0 {
            let elapsed = Date().timeIntervalSince(startTime)
            let speed = Double(importService.importProgress.processedCount) / elapsed
            return String(format: "%.1f", speed)
        }
        return "0.0"
    }
}

// MARK: - 信息项视图
struct InfoItemView: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 6) {
            HStack {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(color)
                Text(title)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Spacer()
            }
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
                .multilineTextAlignment(.leading)
                .lineLimit(2)
        }
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(UIColor.systemGray6))
        )
    }
}

// MARK: - 导入操作区域
struct ImportActionSection: View {
    @Binding var showingDocumentPicker: Bool
    let isImporting: Bool
    
    var body: some View {
        VStack(spacing: 16) {
            // 主导入按钮
            Button(action: {
                showingDocumentPicker = true
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("导入媒体文件夹")
                            .font(.headline)
                            .fontWeight(.semibold)
                        Text("选择包含照片和视频的文件夹进行导入")
                            .font(.caption)
                            .opacity(0.7)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.footnote)
                        .fontWeight(.medium)
                        .opacity(0.6)
                }
                .foregroundColor(.white)
                .padding(20)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [.blue, .blue.opacity(0.8)]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(16)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
}

// MARK: - 导入历史区域
struct ImportHistorySection: View {
    let importHistory: [ImportHistoryItem]
    let viewModel: MediaLibraryViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "clock.fill")
                    .font(.title2)
                    .foregroundColor(.purple)
                Text("导入历史")
                    .font(.title2)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            if importHistory.isEmpty {
                EmptyHistoryView()
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(importHistory, id: \.id) { item in
                        ImportHistoryRow(item: item, viewModel: viewModel)
                    }
                }
            }
        }
    }
}

// MARK: - 空历史视图
struct EmptyHistoryView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "tray")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("暂无导入历史")
                    .font(.headline)
                
                Text("开始导入媒体文件后，历史记录将显示在这里")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(40)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(UIColor.systemGray6))
        )
    }
}

// MARK: - 导入历史行（扁平化设计）
struct ImportHistoryRow: View {
    let item: ImportHistoryItem
    let viewModel: MediaLibraryViewModel
    
    var body: some View {
        VStack(spacing: 0) {
            // 主要内容
            HStack(spacing: 16) {
                // 状态图标
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(item.status.color.opacity(0.15))
                        .frame(width: 56, height: 56)
                    
                    Image(systemName: item.status.icon)
                        .font(.title2)
                        .foregroundColor(item.status.color)
                }
                
                // 导入信息
                VStack(alignment: .leading, spacing: 6) {
                    Text(item.folderName)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .lineLimit(1)
                    
                    // 统计信息网格
                    HStack(spacing: 20) {
                        VStack(alignment: .leading, spacing: 2) {
                            Text("\(item.fileCount)")
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundColor(.blue)
                            Text("文件")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("1")
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundColor(.green)
                            Text("文件夹")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text(ByteCountFormatter.string(fromByteCount: item.totalSize, countStyle: .file))
                                .font(.title3)
                                .fontWeight(.bold)
                                .foregroundColor(.orange)
                            Text("大小")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Text(item.importDate, style: .relative)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // 状态指示器（替代更多按钮）
                VStack(spacing: 4) {
                    Text(item.status.displayName)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(item.status.color)
                    
                    if item.status == .success {
                        Text("导入完成")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    } else if item.status == .failed {
                        Text("导入失败")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    } else {
                        Text("部分完成")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(20)
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(UIColor.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(UIColor.systemGray5), lineWidth: 1)
                )
        )
    }
}

// MARK: - 导入历史项
struct ImportHistoryItem: Codable {
    let id = UUID()
    let folderName: String
    let fileCount: Int
    let totalSize: Int64
    let importDate: Date
    let status: ImportStatus
    
    private enum CodingKeys: String, CodingKey {
        case id, folderName, fileCount, totalSize, importDate, status
    }
}

// MARK: - 导入状态
enum ImportStatus: String, Codable, CaseIterable {
    case success
    case failed
    case partial
    
    var icon: String {
        switch self {
        case .success: return "checkmark.circle.fill"
        case .failed: return "xmark.circle.fill"
        case .partial: return "exclamationmark.circle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .success: return .green
        case .failed: return .red
        case .partial: return .orange
        }
    }
    
    var displayName: String {
        switch self {
        case .success: return "成功"
        case .failed: return "失败"
        case .partial: return "部分完成"
        }
    }
}

#Preview {
    ImportProgressView(viewModel: MediaLibraryViewModel())
} 