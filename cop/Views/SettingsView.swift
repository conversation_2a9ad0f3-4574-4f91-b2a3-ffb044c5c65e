//
//  SettingsView.swift
//  cop
//
//  Created by 阿亮 on 2025/5/31.
//

import SwiftUI

struct SettingsView: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    @State private var showingDeleteAllAlert = false
    @State private var isCleaningCache = false
    @State private var isRegeneratingThumbnails = false
    
    // 导入设置
    @AppStorage("autoCreateFolders") private var autoCreateFolders = true
    @AppStorage("generateThumbnails") private var generateThumbnails = true
    @AppStorage("thumbnailQuality") private var thumbnailQuality: ThumbnailQuality = .medium
    
    // 显示设置
    @AppStorage("defaultSortOption") private var defaultSortOption: MediaSortOption = .dateCreated
    @AppStorage("defaultSortDirection") private var defaultSortDirection: SortDirection = .descending
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 存储管理卡片（顶部）
                StorageOverviewCard(viewModel: viewModel)
                
                // 存储管理操作区域
                StorageManagementSection(
                    viewModel: viewModel, 
                    showingDeleteAllAlert: $showingDeleteAllAlert,
                    isCleaningCache: $isCleaningCache,
                    isRegeneratingThumbnails: $isRegeneratingThumbnails
                )
                
                // 导入设置区域
                ImportSettingsSection(
                    autoCreateFolders: $autoCreateFolders,
                    generateThumbnails: $generateThumbnails,
                    thumbnailQuality: $thumbnailQuality
                )
                
                // 显示设置区域
                DisplaySettingsSection(
                    defaultSortOption: $defaultSortOption,
                    defaultSortDirection: $defaultSortDirection,
                    viewModel: viewModel
                )
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 16)
        }
        .alert("删除所有数据", isPresented: $showingDeleteAllAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                Task {
                    await viewModel.clearAllData()
                }
            }
        } message: {
            Text("确定要删除所有媒体文件和数据吗？此操作无法撤销。")
        }
    }
}

// MARK: - 导入设置区域
struct ImportSettingsSection: View {
    @Binding var autoCreateFolders: Bool
    @Binding var generateThumbnails: Bool
    @Binding var thumbnailQuality: ThumbnailQuality
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeaderView(
                title: "导入设置",
                icon: "square.and.arrow.down",
                color: .blue
            )
            
            VStack(spacing: 12) {
                SettingToggleRow(
                    title: "自动创建文件夹",
                    subtitle: "根据日期自动整理导入的文件",
                    isOn: $autoCreateFolders
                )
                
                SettingToggleRow(
                    title: "自动生成缩略图",
                    subtitle: "为导入的媒体文件生成预览缩略图",
                    isOn: $generateThumbnails
                )
                
                if generateThumbnails {
                    SettingPickerRow(
                        title: "缩略图质量",
                        selection: $thumbnailQuality,
                        options: ThumbnailQuality.allCases
                    )
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(UIColor.systemGray6))
            )
        }
    }
}

// MARK: - 显示设置区域
struct DisplaySettingsSection: View {
    @Binding var defaultSortOption: MediaSortOption
    @Binding var defaultSortDirection: SortDirection
    @ObservedObject var viewModel: MediaLibraryViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeaderView(
                title: "显示设置",
                icon: "eye",
                color: .green
            )
            
            VStack(spacing: 12) {
                SettingPickerRow(
                    title: "默认排序方式",
                    selection: $defaultSortOption,
                    options: MediaSortOption.allCases
                ) {
                    viewModel.sortOption = defaultSortOption
                }
                
                SettingPickerRow(
                    title: "默认排序方向",
                    selection: $defaultSortDirection,
                    options: SortDirection.allCases
                ) {
                    viewModel.sortDirection = defaultSortDirection
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(UIColor.systemGray6))
            )
        }
    }
}

// MARK: - 存储概览卡片
struct StorageOverviewCard: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    
    var body: some View {
        VStack(spacing: 20) {
            // 卡片标题
            HStack {
                Image(systemName: "internaldrive")
                    .font(.title2)
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("应用存储信息")
                        .font(.title3)
                        .fontWeight(.semibold)
                    Text("当前应用占用的存储空间概览")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            // 主要统计信息网格
            VStack(spacing: 16) {
                HStack(spacing: 16) {
                    StatisticCardView(
                        title: "文件夹数",
                        value: "\(getFolderCount())",
                        unit: "个",
                        icon: "folder.fill",
                        color: .purple
                    )
                    .frame(maxWidth: .infinity)
                    
                    StatisticCardView(
                        title: "媒体文件数",
                        value: "\(viewModel.allMediaFiles.count)",
                        unit: "个",
                        icon: "doc.fill",
                        color: .green
                    )
                    .frame(maxWidth: .infinity)
                }
                
                HStack(spacing: 16) {
                    StatisticCardView(
                        title: "总容量",
                        value: formatBytes(getTotalMediaSize()),
                        unit: "",
                        icon: "externaldrive.fill",
                        color: .orange
                    )
                    .frame(maxWidth: .infinity)
                    
                    // 占位符保持布局平衡
                    Color.clear
                        .frame(maxWidth: .infinity)
                }
            }
            
            Divider()
                .padding(.vertical, 8)
            
            // 应用空间占用详情
            VStack(spacing: 12) {
                HStack {
                    Text("应用空间占用")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Spacer()
                    
                    let appSize = getAppStorageSize()
                    Text(formatBytes(appSize))
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                }
                
                // 详细空间分布
                VStack(spacing: 8) {
                    StorageBreakdownRow(
                        title: "媒体文件",
                        size: getTotalMediaSize(),
                        color: .blue
                    )
                    
                    StorageBreakdownRow(
                        title: "缩略图缓存",
                        size: getThumbnailCacheSize(),
                        color: .green
                    )
                    
                    StorageBreakdownRow(
                        title: "应用数据",
                        size: getAppDataSize(),
                        color: .orange
                    )
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(UIColor.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color(UIColor.systemGray5), lineWidth: 1)
                )
        )
    }
    
    private func getTotalMediaSize() -> Int64 {
        return viewModel.allMediaFiles.reduce(0) { $0 + $1.fileSize }
    }
    
    private func getAppStorageSize() -> Int64 {
        // 计算应用总占用空间
        return getTotalMediaSize() + getThumbnailCacheSize() + getAppDataSize()
    }
    
    private func getThumbnailCacheSize() -> Int64 {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let thumbnailsPath = documentsPath.appendingPathComponent("Thumbnails")
        
        guard let enumerator = FileManager.default.enumerator(at: thumbnailsPath, includingPropertiesForKeys: [.fileSizeKey]) else {
            return 0
        }
        
        var totalSize: Int64 = 0
        for case let fileURL as URL in enumerator {
            if let resourceValues = try? fileURL.resourceValues(forKeys: [.fileSizeKey]),
               let fileSize = resourceValues.fileSize {
                totalSize += Int64(fileSize)
            }
        }
        return totalSize
    }
    
    private func getAppDataSize() -> Int64 {
        // 估算应用数据大小（数据库、配置文件等）
        return 1024 * 1024 * 2 // 大约2MB
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
    
    private func getFolderCount() -> Int {
        do {
            let mediaImportService = MediaImportService.shared
            let folders = try mediaImportService.getImportedFoldersSync()
            return folders.count
        } catch {
            return 0
        }
    }
}

// MARK: - 统计卡片视图
struct StatisticCardView: View {
    let title: String
    let value: String
    let unit: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            ZStack {
                Circle()
                    .fill(color.opacity(0.2))
                    .frame(width: 40, height: 40)
                
                Image(systemName: icon)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(color)
            }
            
            VStack(spacing: 2) {
                HStack(spacing: 2) {
                    Text(value)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    if !unit.isEmpty {
                        Text(unit)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(UIColor.systemGray6).opacity(0.5))
        )
    }
}

// MARK: - 存储分布行
struct StorageBreakdownRow: View {
    let title: String
    let size: Int64
    let color: Color
    
    var body: some View {
        HStack {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)
            
            Text(title)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            Spacer()
            
            Text(formatBytes(size))
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
        }
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}

// MARK: - 存储管理区域
struct StorageManagementSection: View {
    @ObservedObject var viewModel: MediaLibraryViewModel
    @Binding var showingDeleteAllAlert: Bool
    @Binding var isCleaningCache: Bool
    @Binding var isRegeneratingThumbnails: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeaderView(
                title: "存储管理操作",
                icon: "gear",
                color: .red
            )
            
            // 存储管理操作
            VStack(spacing: 12) {
                SettingsActionButton(
                    title: "清理缩略图缓存",
                    subtitle: "释放缓存空间",
                    icon: "trash.circle",
                    color: .orange,
                    isLoading: isCleaningCache,
                    action: {
                        Task {
                            isCleaningCache = true
                            await viewModel.clearThumbnailCache()
                            try? await Task.sleep(nanoseconds: 1_000_000_000) // 延迟1秒显示效果
                            isCleaningCache = false
                        }
                    }
                )
                
                SettingsActionButton(
                    title: "重新生成缩略图",
                    subtitle: "重新生成所有媒体文件的缩略图",
                    icon: "arrow.triangle.2.circlepath.circle",
                    color: .blue,
                    isLoading: isRegeneratingThumbnails,
                    action: {
                        Task {
                            isRegeneratingThumbnails = true
                            await viewModel.regenerateThumbnails()
                            try? await Task.sleep(nanoseconds: 1_000_000_000) // 延迟1秒显示效果
                            isRegeneratingThumbnails = false
                        }
                    }
                )
                
                SettingsActionButton(
                    title: "删除所有数据",
                    subtitle: "彻底清除所有媒体文件和数据",
                    icon: "trash.fill",
                    color: .red,
                    isLoading: false,
                    action: {
                        showingDeleteAllAlert = true
                    }
                )
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(UIColor.systemGray6))
            )
        }
    }
}

// MARK: - 区域标题视图
struct SectionHeaderView: View {
    let title: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            Text(title)
                .font(.title2)
                .fontWeight(.semibold)
            Spacer()
        }
    }
}

// MARK: - 设置切换行
struct SettingToggleRow: View {
    let title: String
    let subtitle: String
    @Binding var isOn: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: .blue))
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 设置选择行
struct SettingPickerRow<T: CaseIterable & RawRepresentable & Hashable>: View where T.RawValue == String, T: CustomStringConvertible {
    let title: String
    @Binding var selection: T
    let options: [T]
    let onChange: (() -> Void)?
    
    init(title: String, selection: Binding<T>, options: [T], onChange: (() -> Void)? = nil) {
        self.title = title
        self._selection = selection
        self.options = options
        self.onChange = onChange
    }
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
            
            Spacer()
            
            Picker(title, selection: $selection) {
                ForEach(options, id: \.self) { option in
                    Text(option.description).tag(option)
                }
            }
            .pickerStyle(MenuPickerStyle())
            .onChange(of: selection) {
                onChange?()
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 圆形进度视图
struct CircularProgressView: View {
    let progress: Double
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(Color(UIColor.systemGray5), lineWidth: 4)
            
            Circle()
                .trim(from: 0, to: CGFloat(progress))
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: [.blue, .green]),
                        startPoint: .topTrailing,
                        endPoint: .bottomLeading
                    ),
                    style: StrokeStyle(lineWidth: 4, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 1.0), value: progress)
            
            Text("\(Int(progress * 100))%")
                .font(.caption2)
                .fontWeight(.semibold)
        }
    }
}

// MARK: - 设置操作按钮
struct SettingsActionButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let isLoading: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .tint(color)
                        .frame(width: 24, height: 24)
                } else {
                    Image(systemName: icon)
                        .font(.title3)
                        .foregroundColor(color)
                        .frame(width: 24, height: 24)
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(isLoading ? "处理中..." : subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if !isLoading {
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(UIColor.systemBackground))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isLoading)
    }
}

// MARK: - 扩展协议以支持描述
extension ThumbnailQuality: CustomStringConvertible {
    var description: String {
        return self.displayName
    }
}

extension MediaSortOption: CustomStringConvertible {
    var description: String {
        return self.displayName
    }
}

extension SortDirection: CustomStringConvertible {
    var description: String {
        return self.displayName
    }
}

#Preview {
    SettingsView(viewModel: MediaLibraryViewModel())
} 