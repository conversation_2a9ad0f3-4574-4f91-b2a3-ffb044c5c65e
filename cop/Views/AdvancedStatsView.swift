import SwiftUI
import Charts

// MARK: - 高级统计视图
struct AdvancedStatsView: View {
    @StateObject private var statsViewModel = AdvancedStatsViewModel()
    @State private var selectedTimeRange: TimeRange = .week
    @State private var selectedMetric: StatMetric = .optimizedRequests
    @State private var showingDetailsSheet = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 时间范围选择器
                    timeRangeSelector
                    
                    // 主要指标卡片
                    mainMetricsSection
                    
                    // 趋势图表
                    trendChartSection
                    
                    // 分类统计
                    categoryStatsSection
                    
                    // 性能监控
                    performanceSection
                }
                .padding()
            }
            .navigationTitle("高级统计")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("详情") {
                        showingDetailsSheet = true
                    }
                }
            }
            .sheet(isPresented: $showingDetailsSheet) {
                DetailedStatsView(viewModel: statsViewModel)
            }
            .onAppear {
                statsViewModel.loadStats()
            }
        }
    }
    
    // MARK: - 时间范围选择器
    private var timeRangeSelector: some View {
        HStack {
            Text("时间范围:")
                .font(.headline)
            
            Spacer()
            
            Picker("Time Range", selection: $selectedTimeRange) {
                ForEach(TimeRange.allCases, id: \.self) { range in
                    Text(range.displayName).tag(range)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
        .onChange(of: selectedTimeRange) {
            statsViewModel.updateTimeRange(selectedTimeRange)
        }
    }
    
    // MARK: - 主要指标卡片
    private var mainMetricsSection: some View {
        VStack(spacing: 15) {
            HStack(spacing: 15) {
                MetricCard(
                    title: "优化请求",
                    value: "\(statsViewModel.currentStats.optimizedRequests)",
                    change: statsViewModel.getChangeForMetric(.optimizedRequests),
                    icon: "speedometer",
                    color: .blue
                )
                .frame(maxWidth: .infinity)
                
                MetricCard(
                    title: "节省流量",
                    value: statsViewModel.currentStats.formattedSavedBandwidth,
                    change: statsViewModel.getChangeForMetric(.savedBandwidth),
                    icon: "wifi",
                    color: .green
                )
                .frame(maxWidth: .infinity)
            }
            
            HStack(spacing: 15) {
                MetricCard(
                    title: "页面加速",
                    value: String(format: "%.1f%%", statsViewModel.performanceStats.averageSpeedImprovement),
                    change: "+",
                    icon: "speedometer",
                    color: .orange
                )
                .frame(maxWidth: .infinity)
                
                // 占位符保持布局平衡
                Color.clear
                    .frame(maxWidth: .infinity)
            }
        }
    }
    
    // MARK: - 趋势图表
    private var trendChartSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("趋势分析")
                    .font(.headline)
                
                Spacer()
                
                Picker("Metric", selection: $selectedMetric) {
                    ForEach(StatMetric.allCases, id: \.self) { metric in
                        Text(metric.displayName).tag(metric)
                    }
                }
                .pickerStyle(MenuPickerStyle())
            }
            
            if #available(iOS 16.0, *) {
                Chart(statsViewModel.trendData) { dataPoint in
                    LineMark(
                        x: .value("Date", dataPoint.date),
                        y: .value("Value", dataPoint.getValue(for: selectedMetric))
                    )
                    .foregroundStyle(selectedMetric.color)
                    .interpolationMethod(.catmullRom)
                }
                .frame(height: 200)
                .chartBackground { _ in
                    Rectangle()
                        .fill(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
            } else {
                // iOS 15 兼容性图表
                LegacyChartView(
                    data: statsViewModel.trendData,
                    metric: selectedMetric
                )
                .frame(height: 200)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - 分类统计
    private var categoryStatsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("分类统计")
                .font(.headline)
            
            ForEach(SystemCategory.allCases.filter { $0 != .none }, id: \.self) { category in
                CategoryStatRow(
                    category: category,
                    count: statsViewModel.getCategoryCount(category),
                    percentage: statsViewModel.getCategoryPercentage(category)
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
    
    // MARK: - 性能监控
    private var performanceSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("性能监控")
                .font(.headline)
            
            PerformanceIndicator(
                title: "内存使用",
                value: statsViewModel.memoryStats.formattedCurrentUsage,
                percentage: statsViewModel.memoryStats.usagePercentage / 100,
                status: statsViewModel.memoryStats.isCriticalLevel ? .critical : 
                       statsViewModel.memoryStats.isWarningLevel ? .warning : .normal
            )
            
            PerformanceIndicator(
                title: "规则数量",
                value: "\(statsViewModel.currentStats.activeRulesCount)",
                percentage: Double(statsViewModel.currentStats.activeRulesCount) / 150000,
                status: .normal
            )
            
            PerformanceIndicator(
                title: "编译效率",
                value: String(format: "%.2fs", statsViewModel.performanceStats.averageCompilationTime),
                percentage: min(1.0, statsViewModel.performanceStats.averageCompilationTime / 10.0),
                status: statsViewModel.performanceStats.averageCompilationTime > 5.0 ? .warning : .normal
            )
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

// MARK: - 指标卡片
struct MetricCard: View {
    let title: String
    let value: String
    let change: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                Spacer()
                Text(change)
                    .font(.caption)
                    .foregroundColor(.green)
            }
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - 分类统计行
struct CategoryStatRow: View {
    let category: SystemCategory
    let count: Int
    let percentage: Double
    
    var body: some View {
        HStack {
            Image(systemName: category.icon)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            Text(category.displayName)
                .font(.body)
            
            Spacer()
            
            VStack(alignment: .trailing) {
                Text("\(count)")
                    .font(.headline)
                Text(String(format: "%.1f%%", percentage))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - 性能指示器
struct PerformanceIndicator: View {
    let title: String
    let value: String
    let percentage: Double
    let status: PerformanceStatus
    
    var body: some View {
        HStack {
            VStack(alignment: .leading) {
                Text(title)
                    .font(.body)
                Text(value)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.3), lineWidth: 4)
                
                Circle()
                    .trim(from: 0, to: CGFloat(min(percentage, 1.0)))
                    .stroke(status.color, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut, value: percentage)
            }
            .frame(width: 40, height: 40)
        }
    }
}

// MARK: - 详细统计视图
struct DetailedStatsView: View {
    let viewModel: AdvancedStatsViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 16) {
                    Text("详细统计数据")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    // 这里可以添加更多详细统计内容
                    StatDetailRow(label: "总请求数", value: "\(viewModel.currentStats.totalPageLoads)")
                    StatDetailRow(label: "优化请求", value: "\(viewModel.currentStats.optimizedRequests)")
                    StatDetailRow(label: "节省流量", value: viewModel.currentStats.formattedSavedBandwidth)
                    StatDetailRow(label: "活跃规则", value: "\(viewModel.currentStats.activeRulesCount)")
                    StatDetailRow(label: "平均加载时间", value: String(format: "%.2fs", viewModel.currentStats.averagePageLoadTime))
                }
                .padding()
            }
            .navigationTitle("详细统计")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - 统计详情行
struct StatDetailRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
            Spacer()
            Text(value)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - iOS 15 兼容图表
struct LegacyChartView: View {
    let data: [TrendDataPoint]
    let metric: StatMetric
    
    var body: some View {
        GeometryReader { geometry in
            Path { path in
                guard !data.isEmpty else { return }
                
                let maxValue = data.map { $0.getValue(for: metric) }.max() ?? 1
                let width = geometry.size.width
                let height = geometry.size.height
                
                for (index, point) in data.enumerated() {
                    let x = CGFloat(index) / CGFloat(data.count - 1) * width
                    let y = height - (point.getValue(for: metric) / maxValue * height)
                    
                    if index == 0 {
                        path.move(to: CGPoint(x: x, y: y))
                    } else {
                        path.addLine(to: CGPoint(x: x, y: y))
                    }
                }
            }
            .stroke(metric.color, lineWidth: 2)
        }
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }
}

// MARK: - 支持模型
enum TimeRange: String, CaseIterable {
    case day = "24小时"
    case week = "7天"
    case month = "30天"
    case year = "1年"
    
    var displayName: String {
        self.rawValue
    }
    
    var daysAndInterval: (days: Int, interval: Calendar.Component) {
        switch self {
        case .day: return (24, .hour)
        case .week: return (7, .day)
        case .month: return (30, .day)
        case .year: return (365, .day)
        }
    }
}

enum StatMetric: String, CaseIterable, Identifiable {
    case optimizedRequests
    case savedBandwidth
    case pageLoadTime

    var id: String { self.rawValue }

    var displayName: String {
        switch self {
        case .optimizedRequests: return "优化请求"
        case .savedBandwidth: return "节省流量"
        case .pageLoadTime: return "页面加载时间"
        }
    }

    var color: Color {
        switch self {
        case .optimizedRequests: return .blue
        case .savedBandwidth: return .green
        case .pageLoadTime: return .orange
        }
    }

    func getValue(for stats: SystemStatistics) -> Double {
        switch self {
        case .optimizedRequests: return Double(stats.optimizedRequests)
        case .savedBandwidth: return stats.savedBandwidth
        case .pageLoadTime: return stats.averagePageLoadTime
        }
    }
}

enum PerformanceStatus {
    case normal, warning, critical
    
    var color: Color {
        switch self {
        case .normal: return .green
        case .warning: return .orange
        case .critical: return .red
        }
    }
}

// MARK: - 趋势数据点
struct TrendDataPoint: Identifiable, Codable {
    var id = UUID()
    let date: Date
    let optimizedRequests: Int
    let savedBandwidth: Double
    let pageLoadTime: Double
    
    func getValue(for metric: StatMetric) -> Double {
        switch metric {
        case .optimizedRequests: return Double(optimizedRequests)
        case .savedBandwidth: return savedBandwidth
        case .pageLoadTime: return pageLoadTime
        }
    }
}

// MARK: - 系统统计数据模型
struct SystemStatistics: Codable {
    var totalPageLoads: Int
    var optimizedRequests: Int
    var savedBandwidth: Double // MB
    var activeRulesCount: Int
    var averagePageLoadTime: Double // 秒
    
    var formattedSavedBandwidth: String {
        if savedBandwidth >= 1000 {
            return String(format: "%.1f GB", savedBandwidth / 1000)
        } else {
            return String(format: "%.1f MB", savedBandwidth)
        }
    }
}

struct SystemPerformanceStats {
    var currentMemoryUsage: Double = 0.0
    var maxMemoryUsage: Double = 0.0
    var averageCompilationTime: Double = 0.0
    var averageSpeedImprovement: Double = 0.0
    var cpuUsage: Double = 0.0
    
    var memoryStats: MemoryStats {
        MemoryStats(
            currentUsage: currentMemoryUsage,
            maxUsage: maxMemoryUsage
        )
    }
}

struct MemoryStats {
    let currentUsage: Double
    let maxUsage: Double
    
    var formattedCurrentUsage: String {
        let bcf = ByteCountFormatter()
        bcf.countStyle = .memory
        return bcf.string(fromByteCount: Int64(currentUsage))
    }
    
    var usagePercentage: Double {
        maxUsage > 0 ? (currentUsage / maxUsage) * 100 : 0
    }
    
    var isWarningLevel: Bool { usagePercentage > 70 }
    var isCriticalLevel: Bool { usagePercentage > 90 }
}

enum SystemCategory: String, CaseIterable, Identifiable {
    case none = "未知"
    case social = "社交"
    case news = "新闻"
    case shopping = "购物"
    case video = "视频"
    case other = "其他"
    
    var id: String { self.rawValue }
    
    var displayName: String {
        self.rawValue
    }
    
    var icon: String {
        switch self {
        case .none: return "questionmark.circle"
        case .social: return "person.2"
        case .news: return "newspaper"
        case .shopping: return "cart"
        case .video: return "play.rectangle"
        case .other: return "ellipsis.circle"
        }
    }
}

// MARK: - Preview
struct AdvancedStatsView_Previews: PreviewProvider {
    static var previews: some View {
        AdvancedStatsView()
    }
} 