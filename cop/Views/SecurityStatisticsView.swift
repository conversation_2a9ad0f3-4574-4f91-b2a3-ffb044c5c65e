//
//  SecurityStatisticsView.swift
//  cop
//
//  Created by Augment Agent on 2025/6/2.
//

import SwiftUI

// MARK: - 安全统计视图
struct SecurityStatisticsView: View {
    let statistics: SecurityStatistics
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 总览卡片
                overviewCard
                
                // 网络安全统计
                networkSecurityStats
                
                // 权限管理统计
                permissionStats
                
                // 时间信息
                timeInfo
            }
            .padding()
        }
        .navigationTitle("安全统计")
        .navigationBarTitleDisplayMode(.large)
    }
    
    // MARK: - 总览卡片
    private var overviewCard: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "shield.checkered")
                    .font(.title)
                    .foregroundColor(.green)
                
                Text("安全保护总览")
                    .font(.headline)
                
                Spacer()
            }
            
            HStack {
                Text("\(totalProtections)")
                    .font(.system(size: 48, weight: .bold, design: .rounded))
                    .foregroundColor(.green)
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("次保护")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("守护您的安全")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(UIColor.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - 网络安全统计
    private var networkSecurityStats: some View {
        VStack(spacing: 16) {
            HStack {
                Text("网络安全")
                    .font(.headline)
                Spacer()
            }
            
            VStack(spacing: 16) {
                HStack(spacing: 16) {
                    SecurityStatCard(
                        title: "HTTPS 升级",
                        value: "\(statistics.httpsUpgrades)",
                        icon: "lock.shield",
                        color: .green,
                        description: "HTTP 连接自动升级为 HTTPS"
                    )
                    .frame(maxWidth: .infinity)
                    
                    SecurityStatCard(
                        title: "证书警告",
                        value: "\(statistics.certificateWarnings)",
                        icon: "exclamationmark.shield",
                        color: .orange,
                        description: "检测到的无效证书"
                    )
                    .frame(maxWidth: .infinity)
                }
                
                HStack(spacing: 16) {
                    SecurityStatCard(
                        title: "混合内容",
                        value: "\(statistics.blockedMixedContent)",
                        icon: "exclamationmark.triangle",
                        color: .yellow,
                        description: "阻止的不安全资源"
                    )
                    .frame(maxWidth: .infinity)
                    
                    SecurityStatCard(
                        title: "欺诈网站",
                        value: "\(statistics.fraudulentSitesBlocked)",
                        icon: "exclamationmark.octagon",
                        color: .red,
                        description: "阻止的恶意网站"
                    )
                    .frame(maxWidth: .infinity)
                }
            }
        }
    }
    
    // MARK: - 权限管理统计
    private var permissionStats: some View {
        VStack(spacing: 16) {
            HStack {
                Text("权限管理")
                    .font(.headline)
                Spacer()
            }
            
            SecurityStatCard(
                title: "权限拒绝",
                value: "\(statistics.permissionsBlocked)",
                icon: "hand.raised.slash",
                color: .blue,
                description: "拒绝的权限请求"
            )
        }
    }
    
    // MARK: - 时间信息
    private var timeInfo: some View {
        VStack(spacing: 12) {
            HStack {
                Text("统计信息")
                    .font(.headline)
                Spacer()
            }
            
            VStack(spacing: 8) {
                HStack {
                    Image(systemName: "clock")
                        .foregroundColor(.blue)
                    Text("统计开始时间")
                    Spacer()
                    Text(statistics.lastResetDate, style: .date)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Image(systemName: "calendar")
                        .foregroundColor(.blue)
                    Text("统计天数")
                    Spacer()
                    Text("\(daysSinceReset) 天")
                        .foregroundColor(.secondary)
                }
                
                if totalProtections > 0 {
                    HStack {
                        Image(systemName: "chart.line.uptrend.xyaxis")
                            .foregroundColor(.blue)
                        Text("平均每天保护")
                        Spacer()
                        Text("\(averageProtectionsPerDay) 次")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(UIColor.systemGray6))
            )
        }
    }
    
    // MARK: - 计算属性
    private var totalProtections: Int {
        statistics.httpsUpgrades +
        statistics.blockedMixedContent +
        statistics.certificateWarnings +
        statistics.fraudulentSitesBlocked +
        statistics.permissionsBlocked
    }
    
    private var daysSinceReset: Int {
        let days = Calendar.current.dateComponents([.day], from: statistics.lastResetDate, to: Date()).day ?? 0
        return max(1, days)
    }
    
    private var averageProtectionsPerDay: Int {
        totalProtections / daysSinceReset
    }
}

// MARK: - 安全统计卡片
struct SecurityStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    let description: String
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.system(size: 24, weight: .bold, design: .rounded))
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.primary)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
        )
    }
}

// MARK: - 预览
struct SecurityStatisticsView_Previews: PreviewProvider {
    static var previews: some View {
        SecurityStatisticsView(statistics: SecurityStatistics())
    }
}
