import XCTest
import UIKit
@testable import cop

// MARK: - 优化缩略图管理器测试套件（第二阶段性能优化）
class OptimizedThumbnailManagerTests: XCTestCase {
    
    var thumbnailManager: OptimizedThumbnailManager!
    var testImageURL: URL!
    var testVideoURL: URL!
    var testMediaFileID: UUID!
    
    override func setUpWithError() throws {
        thumbnailManager = OptimizedThumbnailManager.shared
        testMediaFileID = UUID()
        
        // 创建测试图片
        testImageURL = try createTestImage()
        
        // 创建测试视频（简化版本，实际测试中可以使用真实视频）
        testVideoURL = try createMockVideoFile()
    }
    
    override func tearDownWithError() throws {
        // 清理测试文件
        try? FileManager.default.removeItem(at: testImageURL)
        try? FileManager.default.removeItem(at: testVideoURL)
        
        // 清理缓存
        await thumbnailManager.clearCache()
    }
    
    // MARK: - 基础功能测试
    
    func testThumbnailGeneration() async throws {
        // 测试图片缩略图生成
        let result = try await thumbnailManager.generateThumbnails(
            for: testMediaFileID,
            mediaType: .image,
            sourceURL: testImageURL,
            sizes: [.small, .medium, .large]
        )
        
        XCTAssertEqual(result.count, 3, "应该生成3个不同尺寸的缩略图")
        XCTAssertNotNil(result[.small], "应该包含小尺寸缩略图")
        XCTAssertNotNil(result[.medium], "应该包含中等尺寸缩略图")
        XCTAssertNotNil(result[.large], "应该包含大尺寸缩略图")
        
        // 验证文件确实存在
        for (size, url) in result {
            XCTAssertTrue(FileManager.default.fileExists(atPath: url.path),
                         "缩略图文件应该存在: \(size)")
        }
    }
    
    func testThumbnailCaching() async throws {
        // 生成缩略图
        _ = try await thumbnailManager.generateThumbnails(
            for: testMediaFileID,
            mediaType: .image,
            sourceURL: testImageURL,
            sizes: [.medium]
        )
        
        // 从缓存获取
        let cachedImage = await thumbnailManager.getThumbnail(for: testMediaFileID, size: .medium)
        XCTAssertNotNil(cachedImage, "应该能从缓存获取缩略图")
        
        // 验证图片尺寸
        let expectedSize = OptimizedThumbnailManager.ThumbnailSize.medium.size
        XCTAssertEqual(cachedImage?.size.width, expectedSize.width, accuracy: 1.0)
        XCTAssertEqual(cachedImage?.size.height, expectedSize.height, accuracy: 1.0)
    }
    
    func testVideoThumbnailGeneration() async throws {
        let result = try await thumbnailManager.generateThumbnails(
            for: testMediaFileID,
            mediaType: .video,
            sourceURL: testVideoURL,
            sizes: [.medium]
        )
        
        XCTAssertEqual(result.count, 1, "应该生成视频缩略图")
        XCTAssertNotNil(result[.medium], "应该包含中等尺寸视频缩略图")
        
        // 验证缩略图可以正常加载
        let thumbnail = await thumbnailManager.getThumbnail(for: testMediaFileID, size: .medium)
        XCTAssertNotNil(thumbnail, "应该能加载视频缩略图")
    }
    
    // MARK: - 优先级和并发测试
    
    func testTaskPriority() async throws {
        let highPriorityID = UUID()
        let lowPriorityID = UUID()
        
        // 启动低优先级任务
        let lowPriorityTask = Task {
            try await thumbnailManager.generateThumbnails(
                for: lowPriorityID,
                mediaType: .image,
                sourceURL: testImageURL,
                priority: .low
            )
        }
        
        // 启动高优先级任务
        let highPriorityTask = Task {
            try await thumbnailManager.generateThumbnails(
                for: highPriorityID,
                mediaType: .image,
                sourceURL: testImageURL,
                priority: .high
            )
        }
        
        // 等待完成
        let _ = try await highPriorityTask.value
        let _ = try await lowPriorityTask.value
        
        // 验证都能成功完成
        let highPriorityResult = await thumbnailManager.getThumbnail(for: highPriorityID, size: .medium)
        let lowPriorityResult = await thumbnailManager.getThumbnail(for: lowPriorityID, size: .medium)
        
        XCTAssertNotNil(highPriorityResult, "高优先级任务应该完成")
        XCTAssertNotNil(lowPriorityResult, "低优先级任务应该完成")
    }
    
    func testConcurrentGeneration() async throws {
        let fileIDs = (0..<5).map { _ in UUID() }
        
        // 并发生成多个缩略图
        let tasks = fileIDs.map { fileID in
            Task {
                try await thumbnailManager.generateThumbnails(
                    for: fileID,
                    mediaType: .image,
                    sourceURL: testImageURL,
                    sizes: [.medium]
                )
            }
        }
        
        // 等待所有任务完成
        for task in tasks {
            let _ = try await task.value
        }
        
        // 验证所有缩略图都生成成功
        for fileID in fileIDs {
            let thumbnail = await thumbnailManager.getThumbnail(for: fileID, size: .medium)
            XCTAssertNotNil(thumbnail, "并发生成的缩略图应该存在")
        }
    }
    
    // MARK: - 缓存测试
    
    func testCacheHitRate() async throws {
        // 生成一些缩略图
        let fileIDs = (0..<10).map { _ in UUID() }
        
        for fileID in fileIDs {
            _ = try await thumbnailManager.generateThumbnails(
                for: fileID,
                mediaType: .image,
                sourceURL: testImageURL,
                sizes: [.medium]
            )
        }
        
        // 多次访问缓存
        for _ in 0..<5 {
            for fileID in fileIDs {
                let thumbnail = await thumbnailManager.getThumbnail(for: fileID, size: .medium)
                XCTAssertNotNil(thumbnail, "缓存访问应该成功")
            }
        }
        
        // 检查统计信息
        let stats = thumbnailManager.statistics
        let hitRate = stats.cacheHitRate
        
        XCTAssertGreaterThan(hitRate, 0.8, "缓存命中率应该超过80%")
    }
    
    func testCacheCleanup() async throws {
        // 生成大量缩略图填满缓存
        let fileIDs = (0..<50).map { _ in UUID() }
        
        for fileID in fileIDs {
            _ = try await thumbnailManager.generateThumbnails(
                for: fileID,
                mediaType: .image,
                sourceURL: testImageURL,
                sizes: [.medium]
            )
        }
        
        let initialCacheSize = thumbnailManager.statistics.memoryCacheSize
        
        // 清理1秒前的缓存
        await thumbnailManager.clearCache(olderThan: 1.0)
        
        // 等待清理完成
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
        
        let finalCacheSize = thumbnailManager.statistics.memoryCacheSize
        
        // 缓存大小应该有所减少
        XCTAssertLessThanOrEqual(finalCacheSize, initialCacheSize,
                                "清理后缓存大小应该减少或保持不变")
    }
    
    // MARK: - 错误处理测试
    
    func testInvalidFileHandling() async {
        let invalidURL = URL(fileURLWithPath: "/nonexistent/file.jpg")
        
        do {
            _ = try await thumbnailManager.generateThumbnails(
                for: UUID(),
                mediaType: .image,
                sourceURL: invalidURL,
                sizes: [.medium]
            )
            XCTFail("应该抛出错误")
        } catch {
            // 期望的错误
            XCTAssertTrue(error is OptimizedThumbnailManager.ThumbnailError,
                         "应该是缩略图错误类型")
        }
    }
    
    func testTaskCancellation() async throws {
        let fileID = UUID()
        
        // 启动任务
        let task = Task {
            try await thumbnailManager.generateThumbnails(
                for: fileID,
                mediaType: .image,
                sourceURL: testImageURL,
                sizes: [.large] // 大尺寸生成较慢
            )
        }
        
        // 立即取消
        thumbnailManager.cancelGeneration(for: fileID)
        task.cancel()
        
        do {
            _ = try await task.value
        } catch {
            // 期望取消错误
            XCTAssertTrue(error is CancellationError ||
                         (error as? OptimizedThumbnailManager.ThumbnailError) == .cancelled,
                         "应该是取消错误")
        }
    }
    
    // MARK: - 性能测试
    
    func testGenerationPerformance() throws {
        let fileIDs = (0..<20).map { _ in UUID() }
        
        measure {
            let expectation = XCTestExpectation(description: "缩略图生成性能测试")
            
            Task {
                do {
                    for fileID in fileIDs {
                        _ = try await thumbnailManager.generateThumbnails(
                            for: fileID,
                            mediaType: .image,
                            sourceURL: testImageURL,
                            sizes: [.medium]
                        )
                    }
                    expectation.fulfill()
                } catch {
                    XCTFail("性能测试失败: \(error)")
                }
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
    }
    
    func testCachePerformance() throws {
        // 先生成一些缓存
        let fileIDs = (0..<100).map { _ in UUID() }
        
        let setupExpectation = XCTestExpectation(description: "缓存设置")
        Task {
            for fileID in fileIDs {
                _ = try await thumbnailManager.generateThumbnails(
                    for: fileID,
                    mediaType: .image,
                    sourceURL: testImageURL,
                    sizes: [.medium]
                )
            }
            setupExpectation.fulfill()
        }
        wait(for: [setupExpectation], timeout: 30.0)
        
        // 测试缓存访问性能
        measure {
            let expectation = XCTestExpectation(description: "缓存访问性能测试")
            
            Task {
                for fileID in fileIDs {
                    let _ = await thumbnailManager.getThumbnail(for: fileID, size: .medium)
                }
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 5.0)
        }
    }
    
    // MARK: - 统计信息测试
    
    func testStatisticsTracking() async throws {
        let initialStats = thumbnailManager.statistics
        
        // 执行一些操作
        _ = try await thumbnailManager.generateThumbnails(
            for: testMediaFileID,
            mediaType: .image,
            sourceURL: testImageURL,
            sizes: [.medium]
        )
        
        // 多次访问缓存
        for _ in 0..<5 {
            _ = await thumbnailManager.getThumbnail(for: testMediaFileID, size: .medium)
        }
        
        let finalStats = thumbnailManager.statistics
        
        // 验证统计信息更新
        XCTAssertGreaterThan(finalStats.requestCount, initialStats.requestCount,
                           "请求计数应该增加")
        XCTAssertGreaterThan(finalStats.generationCompleted, initialStats.generationCompleted,
                           "生成完成计数应该增加")
        XCTAssertGreaterThan(finalStats.cacheHits, initialStats.cacheHits,
                           "缓存命中计数应该增加")
    }
    
    // MARK: - 辅助方法
    
    private func createTestImage() throws -> URL {
        let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let testImageURL = documentsURL.appendingPathComponent("test_image_\(UUID().uuidString).jpg")
        
        // 创建测试图片
        let size = CGSize(width: 1920, height: 1080)
        let renderer = UIGraphicsImageRenderer(size: size)
        let image = renderer.image { context in
            // 绘制渐变背景
            let colors = [UIColor.red.cgColor, UIColor.blue.cgColor]
            let gradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(),
                                    colors: colors as CFArray,
                                    locations: nil)!
            
            context.cgContext.drawLinearGradient(gradient,
                                               start: CGPoint.zero,
                                               end: CGPoint(x: size.width, y: size.height),
                                               options: [])
        }
        
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw TestError.imageCreationFailed
        }
        
        try imageData.write(to: testImageURL)
        return testImageURL
    }
    
    private func createMockVideoFile() throws -> URL {
        let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let testVideoURL = documentsURL.appendingPathComponent("test_video_\(UUID().uuidString).mp4")
        
        // 创建一个空的模拟视频文件
        // 在实际测试中，这里应该是真实的视频文件
        let mockVideoData = Data("mock video content".utf8)
        try mockVideoData.write(to: testVideoURL)
        
        return testVideoURL
    }
    
    enum TestError: Error {
        case imageCreationFailed
        case videoCreationFailed
    }
} 