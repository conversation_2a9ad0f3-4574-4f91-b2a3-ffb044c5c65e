import XCTest
@testable import cop

@MainActor
final class OptimizedMediaLibraryTests: XCTestCase {
    
    var repository: MediaDatabaseRepository!
    var cache: MediaMetadataCache!
    var service: OptimizedMediaLibraryService!
    
    override func setUp() async throws {
        try await super.setUp()
        
        repository = MediaDatabaseRepository()
        cache = MediaMetadataCache.shared
        service = OptimizedMediaLibraryService.shared
        
        // 清理测试环境
        try await clearTestData()
    }
    
    override func tearDown() async throws {
        try await clearTestData()
        try await super.tearDown()
    }
    
    private func clearTestData() async throws {
        // 清理数据库
        try await DatabaseManager.shared.executeSQL("DELETE FROM media_files")
        try await DatabaseManager.shared.executeSQL("DELETE FROM folders")
        try await DatabaseManager.shared.executeSQL("DELETE FROM cache_metadata")
        
        // 清理缓存
        try await cache.clearAllCache()
    }
    
    // MARK: - 数据库测试
    func testDatabaseManagerInitialization() async throws {
        // 测试数据库管理器能否正常初始化
        let dbManager = DatabaseManager.shared
        XCTAssertNotNil(dbManager)
        
        // 测试基本SQL执行
        try await dbManager.executeSQL("SELECT 1")
    }
    
    func testFolderOperations() async throws {
        // 测试文件夹插入
        let folder = DatabaseFolder(
            id: nil,
            name: "测试文件夹",
            path: "/test/path",
            parentId: nil,
            createdAt: Date(),
            updatedAt: Date(),
            mediaCount: 0,
            totalSize: 0,
            fileCount: 0,
            lastModified: Date(),
            thumbnailPath: nil
        )
        
        let folderId = try await repository.insertFolder(folder)
        XCTAssertGreaterThan(folderId, 0)
        
        // 测试文件夹查询
        let retrievedFolder = try await repository.getFolder(id: folderId)
        XCTAssertNotNil(retrievedFolder)
        XCTAssertEqual(retrievedFolder?.name, "测试文件夹")
        
        // 测试文件夹更新
        var updatedFolder = folder
        updatedFolder = DatabaseFolder(
            id: folderId,
            name: "更新的文件夹",
            path: "/test/path",
            parentId: nil,
            createdAt: folder.createdAt,
            updatedAt: Date(),
            mediaCount: 1,
            totalSize: 1024,
            fileCount: 1,
            lastModified: Date(),
            thumbnailPath: nil
        )
        
        try await repository.updateFolder(updatedFolder)
        
        let afterUpdate = try await repository.getFolder(id: folderId)
        XCTAssertEqual(afterUpdate?.name, "更新的文件夹")
        XCTAssertEqual(afterUpdate?.mediaCount, 1)
        
        // 测试文件夹删除
        try await repository.deleteFolder(id: folderId)
        let afterDelete = try await repository.getFolder(id: folderId)
        XCTAssertNil(afterDelete)
    }
    
    func testMediaFileOperations() async throws {
        // 先创建一个文件夹
        let folder = DatabaseFolder(
            id: nil,
            name: "媒体文件夹",
            path: "/media/path",
            parentId: nil,
            createdAt: Date(),
            updatedAt: Date(),
            mediaCount: 0,
            totalSize: 0,
            fileCount: 0,
            lastModified: Date(),
            thumbnailPath: nil
        )
        
        let folderId = try await repository.insertFolder(folder)
        
        // 创建媒体文件
        let mediaFile = DatabaseMediaFile(
            id: UUID().uuidString,
            folderId: folderId,
            name: "test.jpg",
            filePath: "/media/path/test.jpg",
            fileType: "image",
            fileSize: 1024,
            createdAt: Date(),
            modifiedAt: Date(),
            duration: nil,
            width: 1920,
            height: 1080,
            thumbnailPath: nil,
            folderPath: "媒体文件夹"
        )
        
        // 测试媒体文件插入
        try await repository.insertMediaFile(mediaFile)
        
        // 测试媒体文件查询
        let retrievedFile = try await repository.getMediaFile(id: mediaFile.id)
        XCTAssertNotNil(retrievedFile)
        XCTAssertEqual(retrievedFile?.name, "test.jpg")
        XCTAssertEqual(retrievedFile?.folderId, folderId)
        
        // 测试文件夹内媒体文件查询
        let filesInFolder = try await repository.getMediaFiles(in: folderId)
        XCTAssertEqual(filesInFolder.count, 1)
        XCTAssertEqual(filesInFolder.first?.id, mediaFile.id)
        
        // 测试搜索
        let searchResults = try await repository.searchMediaFiles(query: "test")
        XCTAssertEqual(searchResults.count, 1)
        
        // 测试分页查询
        let paginatedResult = try await repository.getMediaFilesPaginated(
            folderId: folderId,
            pageSize: 10,
            page: 0
        )
        XCTAssertEqual(paginatedResult.files.count, 1)
        XCTAssertEqual(paginatedResult.totalCount, 1)
    }
    
    func testBatchOperations() async throws {
        // 创建文件夹
        let folder = DatabaseFolder(
            id: nil,
            name: "批量测试文件夹",
            path: "/batch/path",
            parentId: nil,
            createdAt: Date(),
            updatedAt: Date(),
            mediaCount: 0,
            totalSize: 0,
            fileCount: 0,
            lastModified: Date(),
            thumbnailPath: nil
        )
        
        let folderId = try await repository.insertFolder(folder)
        
        // 创建多个媒体文件
        var mediaFiles: [DatabaseMediaFile] = []
        for i in 0..<5 {
            let mediaFile = DatabaseMediaFile(
                id: UUID().uuidString,
                folderId: folderId,
                name: "test\(i).jpg",
                filePath: "/batch/path/test\(i).jpg",
                fileType: "image",
                fileSize: Int64(1024 * i),
                createdAt: Date(),
                modifiedAt: Date(),
                duration: nil,
                width: 1920,
                height: 1080,
                thumbnailPath: nil,
                folderPath: "批量测试文件夹"
            )
            mediaFiles.append(mediaFile)
        }
        
        // 测试批量插入
        try await repository.insertMediaFiles(mediaFiles)
        
        // 验证插入结果
        let allFiles = try await repository.getMediaFiles(in: folderId)
        XCTAssertEqual(allFiles.count, 5)
        
        // 测试文件夹统计更新
        try await repository.updateFolderStatistics(id: folderId)
        
        let updatedFolder = try await repository.getFolder(id: folderId)
        XCTAssertEqual(updatedFolder?.mediaCount, 5)
        
        // 测试批量删除
        let idsToDelete = Array(mediaFiles.prefix(3).map { $0.id })
        try await repository.deleteMediaFiles(ids: idsToDelete)
        
        let remainingFiles = try await repository.getMediaFiles(in: folderId)
        XCTAssertEqual(remainingFiles.count, 2)
    }
    
    // MARK: - 缓存测试
    func testCacheOperations() async throws {
        let mediaFileId = UUID()
        let testData = "测试数据".data(using: .utf8)!
        
        // 测试数据缓存
        try await cache.cacheData(
            testData,
            for: mediaFileId,
            type: .metadata,
            size: .small,
            strategy: .memoryAndDisk
        )
        
        // 测试数据获取
        let cachedData = await cache.getCachedData(
            for: mediaFileId,
            type: .metadata,
            size: .small
        )
        
        XCTAssertNotNil(cachedData)
        XCTAssertEqual(cachedData, testData)
        
        // 测试图片缓存
        let testImage = UIImage(systemName: "photo")!
        try await cache.cacheImage(testImage, for: mediaFileId, size: .medium)
        
        let cachedImage = await cache.getCachedImage(for: mediaFileId, size: .medium)
        XCTAssertNotNil(cachedImage)
        
        // 测试缓存删除
        try await cache.removeCachedData(for: mediaFileId, type: .metadata, size: .small)
        
        let afterDelete = await cache.getCachedData(
            for: mediaFileId,
            type: .metadata,
            size: .small
        )
        XCTAssertNil(afterDelete)
    }
    
    func testCacheStatistics() async throws {
        let mediaFileId = UUID()
        let testData = Data(count: 1024) // 1KB数据
        
        try await cache.cacheData(
            testData,
            for: mediaFileId,
            type: .metadata,
            size: .small
        )
        
        let stats = await cache.getCacheStatistics()
        XCTAssertGreaterThan(stats.diskUsage, 0)
    }
    
    // MARK: - 虚拟化组件测试
    func testVirtualizationConfig() {
        let config = VirtualizationConfig(
            itemSize: CGSize(width: 200, height: 200),
            spacing: 10,
            bufferSize: 15,
            preloadDistance: 250
        )
        
        XCTAssertEqual(config.itemSize.width, 200)
        XCTAssertEqual(config.spacing, 10)
        XCTAssertEqual(config.bufferSize, 15)
        XCTAssertEqual(config.preloadDistance, 250)
    }
    
    func testMediaFileVirtualizableAdapter() {
        let mediaFile = MediaFileInfo(
            id: UUID(),
            name: "test.jpg",
            type: .image,
            fileSize: 1024,
            creationDate: Date(),
            modificationDate: Date(),
            localURL: URL(fileURLWithPath: "/test.jpg"),
            thumbnailURL: nil,
            folderPath: "test",
            duration: nil,
            dimensions: CGSize(width: 1920, height: 1080)
        )
        
        XCTAssertEqual(mediaFile.estimatedSize.width, 150)
        XCTAssertEqual(mediaFile.estimatedSize.height, 150)
    }
    
    // MARK: - 服务集成测试
    func testServiceInitialization() async throws {
        // 等待服务初始化完成
        var attempts = 0
        while !service.isInitialized && attempts < 10 {
            try await Task.sleep(nanoseconds: 100_000_000) // 100ms
            attempts += 1
        }
        
        XCTAssertTrue(service.isInitialized)
        XCTAssertNil(service.error)
    }
    
    func testServiceStatistics() async throws {
        // 确保服务已初始化
        XCTAssertTrue(service.isInitialized)
        
        let stats = try await service.getLibraryStatistics()
        XCTAssertGreaterThanOrEqual(stats.folderCount, 0)
        XCTAssertGreaterThanOrEqual(stats.totalMediaCount, 0)
        XCTAssertGreaterThanOrEqual(stats.totalSize, 0)
    }
    
    // MARK: - 性能测试
    func testDatabasePerformance() async throws {
        // 测试大批量数据插入性能
        let folder = DatabaseFolder(
            id: nil,
            name: "性能测试文件夹",
            path: "/performance/path",
            parentId: nil,
            createdAt: Date(),
            updatedAt: Date(),
            mediaCount: 0,
            totalSize: 0,
            fileCount: 0,
            lastModified: Date(),
            thumbnailPath: nil
        )
        
        let folderId = try await repository.insertFolder(folder)
        
        // 创建1000个媒体文件进行性能测试
        var mediaFiles: [DatabaseMediaFile] = []
        for i in 0..<1000 {
            let mediaFile = DatabaseMediaFile(
                id: UUID().uuidString,
                folderId: folderId,
                name: "perf_test\(i).jpg",
                filePath: "/performance/path/perf_test\(i).jpg",
                fileType: "image",
                fileSize: Int64(1024 * (i + 1)),
                createdAt: Date(),
                modifiedAt: Date(),
                duration: nil,
                width: 1920,
                height: 1080,
                thumbnailPath: nil,
                folderPath: "性能测试文件夹"
            )
            mediaFiles.append(mediaFile)
        }
        
        let startTime = CFAbsoluteTimeGetCurrent()
        try await repository.insertMediaFiles(mediaFiles)
        let insertTime = CFAbsoluteTimeGetCurrent() - startTime
        
        // 插入1000条记录应该在合理时间内完成（比如3秒）
        XCTAssertLessThan(insertTime, 3.0, "批量插入性能不符合预期")
        
        // 测试查询性能
        let queryStartTime = CFAbsoluteTimeGetCurrent()
        let paginatedResult = try await repository.getMediaFilesPaginated(
            folderId: folderId,
            pageSize: 50,
            page: 0
        )
        let queryTime = CFAbsoluteTimeGetCurrent() - queryStartTime
        
        XCTAssertEqual(paginatedResult.totalCount, 1000)
        XCTAssertEqual(paginatedResult.files.count, 50)
        XCTAssertLessThan(queryTime, 1.0, "分页查询性能不符合预期")
        
        // 测试搜索性能
        let searchStartTime = CFAbsoluteTimeGetCurrent()
        let searchResults = try await repository.searchMediaFiles(
            query: "perf_test",
            limit: 100
        )
        let searchTime = CFAbsoluteTimeGetCurrent() - searchStartTime
        
        XCTAssertGreaterThan(searchResults.count, 0)
        XCTAssertLessThan(searchTime, 1.0, "搜索性能不符合预期")
    }
    
    func testCachePerformance() async throws {
        let mediaFileIds = (0..<100).map { _ in UUID() }
        let testData = Data(count: 1024) // 1KB per item
        
        // 测试缓存写入性能
        let writeStartTime = CFAbsoluteTimeGetCurrent()
        
        for mediaFileId in mediaFileIds {
            try await cache.cacheData(
                testData,
                for: mediaFileId,
                type: .metadata,
                size: .small,
                strategy: .memoryOnly
            )
        }
        
        let writeTime = CFAbsoluteTimeGetCurrent() - writeStartTime
        XCTAssertLessThan(writeTime, 1.0, "缓存写入性能不符合预期")
        
        // 测试缓存读取性能
        let readStartTime = CFAbsoluteTimeGetCurrent()
        
        for mediaFileId in mediaFileIds {
            let cachedData = await cache.getCachedData(
                for: mediaFileId,
                type: .metadata,
                size: .small,
                strategy: .memoryOnly
            )
            XCTAssertNotNil(cachedData)
        }
        
        let readTime = CFAbsoluteTimeGetCurrent() - readStartTime
        XCTAssertLessThan(readTime, 0.5, "缓存读取性能不符合预期")
    }
    
    // MARK: - 错误处理测试
    func testErrorHandling() async throws {
        // 测试无效数据插入
        let invalidFolder = DatabaseFolder(
            id: nil,
            name: "",
            path: "",
            parentId: nil,
            createdAt: Date(),
            updatedAt: Date(),
            mediaCount: 0,
            totalSize: 0,
            fileCount: 0,
            lastModified: Date(),
            thumbnailPath: nil
        )
        
        do {
            _ = try await repository.insertFolder(invalidFolder)
            XCTFail("应该抛出错误")
        } catch {
            // 预期的错误
        }
        
        // 测试不存在的记录查询
        let nonExistentFolder = try await repository.getFolder(id: 99999)
        XCTAssertNil(nonExistentFolder)
        
        let nonExistentFile = try await repository.getMediaFile(id: "non-existent-id")
        XCTAssertNil(nonExistentFile)
    }
} 