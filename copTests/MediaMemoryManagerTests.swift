import XCTest
@testable import cop

// MARK: - 媒体内存管理器测试（第二阶段性能优化）
@MainActor
final class MediaMemoryManagerTests: XCTestCase {
    
    var memoryManager: MediaMemoryManager!
    
    override func setUp() async throws {
        try await super.setUp()
        memoryManager = MediaMemoryManager.shared
    }
    
    override func tearDown() async throws {
        memoryManager = nil
        try await super.tearDown()
    }
    
    // MARK: - 基础功能测试
    
    func testMemoryManagerInitialization() async {
        // 测试内存管理器初始化
        XCTAssertNotNil(memoryManager)
        XCTAssertEqual(memoryManager.memoryPressureLevel, .normal)
        XCTAssertFalse(memoryManager.isOptimizing)
    }
    
    func testMemoryStatisticsUpdate() async {
        // 测试内存统计信息更新
        let initialStats = memoryManager.memoryStatistics
        
        let stats = await memoryManager.getMemoryStatistics()
        
        XCTAssertGreaterThanOrEqual(stats.totalUsage, 0)
        XCTAssertGreaterThanOrEqual(stats.cacheUsage, 0)
        XCTAssertGreaterThanOrEqual(stats.thumbnailUsage, 0)
    }
    
    func testOptimizeForMediaViewing() async {
        // 测试媒体查看优化
        memoryManager.optimizeForMediaViewing()
        
        // 验证优化配置已应用
        // 这里主要测试方法执行不抛出异常
        XCTAssertTrue(true)
    }
    
    // MARK: - 内存清理测试
    
    func testPredictiveMemoryCleanup() async {
        // 测试预测性内存清理
        let initialOptimizing = memoryManager.isOptimizing
        
        await memoryManager.predictiveMemoryCleanup()
        
        XCTAssertEqual(memoryManager.isOptimizing, initialOptimizing)
    }
    
    func testSmartResourceRelease() async {
        // 测试智能资源释放
        await memoryManager.smartResourceRelease()
        
        // 验证资源释放完成
        XCTAssertTrue(true)
    }
    
    func testMemoryPressureHandling() async {
        // 测试内存压力处理
        await memoryManager.handleMemoryPressure()
        
        // 验证压力处理完成
        XCTAssertTrue(true)
    }
    
    // MARK: - 性能基准测试
    
    func testMemoryCleanupPerformance() async {
        // 测试内存清理性能
        measure {
            Task {
                await memoryManager.smartResourceRelease()
            }
        }
    }
    
    func testMemoryStatisticsPerformance() async {
        // 测试内存统计性能
        measure {
            Task {
                _ = await memoryManager.getMemoryStatistics()
            }
        }
    }
    
    // MARK: - 压力测试
    
    func testConcurrentMemoryOperations() async {
        // 测试并发内存操作
        await withTaskGroup(of: Void.self) { group in
            for _ in 0..<10 {
                group.addTask {
                    await self.memoryManager.predictiveMemoryCleanup()
                }
            }
        }
        
        XCTAssertTrue(true)
    }
    
    func testMemoryPressureLevels() async {
        // 测试不同内存压力级别
        let levels: [MemoryPressureLevel] = [.normal, .warning, .critical, .emergency]
        
        for level in levels {
            await memoryManager.handleMemoryPressure()
            // 验证每个级别都能正确处理
            XCTAssertTrue(true)
        }
    }
    
    // MARK: - 边界条件测试
    
    func testMemoryManagerWithNilCaches() async {
        // 测试缓存管理器为nil的情况
        await memoryManager.predictiveMemoryCleanup()
        await memoryManager.smartResourceRelease()
        
        // 验证操作不会崩溃
        XCTAssertTrue(true)
    }
    
    func testMemoryStatisticsEdgeCases() async {
        // 测试内存统计的边界情况
        let stats = await memoryManager.getMemoryStatistics()
        
        XCTAssertGreaterThanOrEqual(stats.totalUsage, 0)
        XCTAssertNotNil(stats.formattedTotalUsage)
        XCTAssertNotNil(stats.formattedCacheUsage)
        XCTAssertNotNil(stats.formattedThumbnailUsage)
    }
}

// MARK: - 内存压力级别测试
extension MediaMemoryManagerTests {
    
    func testMemoryPressureLevelDescription() {
        XCTAssertEqual(MemoryPressureLevel.normal.description, "正常")
        XCTAssertEqual(MemoryPressureLevel.warning.description, "警告")
        XCTAssertEqual(MemoryPressureLevel.critical.description, "危险")
        XCTAssertEqual(MemoryPressureLevel.emergency.description, "紧急")
    }
    
    func testMemoryPressureLevelColors() {
        XCTAssertEqual(MemoryPressureLevel.normal.color, "green")
        XCTAssertEqual(MemoryPressureLevel.warning.color, "yellow")
        XCTAssertEqual(MemoryPressureLevel.critical.color, "orange")
        XCTAssertEqual(MemoryPressureLevel.emergency.color, "red")
    }
}

// MARK: - 内存统计测试
extension MediaMemoryManagerTests {
    
    func testMemoryStatisticsFormatting() {
        let stats = MemoryStatistics(
            totalUsage: 100 * 1024 * 1024,  // 100MB
            cacheUsage: 50 * 1024 * 1024,   // 50MB
            thumbnailUsage: 25 * 1024 * 1024, // 25MB
            pressureLevel: .normal,
            optimizationCount: 5,
            lastOptimizationTime: Date()
        )
        
        XCTAssertTrue(stats.formattedTotalUsage.contains("100"))
        XCTAssertTrue(stats.formattedCacheUsage.contains("50"))
        XCTAssertTrue(stats.formattedThumbnailUsage.contains("25"))
        XCTAssertEqual(stats.optimizationCount, 5)
        XCTAssertNotNil(stats.lastOptimizationTime)
    }
}

// MARK: - 第二阶段优化功能测试
extension MediaMemoryManagerTests {
    
    /// 测试内存压力预测算法
    func testMemoryPressurePrediction() async throws {
        let memoryManager = MediaMemoryManager.shared
        
        // 模拟内存使用历史数据
        for i in 0..<20 {
            let mockUsage = UInt64(100 * 1024 * 1024 + i * 10 * 1024 * 1024) // 递增内存使用
            memoryManager.predictionModel.addDataPoint(mockUsage)
        }
        
        // 测试预测功能
        let predictedUsage = memoryManager.predictionModel.predictUsageIn(seconds: 60)
        XCTAssertGreaterThan(predictedUsage, 100 * 1024 * 1024, "预测的内存使用应该大于基础值")
        
        // 测试压力概率计算
        let pressureProbability = memoryManager.predictionModel.getPressureProbability(
            threshold: 300 * 1024 * 1024,
            timeWindow: 120
        )
        XCTAssertGreaterThanOrEqual(pressureProbability, 0.0, "压力概率应该非负")
        XCTAssertLessThanOrEqual(pressureProbability, 1.0, "压力概率应该不超过1.0")
    }
    
    /// 测试WebView内存管理集成
    func testWebViewMemoryIntegration() async throws {
        let memoryManager = MediaMemoryManager.shared
        let browserManager = BrowserManager.shared
        
        // 注册WebView管理器
        memoryManager.registerWebViewManager(browserManager)
        
        // 验证集成状态
        XCTAssertNotNil(memoryManager.webViewManager, "WebView管理器应该成功注册")
        
        // 测试WebView内存监控
        let webViewUsage = await browserManager.getDetailedMemoryUsage()
        XCTAssertGreaterThanOrEqual(webViewUsage.totalMemory, 0, "WebView内存使用应该非负")
        
        // 测试WebView优化
        await browserManager.optimizeMemory()
        
        // 验证优化后的内存使用
        let optimizedUsage = await browserManager.getDetailedMemoryUsage()
        XCTAssertLessThanOrEqual(optimizedUsage.totalMemory, webViewUsage.totalMemory + 50 * 1024 * 1024, 
                                "优化后内存使用不应显著增加")
    }
    
    /// 测试协调式内存清理
    func testCoordinatedMemoryCleanup() async throws {
        let memoryManager = MediaMemoryManager.shared
        
        // 记录清理前的内存使用
        let initialMemoryUsage = memoryManager.currentMemoryUsage
        
        // 执行协调式清理
        await memoryManager.coordinatedMemoryCleanup()
        
        // 验证清理效果
        let finalMemoryUsage = memoryManager.currentMemoryUsage
        
        // 内存使用应该减少或保持稳定
        XCTAssertLessThanOrEqual(finalMemoryUsage, initialMemoryUsage + 10 * 1024 * 1024,
                                "协调清理后内存使用不应显著增加")
    }
    
    /// 测试精确缓存清理策略
    func testPreciseCacheCleanup() async throws {
        guard let thumbnailCache = createMockThumbnailCache() else {
            XCTFail("无法创建模拟缓存")
            return
        }
        
        // 添加测试数据
        await populateTestCacheData(thumbnailCache)
        
        let initialCacheSize = await thumbnailCache.getCurrentSize()
        
        // 测试优先级清理
        await thumbnailCache.preciseCleanup(strategy: .priority(.low))
        let afterPriorityCleanup = await thumbnailCache.getCurrentSize()
        XCTAssertLessThan(afterPriorityCleanup, initialCacheSize, "优先级清理应该减少缓存大小")
        
        // 测试预测性清理
        let targetMemory: UInt64 = 50 * 1024 * 1024 // 50MB
        await thumbnailCache.preciseCleanup(strategy: .predictive(targetMemory))
        let finalCacheSize = await thumbnailCache.getCurrentSize()
        
        // 缓存应该进一步减少
        XCTAssertLessThanOrEqual(finalCacheSize, afterPriorityCleanup, "预测性清理应该进一步优化缓存")
    }
    
    /// 测试内存分布分析
    func testMemoryDistributionAnalysis() async throws {
        let memoryManager = MediaMemoryManager.shared
        
        // 模拟各种内存使用
        await simulateMemoryUsage(memoryManager)
        
        // 分析内存分布
        let distribution = await memoryManager.analyzeMemoryDistribution()
        
        // 验证分布数据
        XCTAssertGreaterThan(distribution.totalUsage, 0, "总内存使用应该大于0")
        XCTAssertEqual(
            distribution.totalUsage,
            distribution.cacheUsage + distribution.thumbnailUsage + distribution.webViewUsage + distribution.systemUsage,
            "内存分布各部分之和应该等于总使用量"
        )
        
        // 验证百分比计算
        let totalPercentage = distribution.cachePercentage + distribution.thumbnailPercentage + 
                              distribution.webViewPercentage + distribution.systemPercentage
        XCTAssertEqual(totalPercentage, 1.0, accuracy: 0.01, "所有百分比之和应该等于1.0")
    }
    
    /// 测试缓存分析功能
    func testCacheAnalysis() async throws {
        guard let thumbnailCache = createMockThumbnailCache() else {
            XCTFail("无法创建模拟缓存")
            return
        }
        
        // 添加多样化的测试数据
        await populateDiversifiedCacheData(thumbnailCache)
        
        // 获取缓存分析
        let analysis = await thumbnailCache.getCacheAnalysis()
        
        // 验证分析结果
        XCTAssertGreaterThan(analysis.totalItems, 0, "应该有缓存项目")
        XCTAssertGreaterThan(analysis.totalMemoryUsage, 0, "应该有内存使用")
        XCTAssertGreaterThan(analysis.efficiency, 0, "缓存效率应该大于0")
        
        // 验证分布数据
        XCTAssertFalse(analysis.sizeDistribution.isEmpty, "应该有大小分布数据")
        XCTAssertFalse(analysis.typeDistribution.isEmpty, "应该有类型分布数据")
    }
    
    /// 测试预测性缓存优化
    func testPredictiveCacheOptimization() async throws {
        guard let thumbnailCache = createMockThumbnailCache() else {
            XCTFail("无法创建模拟缓存")
            return
        }
        
        // 填充大量测试数据
        await populateTestCacheData(thumbnailCache)
        
        let initialSize = await thumbnailCache.getCurrentSize()
        
        // 执行预测性优化
        await thumbnailCache.predictiveOptimization()
        
        let optimizedSize = await thumbnailCache.getCurrentSize()
        
        // 验证优化效果
        XCTAssertLessThanOrEqual(optimizedSize, initialSize, "预测性优化应该不增加缓存大小")
        
        // 获取优化后的分析
        let analysis = await thumbnailCache.getCacheAnalysis()
        XCTAssertGreaterThan(analysis.efficiency, 0, "优化后的缓存效率应该保持正数")
    }
    
    // MARK: - 辅助方法
    
    private func createMockThumbnailCache() -> ThumbnailMemoryCache? {
        // 创建模拟的缩略图缓存
        return ThumbnailMemoryCache()
    }
    
    private func populateTestCacheData(_ cache: ThumbnailMemoryCache) async {
        // 填充测试缓存数据
        for i in 0..<20 {
            let mockFileID = UUID()
            let mockImage = createMockImage(size: CGSize(width: 300, height: 300))
            await cache.store(mockImage, for: mockFileID, size: .medium)
        }
    }
    
    private func populateDiversifiedCacheData(_ cache: ThumbnailMemoryCache) async {
        // 填充多样化的测试数据
        let sizes: [OptimizedThumbnailManager.ThumbnailSize] = [.small, .medium, .large]
        
        for size in sizes {
            for i in 0..<10 {
                let mockFileID = UUID()
                let mockImage = createMockImage(size: size.cgSize)
                await cache.store(mockImage, for: mockFileID, size: size)
            }
        }
    }
    
    private func createMockImage(size: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            UIColor.blue.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
    }
    
    private func simulateMemoryUsage(_ memoryManager: MediaMemoryManager) async {
        // 模拟各种内存使用情况
        // 这里可以添加具体的模拟逻辑
    }
    
    /// 测试性能监控集成
    func testPerformanceMonitoringIntegration() async throws {
        let memoryManager = MediaMemoryManager.shared
        
        // 获取初始统计信息
        let initialStats = await memoryManager.getMemoryStatistics()
        
        // 执行一些内存操作
        await memoryManager.predictiveMemoryCleanup()
        
        // 获取更新后的统计信息
        let updatedStats = await memoryManager.getMemoryStatistics()
        
        // 验证统计信息更新
        XCTAssertGreaterThanOrEqual(updatedStats.optimizationCount, initialStats.optimizationCount,
                                    "优化计数应该增加或保持不变")
        
        if let lastOptimization = updatedStats.lastOptimizationTime {
            XCTAssertGreaterThan(lastOptimization, Date().addingTimeInterval(-60),
                                "最后优化时间应该在最近一分钟内")
        }
    }
} 